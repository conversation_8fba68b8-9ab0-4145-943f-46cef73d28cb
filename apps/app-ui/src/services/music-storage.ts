/**
 * 音乐存储服务
 * 处理在线音乐缓存和本地音乐导入
 */
import { Filesystem, Directory } from '@capacitor/filesystem'
import { Capacitor } from '@capacitor/core'
import { FileSystemDownloadService, type FileSystemResourceFile } from './filesystem-download'
import type { MusicItem } from '@/types/music'

export interface StoredMusicItem extends MusicItem {
  filePath?: string // 本地文件路径
  webPath?: string // Web可访问路径
  fileSize?: number // 文件大小
  isLocal: boolean // 是否为本地文件
}

export class MusicStorageService {
  private static readonly MUSIC_SCRIPT_ID = 'music-player' // 统一的脚本ID用于音乐存储
  private static updateListeners: ((music: StoredMusicItem) => void)[] = [] // 更新监听器

  /**
   * 缓存在线音乐到本地
   */
  static async cacheOnlineMusic(
    music: MusicItem,
    onProgress?: (loaded: number, total: number) => void
  ): Promise<StoredMusicItem> {
    try {
      console.log(`📥 开始缓存在线音乐: ${music.title}`)

      // 使用现有的文件系统下载服务
      const resourceFile = await FileSystemDownloadService.downloadFileToFileSystem(
        music.url,
        this.MUSIC_SCRIPT_ID,
        onProgress
      )

      const storedMusic: StoredMusicItem = {
        ...music,
        filePath: resourceFile.filePath,
        webPath: resourceFile.webPath,
        fileSize: resourceFile.size,
        isLocal: true,
        // 更新URL为本地路径，这样后续播放使用本地文件
        url: resourceFile.webPath || music.url
      }

      // 保存缓存记录
      this.saveCachedMusicRecord(storedMusic)

      console.log(`✅ 在线音乐缓存完成: ${music.title}`)
      return storedMusic
    } catch (error) {
      console.error(`❌ 缓存在线音乐失败: ${music.title}`, error)
      // 缓存失败时返回原始音乐信息
      return {
        ...music,
        isLocal: false
      }
    }
  }

  /**
   * 检查在线音乐是否已缓存
   */
  static async isMusicCached(musicUrl: string): Promise<StoredMusicItem | null> {
    try {
      // 使用FileSystemDownloadService检查文件是否已缓存
      const fileName = this.generateFileNameFromUrl(musicUrl)

      if (!Capacitor.isNativePlatform()) {
        // Web环境：检查localStorage中是否有对应的blob URL
        const cachedMusic = JSON.parse(localStorage.getItem('cached_music') || '[]')
        const found = cachedMusic.find((music: StoredMusicItem) => music.url === musicUrl)
        if (found && found.webPath) {
          // 验证blob URL是否仍然有效
          try {
            const response = await fetch(found.webPath)
            if (response.ok) {
              return found
            }
          } catch {
            // blob URL已失效，从缓存中移除
            const updatedCache = cachedMusic.filter(
              (music: StoredMusicItem) => music.url !== musicUrl
            )
            localStorage.setItem('cached_music', JSON.stringify(updatedCache))
          }
        }
        return null
      } else {
        // 原生环境：检查文件系统中是否存在文件
        try {
          const stat = await Filesystem.stat({
            path: `music/${fileName}`,
            directory: Directory.Data
          })

          if (stat.size > 0) {
            // 文件存在，构造StoredMusicItem
            const uri = await Filesystem.getUri({
              directory: Directory.Data,
              path: `music/${fileName}`
            })

            return {
              id: `cached-${Date.now()}`,
              title: fileName.replace(/\.[^/.]+$/, ''), // 移除扩展名
              url: musicUrl,
              filePath: `music/${fileName}`,
              webPath: uri.uri,
              isLocal: false,
              isOnline: true,
              createdAt: new Date(stat.mtime || Date.now())
            }
          }
        } catch {
          // 文件不存在
        }
        return null
      }
    } catch (error) {
      console.error('检查音乐缓存状态失败:', error)
      return null
    }
  }

  /**
   * 从URL生成文件名
   */
  private static generateFileNameFromUrl(url: string): string {
    const urlObj = new URL(url)
    const pathName = urlObj.pathname
    const fileName = pathName.split('/').pop() || 'music'

    // 确保有扩展名
    if (!fileName.includes('.')) {
      return `${fileName}.mp3`
    }

    return fileName
  }

  /**
   * 保存缓存音乐记录
   */
  private static saveCachedMusicRecord(music: StoredMusicItem): void {
    try {
      if (!Capacitor.isNativePlatform()) {
        // Web环境：保存到localStorage
        const cachedMusic = JSON.parse(localStorage.getItem('cached_music') || '[]')
        const existingIndex = cachedMusic.findIndex((m: StoredMusicItem) => m.url === music.url)

        if (existingIndex >= 0) {
          cachedMusic[existingIndex] = music
        } else {
          cachedMusic.push(music)
        }

        localStorage.setItem('cached_music', JSON.stringify(cachedMusic))
        console.log('💾 缓存音乐记录已保存到localStorage')
      } else {
        // 原生环境：这里可以保存到SQLite数据库或其他持久化存储
        console.log('💾 缓存音乐记录已保存（原生环境）')
      }
    } catch (error) {
      console.error('保存缓存音乐记录失败:', error)
    }
  }

  /**
   * 导入本地音乐文件
   */
  static async importLocalMusic(
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<StoredMusicItem> {
    try {
      console.log(`📁 开始导入本地音乐: ${file.name}`)
      onProgress?.(10)

      // 生成唯一ID
      const musicId = `imported_music_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

      let musicUrl: string
      let filePath: string | undefined
      let webPath: string | undefined

      if (!Capacitor.isNativePlatform()) {
        // Web环境：使用Blob URL
        console.log(`🌐 Web环境使用Blob URL`)
        musicUrl = URL.createObjectURL(file)
        onProgress?.(80)
      } else {
        // 原生环境：将文件保存到文件系统
        console.log(`📱 原生环境保存文件到文件系统`)
        onProgress?.(20)

        // 生成文件名
        const fileName = `imported_music_${musicId}.${this.getFileExtension(file.name)}`
        filePath = `imported_music/${fileName}`

        try {
          // 确保目录存在
          await Filesystem.mkdir({
            path: 'imported_music',
            directory: Directory.Data,
            recursive: true
          })
          console.log(`📂 创建目录: imported_music`)
          onProgress?.(30)

          // 将文件转换为ArrayBuffer
          const arrayBuffer = await file.arrayBuffer()
          const uint8Array = new Uint8Array(arrayBuffer)
          onProgress?.(50)

          // 使用分块方式转换为Base64（避免内存溢出）
          const base64Data = this.arrayBufferToBase64(uint8Array)
          onProgress?.(70)

          // 保存文件到文件系统
          await Filesystem.writeFile({
            path: filePath,
            data: base64Data,
            directory: Directory.Data
          })
          console.log(`💾 文件保存成功: ${filePath}`)
          onProgress?.(80)

          // 获取Web可访问的路径
          webPath = Capacitor.convertFileSrc(
            await Filesystem.getUri({
              directory: Directory.Data,
              path: filePath
            }).then(result => result.uri)
          )

          musicUrl = webPath
          console.log(`🔗 生成Web路径: ${webPath}`)
        } catch (fsError) {
          console.error('文件系统保存失败，回退到Blob URL:', fsError)
          // 如果文件系统保存失败，回退到Blob URL
          musicUrl = URL.createObjectURL(file)
        }
      }

      onProgress?.(90) // 创建音乐对象

      // 创建音乐对象
      const storedMusic: StoredMusicItem = {
        id: musicId,
        title: this.extractTitleFromFileName(file.name),
        artist: '未知艺术家',
        duration: 0, // 先设为0，后台异步获取真实时长
        url: musicUrl,
        isOnline: false,
        createdAt: new Date(),
        fileSize: file.size,
        isLocal: true,
        filePath, // 原生环境的文件路径
        webPath // 原生环境的Web访问路径
      }

      // 保存记录到存储
      await this.saveImportedMusic(storedMusic)

      onProgress?.(100) // 导入完成
      console.log(`✅ 音乐导入完成: ${file.name}`)

      // 后台异步获取音乐时长，不阻塞返回
      this.updateMusicDurationInBackground(storedMusic)

      return storedMusic
    } catch (error) {
      console.error(`❌ 导入本地音乐失败: ${file.name}`, error)
      throw error
    }
  }

  /**
   * 保存导入的音乐（统一入口）
   */
  static async saveImportedMusic(music: StoredMusicItem): Promise<void> {
    try {
      if (!Capacitor.isNativePlatform()) {
        // Web环境：保存到localStorage
        this.saveImportedMusicRecord(music, null)
      } else {
        // 原生环境：保存到数据库
        await this.saveImportedMusicToDatabase(music)
      }
      console.log(`💾 音乐保存成功: ${music.title}`)
    } catch (error) {
      console.error(`❌ 音乐保存失败: ${music.title}`, error)
      throw error
    }
  }

  /**
   * 获取所有导入的音乐
   */
  static async getAllImportedMusic(): Promise<StoredMusicItem[]> {
    try {
      if (!Capacitor.isNativePlatform()) {
        // Web环境：从localStorage读取
        return this.getImportedMusicFromLocalStorage()
      } else {
        // 原生环境：从数据库读取
        return await this.getImportedMusicFromDatabase()
      }
    } catch (error) {
      console.error('获取导入音乐失败:', error)
      return []
    }
  }

  /**
   * 删除导入的音乐
   */
  static async deleteImportedMusic(musicId: string): Promise<void> {
    try {
      console.log(`🗑️ 开始删除音乐: ${musicId}`)

      if (!Capacitor.isNativePlatform()) {
        // Web环境：从localStorage删除
        this.deleteImportedMusicFromLocalStorage(musicId)
      } else {
        // 原生环境：从数据库和文件系统删除
        await this.deleteImportedMusicFromDatabase(musicId)
      }

      console.log(`✅ 音乐删除成功: ${musicId}`)
    } catch (error) {
      console.error(`❌ 删除音乐失败: ${musicId}`, error)
      throw error
    }
  }

  /**
   * Web环境：保存音乐记录到localStorage
   */
  private static saveImportedMusicRecord(music: StoredMusicItem, blob: Blob | null): void {
    try {
      const existingMusic = JSON.parse(localStorage.getItem('imported_music') || '[]')
      const updatedMusic = existingMusic.filter((m: StoredMusicItem) => m.id !== music.id)
      updatedMusic.push(music)
      localStorage.setItem('imported_music', JSON.stringify(updatedMusic))
    } catch (error) {
      console.error('保存音乐记录到localStorage失败:', error)
      throw error
    }
  }

  /**
   * Web环境：从localStorage获取导入的音乐
   */
  private static getImportedMusicFromLocalStorage(): StoredMusicItem[] {
    try {
      const stored = localStorage.getItem('imported_music')
      if (!stored) return []

      const musicList: StoredMusicItem[] = JSON.parse(stored)
      return this.validateImportedMusic(musicList)
    } catch (error) {
      console.error('从localStorage读取音乐失败:', error)
      return []
    }
  }

  /**
   * Web环境：从localStorage删除音乐
   */
  private static deleteImportedMusicFromLocalStorage(musicId: string): void {
    try {
      const existingMusic = JSON.parse(localStorage.getItem('imported_music') || '[]')
      const music = existingMusic.find((m: StoredMusicItem) => m.id === musicId)

      if (music && music.url && music.url.startsWith('blob:')) {
        // 释放Blob URL
        URL.revokeObjectURL(music.url)
        console.log(`🔗 释放Blob URL: ${music.url}`)
      }

      const filteredMusic = existingMusic.filter((m: StoredMusicItem) => m.id !== musicId)
      localStorage.setItem('imported_music', JSON.stringify(filteredMusic))
    } catch (error) {
      console.error('从localStorage删除音乐失败:', error)
      throw error
    }
  }

  /**
   * 原生环境：保存音乐到数据库
   */
  private static async saveImportedMusicToDatabase(music: StoredMusicItem): Promise<void> {
    // 这里应该实现数据库保存逻辑
    // 暂时使用localStorage作为fallback
    this.saveImportedMusicRecord(music, null)
  }

  /**
   * 原生环境：从数据库获取音乐
   */
  private static async getImportedMusicFromDatabase(): Promise<StoredMusicItem[]> {
    // 这里应该实现数据库读取逻辑
    // 暂时使用localStorage作为fallback
    return this.getImportedMusicFromLocalStorage()
  }

  /**
   * 原生环境：从数据库删除音乐
   */
  private static async deleteImportedMusicFromDatabase(musicId: string): Promise<void> {
    try {
      // 先获取音乐信息
      const musicList = await this.getImportedMusicFromDatabase()
      const music = musicList.find(m => m.id === musicId)

      if (music && music.filePath) {
        // 删除文件系统中的文件
        try {
          await Filesystem.deleteFile({
            path: music.filePath,
            directory: Directory.Data
          })
          console.log(`🗑️ 删除文件: ${music.filePath}`)
        } catch (error) {
          console.warn('删除文件失败:', error)
        }
      }

      // 从数据库删除记录（暂时使用localStorage）
      this.deleteImportedMusicFromLocalStorage(musicId)
    } catch (error) {
      console.error('从数据库删除音乐失败:', error)
      throw error
    }
  }

  /**
   * 验证导入的音乐列表
   */
  private static validateImportedMusic(musicList: StoredMusicItem[]): StoredMusicItem[] {
    return musicList.filter(music => {
      // 基本验证
      if (!music.id || !music.title || !music.url) {
        console.warn('无效的音乐记录:', music)
        return false
      }

      // Web环境下验证Blob URL是否仍然有效
      if (!Capacitor.isNativePlatform() && music.url.startsWith('blob:')) {
        // 注意：无法直接验证Blob URL是否有效，这里假设都有效
        // 实际使用中如果Blob URL失效，播放时会出错
      }

      return true
    })
  }

  /**
   * 从文件名提取标题
   */
  private static extractTitleFromFileName(fileName: string): string {
    // 移除文件扩展名
    const nameWithoutExt = fileName.replace(/\.[^/.]+$/, '')

    // 清理常见的文件名模式
    return nameWithoutExt
      .replace(/^\d+[\s\-\.]*/, '') // 移除开头的数字
      .replace(/[\[\(].*?[\]\)]/g, '') // 移除括号内容
      .replace(/\s+/g, ' ') // 规范化空格
      .trim()
  }

  /**
   * 获取文件扩展名
   */
  private static getFileExtension(fileName: string): string {
    const lastDot = fileName.lastIndexOf('.')
    return lastDot > 0 ? fileName.substring(lastDot + 1).toLowerCase() : 'mp3'
  }

  /**
   * ArrayBuffer转Base64（分块处理避免内存溢出）
   */
  private static arrayBufferToBase64(buffer: Uint8Array): string {
    const chunkSize = 8192
    let binary = ''

    for (let i = 0; i < buffer.length; i += chunkSize) {
      const chunk = buffer.subarray(i, i + chunkSize)
      binary += String.fromCharCode.apply(null, Array.from(chunk))
    }

    return btoa(binary)
  }

  /**
   * 后台异步获取音乐时长
   */
  private static updateMusicDurationInBackground(music: StoredMusicItem): void {
    const audio = new Audio()

    const handleLoadedMetadata = () => {
      const duration = audio.duration
      if (duration && isFinite(duration)) {
        // 更新音乐时长
        const updatedMusic: StoredMusicItem = {
          ...music,
          duration
        }

        // 保存更新后的音乐信息
        this.saveImportedMusic(updatedMusic).catch(error => {
          console.error('更新音乐时长失败:', error)
        })

        // 通知监听器
        this.notifyUpdateListeners(updatedMusic)

        console.log(`⏱️ 音乐时长更新: ${music.title} -> ${duration}秒`)
      }

      // 清理
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata)
      audio.removeEventListener('error', handleError)
      audio.src = ''
    }

    const handleError = () => {
      console.warn(`⚠️ 无法获取音乐时长: ${music.title}`)
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata)
      audio.removeEventListener('error', handleError)
      audio.src = ''
    }

    audio.addEventListener('loadedmetadata', handleLoadedMetadata)
    audio.addEventListener('error', handleError)
    audio.src = music.url
  }

  /**
   * 添加更新监听器
   */
  static addUpdateListener(listener: (music: StoredMusicItem) => void): void {
    this.updateListeners.push(listener)
  }

  /**
   * 移除更新监听器
   */
  static removeUpdateListener(listener: (music: StoredMusicItem) => void): void {
    const index = this.updateListeners.indexOf(listener)
    if (index > -1) {
      this.updateListeners.splice(index, 1)
    }
  }

  /**
   * 通知所有监听器
   */
  private static notifyUpdateListeners(music: StoredMusicItem): void {
    this.updateListeners.forEach(listener => {
      try {
        listener(music)
      } catch (error) {
        console.error('通知音乐更新监听器失败:', error)
      }
    })
  }
}
