import type { AudioVisualizationData } from '@/types/music'

export interface AudioAnalysisOptions {
  fftSize?: number // FFT大小，影响频谱分析精度
  smoothingTimeConstant?: number // 平滑时间常数
  minDecibels?: number // 最小分贝值
  maxDecibels?: number // 最大分贝值
}

/**
 * 音频分析服务
 * 使用 Web Audio API 进行真实的音频频谱分析
 */
export class AudioAnalysisService {
  private audioContext: AudioContext | null = null
  private analyser: AnalyserNode | null = null
  private source: MediaElementAudioSourceNode | null = null
  private frequencyData: Uint8Array | null = null
  private timeDomainData: Uint8Array | null = null
  private isConnected = false
  private lastDebugTime = 0

  constructor(private options: AudioAnalysisOptions = {}) {
    this.options = {
      fftSize: 256, // 默认FFT大小，产生128个频率数据点
      smoothingTimeConstant: 0.8, // 平滑度
      minDecibels: -90,
      maxDecibels: -10,
      ...options
    }
  }

  /**
   * 连接音频元素进行分析
   */
  async connectAudioElement(audioElement: HTMLAudioElement): Promise<void> {
    try {
      // 如果已经连接了同一个元素，直接返回
      if (this.isConnected && this.source && this.source.mediaElement === audioElement) {
        return
      }

      // 断开之前的连接
      this.disconnect()

      // 创建音频上下文
      if (!this.audioContext) {
        this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
      }

      // 如果音频上下文被暂停，恢复它
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume()
      }

      // 创建音频源 - 注意：每个audio元素只能创建一次MediaElementAudioSourceNode
      try {
        this.source = this.audioContext.createMediaElementSource(audioElement)
      } catch (error) {
        // 如果元素已经连接到AudioContext，说明之前已经创建过source
        console.warn('音频元素可能已经连接到AudioContext:', error)
        throw new Error('音频元素已被其他AudioContext使用，请刷新页面重试')
      }

      // 创建分析器节点
      this.analyser = this.audioContext.createAnalyser()
      this.analyser.fftSize = this.options.fftSize!
      this.analyser.smoothingTimeConstant = this.options.smoothingTimeConstant!
      this.analyser.minDecibels = this.options.minDecibels!
      this.analyser.maxDecibels = this.options.maxDecibels!

      // 连接音频图：音频源 -> 分析器 -> 输出
      // 重要：必须连接到destination，否则没有声音！
      this.source.connect(this.analyser)
      this.analyser.connect(this.audioContext.destination)

      // 初始化数据数组
      const bufferLength = this.analyser.frequencyBinCount
      this.frequencyData = new Uint8Array(bufferLength)
      this.timeDomainData = new Uint8Array(bufferLength)

      this.isConnected = true
      console.log('🎵 音频分析器连接成功')
    } catch (error) {
      console.error('❌ 连接音频分析器失败:', error)
      throw new Error('音频分析器初始化失败')
    }
  }

  /**
   * 获取当前音频可视化数据
   */
  getVisualizationData(): AudioVisualizationData | null {
    if (!this.analyser || !this.frequencyData || !this.timeDomainData) {
      return null
    }

    try {
      // 获取频域数据（频谱）
      this.analyser.getByteFrequencyData(this.frequencyData)

      // 获取时域数据（波形）
      this.analyser.getByteTimeDomainData(this.timeDomainData)

      // 转换频谱数据为0-100范围
      const frequencies = Array.from(this.frequencyData).map(value => (value / 255) * 100)

      // 转换波形数据为-1到1范围
      const waveform = Array.from(this.timeDomainData).map(value => (value - 128) / 128)

      // 计算平均音量
      const volume = frequencies.reduce((sum, freq) => sum + freq, 0) / frequencies.length

      // 简单的节拍检测：检查低频能量的突然增加
      const lowFreqEnergy = frequencies.slice(0, 8).reduce((sum, freq) => sum + freq, 0) / 8
      const beat = lowFreqEnergy > 60 // 阈值可以调整

      // 调试信息：每5秒打印一次数据统计（减少频率）
      if (!this.lastDebugTime || Date.now() - this.lastDebugTime > 5000) {
        const maxFreq = Math.max(...this.frequencyData)
        const avgFreq =
          this.frequencyData.reduce((sum, val) => sum + val, 0) / this.frequencyData.length
        const maxWave = Math.max(...this.timeDomainData)
        const minWave = Math.min(...this.timeDomainData)
        const nonZeroFreqs = this.frequencyData.filter(val => val > 0).length

        console.log('🎵 音频分析数据:', {
          volume: volume.toFixed(1) + '%',
          maxFrequency: maxFreq,
          avgFrequency: avgFreq.toFixed(1),
          nonZeroFreqs: `${nonZeroFreqs}/${this.frequencyData.length}`,
          waveformRange: `${minWave}-${maxWave}`,
          lowFreqEnergy: lowFreqEnergy.toFixed(1),
          beat,
          contextState: this.audioContext?.state,
          connected: this.isConnected
        })
        this.lastDebugTime = Date.now()
      }

      return {
        frequencies,
        waveform,
        volume,
        beat
      }
    } catch (error) {
      console.error('❌ 获取音频数据失败:', error)
      return null
    }
  }

  /**
   * 计算音频强度（0-9）
   * 基于频谱分析结果
   */
  calculateIntensity(): number {
    const data = this.getVisualizationData()
    if (!data) return 0

    // 使用加权平均计算强度
    // 低频（0-32）权重更高，因为它们通常包含更多的能量信息
    const lowFreq = data.frequencies.slice(0, 32).reduce((sum, freq) => sum + freq, 0) / 32
    const midFreq = data.frequencies.slice(32, 64).reduce((sum, freq) => sum + freq, 0) / 32
    const highFreq =
      data.frequencies.slice(64).reduce((sum, freq) => sum + freq, 0) /
      (data.frequencies.length - 64)

    // 加权计算：低频权重50%，中频权重30%，高频权重20%
    const weightedIntensity = lowFreq * 0.5 + midFreq * 0.3 + highFreq * 0.2

    // 转换为0-9范围，并添加一些非线性变换使其更敏感
    let intensity = Math.pow(weightedIntensity / 100, 0.7) * 9

    // 确保在有效范围内
    intensity = Math.max(0, Math.min(9, intensity))

    return Math.round(intensity * 10) / 10 // 保留一位小数
  }

  /**
   * 断开音频分析器
   */
  disconnect(): void {
    try {
      if (this.source) {
        this.source.disconnect()
        this.source = null
      }

      if (this.analyser) {
        this.analyser.disconnect()
        this.analyser = null
      }

      this.isConnected = false
      console.log('🔌 音频分析器已断开')
    } catch (error) {
      console.error('❌ 断开音频分析器失败:', error)
    }
  }

  /**
   * 销毁音频分析器
   */
  destroy(): void {
    this.disconnect()

    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close()
      this.audioContext = null
    }

    this.frequencyData = null
    this.timeDomainData = null
  }

  /**
   * 检查是否已连接
   */
  get connected(): boolean {
    return this.isConnected
  }

  /**
   * 获取音频上下文状态
   */
  get contextState(): AudioContextState | null {
    return this.audioContext?.state || null
  }
}

// 创建单例实例
export const audioAnalysisService = new AudioAnalysisService()
