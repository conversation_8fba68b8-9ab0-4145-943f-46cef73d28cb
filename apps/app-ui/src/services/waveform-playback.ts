import type { TrackPoint, WaveformLibraryItem, WaveformPlaybackState } from '@/types/swipe-mode'
import { TrackCalculator } from '@/utils/swipe-mode/track-calculator'
import { useWaveformData } from '@/hooks/useWaveformData'

/**
 * 轨迹播放服务
 * 负责模拟真实滑动轨迹的播放和蓝牙广播
 */
export class WaveformPlaybackService {
  private static instance: WaveformPlaybackService
  private animationId: number | null = null
  private startTime: number = 0
  private pausedTime: number = 0
  private waveformData: ReturnType<typeof useWaveformData> | null = null

  // 播放状态
  private state: WaveformPlaybackState = {
    isPlaying: false,
    currentTime: 0,
    duration: 0,
    playbackSpeed: 1.0,
    currentIntensity: 0,
    progress: 0
  }

  // 事件监听器
  private listeners: {
    onStateChange: ((state: WaveformPlaybackState) => void)[]
    onTrackUpdate: ((point: TrackPoint, intensity: number) => void)[]
    onPlaybackComplete: (() => void)[]
  } = {
    onStateChange: [],
    onTrackUpdate: [],
    onPlaybackComplete: []
  }

  private constructor() {}

  static getInstance(): WaveformPlaybackService {
    if (!WaveformPlaybackService.instance) {
      WaveformPlaybackService.instance = new WaveformPlaybackService()
    }
    return WaveformPlaybackService.instance
  }

  /**
   * 初始化播放服务
   */
  initialize(waveformDataHook: ReturnType<typeof useWaveformData>) {
    this.waveformData = waveformDataHook
  }

  /**
   * 开始播放轨迹
   */
  async play(waveform: WaveformLibraryItem): Promise<void> {
    if (this.state.isPlaying) {
      this.pause()
    }

    // 初始化播放状态
    this.state = {
      isPlaying: true,
      currentTime: this.pausedTime,
      duration: waveform.duration,
      playbackSpeed: this.state.playbackSpeed,
      currentIntensity: 0,
      progress: this.pausedTime / waveform.duration
    }

    // 计算起始时间
    this.startTime = performance.now() - this.pausedTime

    // 开始播放循环
    this.startPlaybackLoop(waveform)

    // 通知状态变化
    this.notifyStateChange()
  }

  /**
   * 暂停播放
   */
  pause(): void {
    if (!this.state.isPlaying) return

    this.state.isPlaying = false
    this.pausedTime = this.state.currentTime

    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
      this.animationId = null
    }

    this.notifyStateChange()
  }

  /**
   * 恢复播放
   */
  resume(waveform: WaveformLibraryItem): void {
    if (this.state.isPlaying) return

    this.play(waveform)
  }

  /**
   * 停止播放
   */
  stop(): void {
    this.state.isPlaying = false
    this.pausedTime = 0

    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
      this.animationId = null
    }

    // 重置状态
    this.state = {
      isPlaying: false,
      currentTime: 0,
      duration: this.state.duration,
      playbackSpeed: this.state.playbackSpeed,
      currentIntensity: 0,
      progress: 0
    }

    // 清除波形数据
    if (this.waveformData) {
      this.waveformData.clearData()
    }

    this.notifyStateChange()
  }

  /**
   * 重新开始播放
   */
  restart(waveform: WaveformLibraryItem): void {
    this.stop()
    this.play(waveform)
  }

  /**
   * 设置播放速度
   */
  setPlaybackSpeed(speed: number): void {
    const wasPlaying = this.state.isPlaying
    const currentProgress = this.state.progress

    if (wasPlaying) {
      this.pause()
    }

    this.state.playbackSpeed = Math.max(0.25, Math.min(2.0, speed))

    // 根据新速度调整暂停时间
    this.pausedTime = currentProgress * this.state.duration

    this.notifyStateChange()
  }

  /**
   * 跳转到指定进度
   */
  seekTo(progress: number): void {
    const wasPlaying = this.state.isPlaying

    if (wasPlaying) {
      this.pause()
    }

    this.pausedTime = Math.max(0, Math.min(1, progress)) * this.state.duration
    this.state.currentTime = this.pausedTime
    this.state.progress = this.pausedTime / this.state.duration

    this.notifyStateChange()
  }

  /**
   * 获取当前播放状态
   */
  getState(): WaveformPlaybackState {
    return { ...this.state }
  }

  /**
   * 添加状态变化监听器
   */
  onStateChange(callback: (state: WaveformPlaybackState) => void): () => void {
    this.listeners.onStateChange.push(callback)
    return () => {
      const index = this.listeners.onStateChange.indexOf(callback)
      if (index > -1) {
        this.listeners.onStateChange.splice(index, 1)
      }
    }
  }

  /**
   * 添加轨迹更新监听器
   */
  onTrackUpdate(callback: (point: TrackPoint, intensity: number) => void): () => void {
    this.listeners.onTrackUpdate.push(callback)
    return () => {
      const index = this.listeners.onTrackUpdate.indexOf(callback)
      if (index > -1) {
        this.listeners.onTrackUpdate.splice(index, 1)
      }
    }
  }

  /**
   * 添加播放完成监听器
   */
  onPlaybackComplete(callback: () => void): () => void {
    this.listeners.onPlaybackComplete.push(callback)
    return () => {
      const index = this.listeners.onPlaybackComplete.indexOf(callback)
      if (index > -1) {
        this.listeners.onPlaybackComplete.splice(index, 1)
      }
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 开始播放循环
   */
  private startPlaybackLoop(waveform: WaveformLibraryItem): void {
    const playFrame = (timestamp: number) => {
      if (!this.state.isPlaying) return

      // 计算当前播放时间（考虑播放速度）
      const elapsed = (timestamp - this.startTime) * this.state.playbackSpeed
      this.state.currentTime = elapsed
      this.state.progress = Math.min(1, elapsed / waveform.duration)

      // 检查是否播放完成
      if (this.state.progress >= 1) {
        this.state.isPlaying = false
        this.state.currentTime = waveform.duration
        this.state.progress = 1
        this.state.currentIntensity = 0

        this.notifyStateChange()
        this.notifyPlaybackComplete()
        return
      }

      // 计算当前应该播放的轨迹点
      const currentPoint = this.getCurrentTrackPoint(waveform, elapsed)
      if (currentPoint) {
        // 计算当前强度
        const pointIndex = this.getPointIndex(waveform, elapsed)
        const speed = TrackCalculator.calculateSpeed(waveform.points, pointIndex)
        const intensity = TrackCalculator.calculateIntensity(speed)

        this.state.currentIntensity = intensity

        // 通知轨迹更新（用于绘制和蓝牙广播）
        this.notifyTrackUpdate(currentPoint, intensity)

        // 更新波形数据
        if (this.waveformData) {
          this.waveformData.addDataPoint(intensity)
        }
      }

      // 通知状态变化
      this.notifyStateChange()

      // 继续下一帧
      this.animationId = requestAnimationFrame(playFrame)
    }

    this.animationId = requestAnimationFrame(playFrame)
  }

  /**
   * 根据时间获取当前轨迹点
   */
  private getCurrentTrackPoint(
    waveform: WaveformLibraryItem,
    currentTime: number
  ): TrackPoint | null {
    if (waveform.points.length === 0) return null

    // 找到对应时间的轨迹点
    const startTime = waveform.points[0].timestamp
    const targetTime = startTime + currentTime

    // 使用二分查找找到最接近的点
    let left = 0
    let right = waveform.points.length - 1

    while (left < right) {
      const mid = Math.floor((left + right) / 2)
      if (waveform.points[mid].timestamp <= targetTime) {
        left = mid + 1
      } else {
        right = mid
      }
    }

    // 返回最接近的点，如果超出范围则返回最后一个点
    const index = Math.min(left, waveform.points.length - 1)
    return waveform.points[index]
  }

  /**
   * 根据时间获取轨迹点索引
   */
  private getPointIndex(waveform: WaveformLibraryItem, currentTime: number): number {
    if (waveform.points.length === 0) return 0

    const startTime = waveform.points[0].timestamp
    const targetTime = startTime + currentTime

    for (let i = 0; i < waveform.points.length; i++) {
      if (waveform.points[i].timestamp >= targetTime) {
        return Math.max(0, i - 1)
      }
    }

    return waveform.points.length - 1
  }

  /**
   * 通知状态变化
   */
  private notifyStateChange(): void {
    this.listeners.onStateChange.forEach(callback => {
      try {
        callback({ ...this.state })
      } catch (error) {
        console.error('Error in state change callback:', error)
      }
    })
  }

  /**
   * 通知轨迹更新
   */
  private notifyTrackUpdate(point: TrackPoint, intensity: number): void {
    this.listeners.onTrackUpdate.forEach(callback => {
      try {
        callback(point, intensity)
      } catch (error) {
        console.error('Error in track update callback:', error)
      }
    })
  }

  /**
   * 通知播放完成
   */
  private notifyPlaybackComplete(): void {
    this.listeners.onPlaybackComplete.forEach(callback => {
      try {
        callback()
      } catch (error) {
        console.error('Error in playback complete callback:', error)
      }
    })
  }
}

// 导出单例实例
export const waveformPlayback = WaveformPlaybackService.getInstance()

/**
 * React Hook for using waveform playback service
 */
export const useWaveformPlayback = () => {
  return waveformPlayback
}
