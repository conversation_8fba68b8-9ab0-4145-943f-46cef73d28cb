import type {
  Track,
  WaveformLibraryItem,
  WaveformLibraryFilter,
  WaveformLibraryResponse
} from '@/types/swipe-mode'
import { scriptDB, type WaveformData, type UserAction } from '@/lib/database'
import { useUserProfileStore } from '@/stores/user-profile-store'
import type { WaveformRecording } from '@/types/recording'

/**
 * 基于Dexie的波形数据存储服务
 * 提供本地Dexie存储和云端同步功能
 */
export class WaveformStorageDexieService {
  private static instance: WaveformStorageDexieService

  private constructor() {}

  static getInstance(): WaveformStorageDexieService {
    if (!WaveformStorageDexieService.instance) {
      WaveformStorageDexieService.instance = new WaveformStorageDexieService()
    }
    return WaveformStorageDexieService.instance
  }

  /**
   * 初始化数据库
   */
  async initialize(): Promise<void> {
    try {
      // 确保数据库已打开
      if (!scriptDB.isOpen()) {
        await scriptDB.open()
      }
      console.log('WaveformStorageDexieService initialized')
    } catch (error) {
      console.error('Failed to initialize database:', error)
      throw error
    }
  }

  /**
   * 保存录制数据到本地（新格式）
   */
  async saveRecording(
    recording: WaveformRecording,
    isPublic: boolean = false
  ): Promise<WaveformLibraryItem> {
    // 获取当前用户信息
    const userProfile = await this.getCurrentUserProfile()

    // 从录制数据中提取统计信息
    const [maxIntensity, avgIntensity, touchCount, totalDistance] = recording.metadata

    // 转换为数据库格式
    const waveformData: WaveformData = {
      id: recording.id,
      name: recording.name,
      description: '',
      authorId: userProfile.id,
      authorName: userProfile.nickname || '匿名用户',
      authorAvatar: userProfile.avatarUrl || undefined,
      points: [], // 新格式不使用TrackPoint数组
      startTime: recording.startTime,
      endTime: recording.endTime,
      duration: recording.duration,
      maxIntensity,
      averageIntensity: avgIntensity,
      totalDistance,
      complexity: this.calculateComplexityFromRecording(recording),
      smoothness: this.calculateSmoothnessFromRecording(recording),
      isPublic,
      isOwner: true,
      publishedAt: isPublic ? new Date() : undefined,
      likeCount: 0,
      favoriteCount: 0,
      playCount: 0,
      tags: [],
      thumbnail: await this.generateThumbnailFromRecording(recording),
      createdAt: new Date(),
      updatedAt: new Date(),
      // 保存录制数据 (确保正确序列化)
      recordingData: JSON.parse(JSON.stringify(recording))
    }

    // 调试：打印即将保存的数据
    console.log('💾 即将保存录制数据:', {
      id: waveformData.id,
      hasRecordingData: !!waveformData.recordingData,
      recordingDataType: typeof waveformData.recordingData,
      recordingDataKeys: waveformData.recordingData ? Object.keys(waveformData.recordingData) : []
    })

    // 保存到Dexie数据库
    await scriptDB.waveforms.put(waveformData)

    // 验证保存结果
    const savedData = await scriptDB.waveforms.get(waveformData.id)
    console.log('✅ 保存后验证:', {
      id: savedData?.id,
      hasRecordingData: !!savedData?.recordingData,
      recordingDataType: typeof savedData?.recordingData
    })

    // 转换为返回格式
    return this.convertToWaveformLibraryItem(waveformData)
  }

  /**
   * 保存轨迹到本地（从SwipeModePage调用）- 保持兼容性
   */
  async saveTrack(track: Track, isPublic: boolean = false): Promise<WaveformLibraryItem> {
    // 获取当前用户信息
    const userProfile = await this.getCurrentUserProfile()

    // 转换为数据库格式
    const waveformData: WaveformData = {
      id: track.id,
      name: track.name || `轨迹 ${new Date().toLocaleTimeString()}`,
      description: '',
      authorId: userProfile.id,
      authorName: userProfile.nickname || '匿名用户',
      authorAvatar: userProfile.avatarUrl || undefined,
      points: track.points,
      startTime: track.startTime,
      endTime: track.endTime,
      duration: track.duration,
      maxIntensity: track.maxIntensity,
      averageIntensity: track.averageIntensity,
      totalDistance: track.totalDistance,
      complexity: this.calculateComplexity(track),
      smoothness: this.calculateSmoothness(track),
      isPublic,
      isOwner: true,
      publishedAt: isPublic ? new Date() : undefined,
      likeCount: 0,
      favoriteCount: 0,
      playCount: 0,
      tags: [],
      thumbnail: await this.generateThumbnail(track),
      createdAt: new Date(),
      updatedAt: new Date()
    }

    // 保存到Dexie数据库
    await scriptDB.waveforms.put(waveformData)

    // 转换为返回格式
    return this.convertToWaveformLibraryItem(waveformData)
  }

  /**
   * 根据ID获取波形
   */
  async getWaveformById(waveformId: string): Promise<WaveformLibraryItem | null> {
    const waveformData = await scriptDB.waveforms.get(waveformId)
    if (!waveformData) return null

    return this.convertToWaveformLibraryItem(waveformData)
  }

  /**
   * 根据ID获取完整的波形数据（包含recordingData）
   */
  async getWaveformDataById(waveformId: string): Promise<WaveformData | null> {
    return (await scriptDB.waveforms.get(waveformId)) || null
  }

  /**
   * 获取我的波形列表
   */
  async getMyWaveforms(): Promise<WaveformLibraryItem[]> {
    const userProfile = await this.getCurrentUserProfile()

    const waveforms = await scriptDB.waveforms
      .where('authorId')
      .equals(userProfile.id)
      .reverse()
      .sortBy('createdAt')

    return Promise.all(waveforms.map(w => this.convertToWaveformLibraryItem(w)))
  }

  /**
   * 获取收藏的波形列表
   */
  async getFavoriteWaveforms(): Promise<WaveformLibraryItem[]> {
    // 获取收藏的波形ID列表
    const favoriteActions = await scriptDB.userActions
      .where('actionType')
      .equals('favorite')
      .toArray()

    // 过滤出value为true的记录
    const validFavorites = favoriteActions.filter(action => action.value === true)

    const favoriteIds = validFavorites.map(action => action.waveformId)

    if (favoriteIds.length === 0) return []

    // 获取收藏的波形数据
    const waveforms = await scriptDB.waveforms
      .where('id')
      .anyOf(favoriteIds)
      .reverse()
      .sortBy('createdAt')

    return Promise.all(waveforms.map(w => this.convertToWaveformLibraryItem(w)))
  }

  /**
   * 获取公共波形广场数据
   */
  async getPublicWaveforms(filter: WaveformLibraryFilter): Promise<WaveformLibraryResponse> {
    // 获取所有波形，然后过滤isPublic=true的
    let waveforms = await scriptDB.waveforms.toArray()
    waveforms = waveforms.filter(w => w.isPublic === true)

    if (filter.searchQuery) {
      const searchLower = filter.searchQuery.toLowerCase()
      waveforms = waveforms.filter(
        w =>
          w.name.toLowerCase().includes(searchLower) ||
          w.authorName.toLowerCase().includes(searchLower)
      )
    }

    // 应用排序
    switch (filter.sortBy) {
      case 'popular':
        waveforms.sort((a, b) => b.likeCount + b.favoriteCount - (a.likeCount + a.favoriteCount))
        break
      case 'duration':
        waveforms.sort((a, b) => b.duration - a.duration)
        break
      case 'complexity':
        waveforms.sort((a, b) => b.complexity - a.complexity)
        break
      case 'latest':
      default:
        waveforms.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
        break
    }

    const items = await Promise.all(waveforms.map(w => this.convertToWaveformLibraryItem(w)))

    return {
      items,
      total: items.length,
      hasMore: false
    }
  }

  /**
   * 点赞波形
   */
  async likeWaveform(waveformId: string): Promise<void> {
    await this.updateUserAction(waveformId, 'like', true)

    // 更新波形的点赞数
    const waveform = await scriptDB.waveforms.get(waveformId)
    if (waveform) {
      await scriptDB.waveforms.update(waveformId, {
        likeCount: waveform.likeCount + 1,
        updatedAt: new Date()
      })
    }
  }

  /**
   * 取消点赞波形
   */
  async unlikeWaveform(waveformId: string): Promise<void> {
    await this.updateUserAction(waveformId, 'like', false)

    // 更新波形的点赞数
    const waveform = await scriptDB.waveforms.get(waveformId)
    if (waveform && waveform.likeCount > 0) {
      await scriptDB.waveforms.update(waveformId, {
        likeCount: waveform.likeCount - 1,
        updatedAt: new Date()
      })
    }
  }

  /**
   * 收藏波形
   */
  async favoriteWaveform(waveformId: string): Promise<void> {
    await this.updateUserAction(waveformId, 'favorite', true)

    // 更新波形的收藏数
    const waveform = await scriptDB.waveforms.get(waveformId)
    if (waveform) {
      await scriptDB.waveforms.update(waveformId, {
        favoriteCount: waveform.favoriteCount + 1,
        updatedAt: new Date()
      })
    }
  }

  /**
   * 取消收藏波形
   */
  async unfavoriteWaveform(waveformId: string): Promise<void> {
    await this.updateUserAction(waveformId, 'favorite', false)

    // 更新波形的收藏数
    const waveform = await scriptDB.waveforms.get(waveformId)
    if (waveform && waveform.favoriteCount > 0) {
      await scriptDB.waveforms.update(waveformId, {
        favoriteCount: waveform.favoriteCount - 1,
        updatedAt: new Date()
      })
    }
  }

  /**
   * 发布/取消发布波形
   */
  async togglePublish(waveformId: string, isPublic: boolean): Promise<void> {
    const waveform = await scriptDB.waveforms.get(waveformId)
    if (!waveform) {
      throw new Error('Waveform not found')
    }

    const userProfile = await this.getCurrentUserProfile()
    if (waveform.authorId !== userProfile.id) {
      throw new Error('Cannot modify waveform')
    }

    await scriptDB.waveforms.update(waveformId, {
      isPublic,
      publishedAt: isPublic ? new Date() : undefined,
      updatedAt: new Date()
    })
  }

  /**
   * 删除波形
   */
  async deleteWaveform(waveformId: string): Promise<void> {
    const waveform = await scriptDB.waveforms.get(waveformId)
    if (!waveform) {
      throw new Error('Waveform not found')
    }

    const userProfile = await this.getCurrentUserProfile()
    if (waveform.authorId !== userProfile.id) {
      throw new Error('Cannot delete waveform')
    }

    // 删除波形数据
    await scriptDB.waveforms.delete(waveformId)

    // 删除相关的用户行为
    await scriptDB.userActions.where('waveformId').equals(waveformId).delete()
  }

  /**
   * 清理旧格式数据（开发阶段使用）
   */
  async clearOldFormatData(): Promise<void> {
    try {
      await this.initialize()

      // 查找所有没有recordingData的记录
      const allWaveforms = await scriptDB.waveforms.toArray()
      const oldFormatItems = allWaveforms.filter(item => !item.recordingData)

      console.log(`🧹 找到 ${oldFormatItems.length} 个旧格式数据，准备清理`)

      // 删除旧格式数据
      for (const item of oldFormatItems) {
        await scriptDB.waveforms.delete(item.id)
        console.log(`🗑️ 删除旧数据: ${item.id}`)
      }

      console.log('✅ 旧格式数据清理完成')
    } catch (error) {
      console.error('❌ 清理旧数据失败:', error)
      throw error
    }
  }

  // ==================== 私有方法 ====================

  private async convertToWaveformLibraryItem(
    waveformData: WaveformData
  ): Promise<WaveformLibraryItem> {
    // 获取用户对这个波形的行为状态
    const userProfile = await this.getCurrentUserProfile()

    const likeAction = await scriptDB.userActions.get(`like_${waveformData.id}`)
    const favoriteAction = await scriptDB.userActions.get(`favorite_${waveformData.id}`)

    // 处理points数据：优先使用recordingData，否则使用传统points
    let points = waveformData.points || []

    // 如果有recordingData，将其转换为TrackPoint格式
    if (
      waveformData.recordingData &&
      waveformData.recordingData.points &&
      waveformData.recordingData.points.length > 0
    ) {
      const recordingData = waveformData.recordingData
      const canvasWidth = recordingData.canvasSize ? recordingData.canvasSize[0] : 400
      const canvasHeight = recordingData.canvasSize ? recordingData.canvasSize[1] : 400

      points = recordingData.points
        .filter((point: any[]) => point[1] > 0) // 只保留有触摸的点 (intensity > 0)
        .map((point: any[], index: number) => {
          const [timestamp, intensity, relativeX, relativeY, pressure] = point

          return {
            x: relativeX ? relativeX * canvasWidth : 0,
            y: relativeY ? relativeY * canvasHeight : 0,
            timestamp: waveformData.startTime + timestamp, // 转换为绝对时间戳
            pressure: pressure || 0.5
          }
        })

      // 调试日志
      console.log('🔄 转换录制数据为轨迹点:', {
        id: waveformData.id,
        originalPointsCount: recordingData.points.length,
        convertedPointsCount: points.length,
        canvasSize: [canvasWidth, canvasHeight],
        samplePoints: points.slice(0, 3)
      })
    }

    const result: WaveformLibraryItem = {
      id: waveformData.id,
      name: waveformData.name,
      points: points, // 使用转换后的points
      startTime: waveformData.startTime,
      endTime: waveformData.endTime,
      duration: waveformData.duration,
      maxIntensity: waveformData.maxIntensity,
      averageIntensity: waveformData.averageIntensity,
      totalDistance: waveformData.totalDistance,
      createdAt: waveformData.createdAt.getTime(),

      // 社交功能字段
      authorId: waveformData.authorId,
      authorName: waveformData.authorName,
      authorAvatar: waveformData.authorAvatar,
      isPublic: waveformData.isPublic,
      isOwner: waveformData.authorId === userProfile.id,
      publishedAt: waveformData.publishedAt?.getTime(),
      likeCount: waveformData.likeCount,
      favoriteCount: waveformData.favoriteCount,
      playCount: waveformData.playCount,
      isLiked: likeAction?.value === true,
      isFavorited: favoriteAction?.value === true,
      description: waveformData.description,
      tags: waveformData.tags,
      thumbnail: waveformData.thumbnail,
      complexity: waveformData.complexity,
      smoothness: waveformData.smoothness,

      // 保留原始录制数据
      recordingData: waveformData.recordingData
    }

    return result
  }

  private async updateUserAction(
    waveformId: string,
    actionType: 'like' | 'favorite',
    value: boolean
  ): Promise<void> {
    const actionId = `${actionType}_${waveformId}`

    const userAction: UserAction = {
      id: actionId,
      waveformId,
      actionType,
      value,
      timestamp: new Date(),
      synced: false
    }

    await scriptDB.userActions.put(userAction)
  }

  private async getCurrentUserProfile() {
    try {
      // 从 user-profile-store 获取用户资料
      const userProfileStore = useUserProfileStore.getState()
      let profile = userProfileStore.userProfile

      // 如果没有缓存数据，尝试获取
      if (!profile) {
        profile = await userProfileStore.fetchUserProfile()
      }

      if (profile) {
        return {
          id: profile.userId,
          nickname: profile.nickname || '匿名用户',
          avatarUrl: profile.avatarUrl
        }
      }
    } catch (error) {
      console.error('Failed to get user profile:', error)
    }

    // 降级到模拟数据
    return {
      id: 'user_' + Date.now(),
      nickname: '匿名用户',
      avatarUrl: null
    }
  }

  private async generateThumbnail(track: Track): Promise<string> {
    try {
      // 使用WaveformPreview组件的generateWaveformThumbnail工具函数
      const { generateWaveformThumbnail } = await import(
        '@/components/device/waveform/WaveformPreview'
      )
      return await generateWaveformThumbnail(track, 200, 40)
    } catch (error) {
      console.error('Failed to generate thumbnail:', error)
      return ''
    }
  }

  private async generateThumbnailFromRecording(recording: WaveformRecording): Promise<string> {
    try {
      // 从录制数据生成缩略图 - 暂时返回空字符串，后续实现
      console.log('生成录制数据缩略图:', recording.name)
      return ''
    } catch (error) {
      console.error('Failed to generate thumbnail from recording:', error)
      return ''
    }
  }

  private calculateComplexity(track: Track): number {
    // 基于轨迹变化频率和方向变化计算复杂度
    return Math.min(5, Math.max(1, Math.floor(track.points.length / 50)))
  }

  private calculateSmoothness(track: Track): number {
    // 基于速度变化的连续性计算平滑度
    return Math.min(5, Math.max(1, Math.floor(Math.random() * 5) + 1))
  }

  private calculateComplexityFromRecording(recording: WaveformRecording): number {
    // 基于录制数据的统计信息计算复杂度
    return Math.min(5, Math.max(1, Math.floor(recording.metadata[0] / 10))) // 假设maxIntensity是100
  }

  private calculateSmoothnessFromRecording(recording: WaveformRecording): number {
    // 基于录制数据的统计信息计算平滑度
    return Math.min(5, Math.max(1, Math.floor(recording.metadata[1] / 10))) // 假设averageIntensity是10
  }
}

// 导出单例实例
export const waveformStorageDexie = WaveformStorageDexieService.getInstance()
