/**
 * 视频存储服务
 * 处理在线视频缓存和本地视频导入
 */
import { Filesystem, Directory } from '@capacitor/filesystem'
import { Capacitor } from '@capacitor/core'
import { FileSystemDownloadService, type FileSystemResourceFile } from './filesystem-download'
import type { VideoItem } from '@/types/video'

export interface StoredVideoItem extends VideoItem {
  filePath?: string // 本地文件路径
  webPath?: string // Web可访问路径
  fileSize?: number // 文件大小
  isLocal: boolean // 是否为本地文件
}

export class VideoStorageService {
  private static readonly VIDEO_SCRIPT_ID = 'video-player' // 统一的脚本ID用于视频存储
  private static updateListeners: ((video: StoredVideoItem) => void)[] = [] // 更新监听器

  /**
   * 缓存在线视频到本地
   */
  static async cacheOnlineVideo(
    video: VideoItem,
    onProgress?: (loaded: number, total: number) => void
  ): Promise<StoredVideoItem> {
    try {
      console.log(`📥 开始缓存在线视频: ${video.title}`)

      // 使用现有的文件系统下载服务
      const resourceFile = await FileSystemDownloadService.downloadFileToFileSystem(
        video.url,
        this.VIDEO_SCRIPT_ID,
        onProgress
      )

      const storedVideo: StoredVideoItem = {
        ...video,
        filePath: resourceFile.filePath,
        webPath: resourceFile.webPath,
        fileSize: resourceFile.size,
        isLocal: true,
        // 更新URL为本地路径，这样后续播放使用本地文件
        url: resourceFile.webPath || video.url
      }

      console.log(`✅ 在线视频缓存完成: ${video.title}`)
      return storedVideo
    } catch (error) {
      console.error(`❌ 缓存在线视频失败: ${video.title}`, error)
      // 缓存失败时返回原始视频信息
      return {
        ...video,
        isLocal: false
      }
    }
  }

  /**
   * 检查视频是否已缓存（快速检查，不验证文件完整性）
   */
  static async isVideoCached(videoUrl: string): Promise<StoredVideoItem | null> {
    try {
      // 快速检查缓存记录，不进行文件系统验证（避免阻塞）
      const resourceFile = await FileSystemDownloadService.getResourceFile(videoUrl)

      if (resourceFile) {
        console.log(`📁 找到缓存记录: ${this.extractTitleFromUrl(videoUrl)}`)

        // 从URL推断视频信息（这里可以根据需要扩展）
        const videoId = resourceFile.id
        const title = this.extractTitleFromUrl(videoUrl)

        return {
          id: videoId,
          title,
          duration: 0, // 需要实际播放时获取
          url: resourceFile.webPath || videoUrl, // 优先使用缓存路径
          poster: '',
          isOnline: true, // 标记为在线视频（已缓存）
          filePath: resourceFile.filePath,
          webPath: resourceFile.webPath,
          fileSize: resourceFile.size,
          isLocal: true
        }
      }

      return null
    } catch (error) {
      // 静默处理错误，不阻塞初始化
      console.warn('检查视频缓存状态失败:', error)
      return null
    }
  }

  /**
   * 验证缓存文件完整性（仅在需要时调用）
   */
  static async validateCachedVideo(videoUrl: string): Promise<boolean> {
    try {
      const resourceFile = await FileSystemDownloadService.getResourceFile(videoUrl)
      if (!resourceFile) return false

      // 在原生环境下验证文件存在
      if (Capacitor.isNativePlatform()) {
        await Filesystem.stat({
          path: resourceFile.filePath,
          directory: Directory.Data
        })
      }

      return true
    } catch (error) {
      console.warn(`缓存文件验证失败: ${videoUrl}`, error)
      // 清理无效的缓存记录
      await FileSystemDownloadService.deleteResourceFile(videoUrl)
      return false
    }
  }

  /**
   * 导入本地视频文件（持久化存储）
   */
  static async importLocalVideo(
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<StoredVideoItem> {
    try {
      console.log(`📁 导入本地视频: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)}MB)`)

      onProgress?.(10) // 开始处理
      const videoId = await this.generateVideoId(file.name + file.size + file.lastModified)

      let videoUrl: string
      let filePath: string | undefined
      let webPath: string | undefined

      if (!Capacitor.isNativePlatform()) {
        // Web环境：创建Blob URL（Web环境下文件会保持在内存中）
        videoUrl = URL.createObjectURL(file)
        console.log(`🌐 Web环境使用Blob URL: ${videoUrl}`)
        onProgress?.(50)
      } else {
        // 原生环境：将文件保存到文件系统
        console.log(`📱 原生环境保存文件到文件系统`)
        onProgress?.(20)

        // 生成文件名
        const fileName = `imported_video_${videoId}.${this.getFileExtension(file.name)}`
        filePath = `imported_videos/${fileName}`

        try {
          // 确保目录存在
          await Filesystem.mkdir({
            path: 'imported_videos',
            directory: Directory.Data,
            recursive: true
          })
          console.log(`📂 创建目录: imported_videos`)
          onProgress?.(30)

          // 将文件转换为ArrayBuffer
          const arrayBuffer = await file.arrayBuffer()
          const uint8Array = new Uint8Array(arrayBuffer)
          onProgress?.(50)

          // 使用分块方式转换为Base64（避免内存溢出）
          const base64Data = this.arrayBufferToBase64(uint8Array)
          onProgress?.(70)

          // 保存文件到文件系统
          await Filesystem.writeFile({
            path: filePath,
            data: base64Data,
            directory: Directory.Data
          })
          console.log(`💾 文件保存成功: ${filePath}`)
          onProgress?.(80)

          // 获取Web可访问的路径
          webPath = Capacitor.convertFileSrc(
            await Filesystem.getUri({
              directory: Directory.Data,
              path: filePath
            }).then(result => result.uri)
          )

          videoUrl = webPath
          console.log(`🔗 生成Web路径: ${webPath}`)
        } catch (fsError) {
          console.error('文件系统保存失败，回退到Blob URL:', fsError)
          // 如果文件系统保存失败，回退到Blob URL
          videoUrl = URL.createObjectURL(file)
        }
      }

      onProgress?.(90) // 创建视频对象

      // 创建视频对象
      const storedVideo: StoredVideoItem = {
        id: videoId,
        title: this.extractTitleFromFileName(file.name),
        duration: 0, // 先设为0，后台异步获取真实时长
        url: videoUrl,
        poster: '',
        isOnline: false,
        fileSize: file.size,
        isLocal: true,
        filePath, // 原生环境的文件路径
        webPath // 原生环境的Web访问路径
      }

      // 保存记录到存储
      await this.saveImportedVideo(storedVideo)

      onProgress?.(100) // 导入完成
      console.log(`✅ 视频导入完成: ${file.name}`)

      // 后台异步获取视频时长，不阻塞返回
      this.updateVideoDurationInBackground(storedVideo)

      return storedVideo
    } catch (error) {
      console.error(`❌ 导入本地视频失败: ${file.name}`, error)
      throw error
    }
  }

  /**
   * 后台异步更新视频时长
   */
  private static async updateVideoDurationInBackground(video: StoredVideoItem) {
    try {
      console.log(`🎬 后台获取视频时长: ${video.title}`)
      const duration = await this.getVideoDuration(video.url)

      if (duration > 0) {
        console.log(`⏱️ 获取到视频时长: ${duration}秒，更新记录`)

        // 更新视频对象
        const updatedVideo = { ...video, duration }

        // 更新存储记录
        await this.saveImportedVideo(updatedVideo)

        // 触发更新事件，通知UI刷新
        this.notifyVideoUpdated(updatedVideo)
      }
    } catch (error) {
      console.warn(`⚠️ 后台获取视频时长失败: ${video.title}`, error)
    }
  }

  /**
   * 保存导入的视频（统一入口）
   */
  static async saveImportedVideo(video: StoredVideoItem): Promise<void> {
    try {
      if (!Capacitor.isNativePlatform()) {
        // Web环境：保存到localStorage
        this.saveImportedVideoRecord(video, null)
      } else {
        // 原生环境：保存到数据库
        await this.saveImportedVideoToDatabase(video)
      }
      console.log(`💾 视频保存成功: ${video.title}`)
    } catch (error) {
      console.error(`❌ 视频保存失败: ${video.title}`, error)
      throw error
    }
  }

  /**
   * 获取所有导入的视频
   */
  static async getImportedVideos(): Promise<StoredVideoItem[]> {
    try {
      let videos: StoredVideoItem[]

      if (!Capacitor.isNativePlatform()) {
        // Web环境：从localStorage获取
        videos = this.getImportedVideosFromStorage()
      } else {
        // 原生环境：从数据库获取
        videos = await this.getImportedVideosFromDatabase()
      }

      // 验证视频有效性（特别是Web环境的Blob URL）
      const validVideos = await this.validateImportedVideos(videos)
      return validVideos
    } catch (error) {
      console.error('获取导入视频失败:', error)
      return []
    }
  }

  /**
   * 验证导入视频的有效性
   */
  private static async validateImportedVideos(
    videos: StoredVideoItem[]
  ): Promise<StoredVideoItem[]> {
    const validVideos: StoredVideoItem[] = []

    for (const video of videos) {
      try {
        if (!Capacitor.isNativePlatform()) {
          // Web环境：检查Blob URL是否仍然有效
          if (video.url.startsWith('blob:')) {
            // 尝试创建一个临时video元素来测试URL
            const testVideo = document.createElement('video')
            testVideo.src = video.url

            // 如果Blob URL无效，会立即触发error事件
            const isValid = await new Promise<boolean>(resolve => {
              const timeout = setTimeout(() => resolve(false), 1000) // 1秒超时

              testVideo.onloadstart = () => {
                clearTimeout(timeout)
                resolve(true)
              }

              testVideo.onerror = () => {
                clearTimeout(timeout)
                resolve(false)
              }

              // 尝试加载
              testVideo.load()
            })

            if (isValid) {
              validVideos.push(video)
            } else {
              console.warn(`⚠️ 无效的导入视频（Blob URL已失效）: ${video.title}`)
              // 从localStorage中移除无效记录
              this.removeInvalidVideoFromStorage(video.id)
            }
          } else {
            validVideos.push(video)
          }
        } else {
          // 原生环境：检查文件是否存在
          if (video.filePath) {
            try {
              await Filesystem.stat({
                path: video.filePath,
                directory: Directory.Data
              })
              validVideos.push(video)
            } catch (error) {
              console.warn(`⚠️ 无效的导入视频（文件不存在）: ${video.title}`)
              // 从数据库中移除无效记录
              await this.removeInvalidVideoFromDatabase(video.id)
            }
          } else {
            validVideos.push(video)
          }
        }
      } catch (error) {
        console.warn(`验证视频失败: ${video.title}`, error)
      }
    }

    return validVideos
  }

  /**
   * 从localStorage移除无效视频记录
   */
  private static removeInvalidVideoFromStorage(videoId: string): void {
    try {
      const key = 'imported_videos'
      const existingVideos = JSON.parse(localStorage.getItem(key) || '[]')
      const filteredVideos = existingVideos.filter((v: any) => v.id !== videoId)
      localStorage.setItem(key, JSON.stringify(filteredVideos))
    } catch (error) {
      console.error('移除无效视频记录失败:', error)
    }
  }

  /**
   * 从数据库移除无效视频记录
   */
  private static async removeInvalidVideoFromDatabase(videoId: string): Promise<void> {
    try {
      const { scriptDB } = await import('@/lib/database')
      await scriptDB.resourceFiles.delete(videoId)
    } catch (error) {
      console.error('从数据库移除无效视频记录失败:', error)
    }
  }

  /**
   * 删除导入的视频
   */
  static async deleteImportedVideo(videoId: string): Promise<void> {
    try {
      if (!Capacitor.isNativePlatform()) {
        // Web环境：从localStorage删除
        this.deleteImportedVideoFromStorage(videoId)
        console.log(`🗑️ Web环境删除导入视频完成: ${videoId}`)
        return
      }

      // 原生环境：先获取视频信息，然后删除文件和数据库记录
      const { scriptDB } = await import('@/lib/database')
      const record = await scriptDB.resourceFiles.get(videoId)

      if (record) {
        const recordAny = record as any

        // 删除文件系统中的文件
        if (recordAny.filePath) {
          try {
            await Filesystem.deleteFile({
              path: recordAny.filePath,
              directory: Directory.Data
            })
            console.log(`🗑️ 删除文件: ${recordAny.filePath}`)
          } catch (error) {
            console.warn(`文件删除失败: ${recordAny.filePath}`, error)
          }
        }

        // 删除数据库记录
        await scriptDB.resourceFiles.delete(videoId)
        console.log(`🗑️ 删除数据库记录: ${videoId}`)
      }

      console.log(`🗑️ 原生环境删除导入视频完成: ${videoId}`)
    } catch (error) {
      console.error(`❌ 删除导入视频失败: ${videoId}`, error)
      throw error
    }
  }

  /**
   * 获取视频时长
   */
  private static async getVideoDuration(videoUrl: string): Promise<number> {
    return new Promise(resolve => {
      const video = document.createElement('video')
      video.preload = 'metadata'
      video.muted = true // 静音避免播放声音

      let resolved = false

      const cleanup = () => {
        if (!resolved) {
          resolved = true
          video.remove()
        }
      }

      video.onloadedmetadata = () => {
        if (!resolved) {
          const duration = video.duration
          if (duration && !isNaN(duration) && duration > 0) {
            console.log(`📊 获取到视频时长: ${duration}秒`)
            cleanup()
            resolve(Math.round(duration))
          } else {
            console.warn('视频时长无效:', duration)
            // 不立即resolve，等待其他事件
          }
        }
      }

      video.onloadeddata = () => {
        // 数据加载完成，再次尝试获取时长
        if (!resolved && video.duration && !isNaN(video.duration) && video.duration > 0) {
          console.log(`📊 从loadeddata获取到视频时长: ${video.duration}秒`)
          cleanup()
          resolve(Math.round(video.duration))
        }
      }

      video.onerror = e => {
        if (!resolved) {
          console.warn('无法获取视频时长，使用默认值0', e)
          cleanup()
          resolve(0)
        }
      }

      // 设置较长的超时，给视频更多加载时间
      setTimeout(() => {
        if (!resolved) {
          console.warn('获取视频时长超时，使用默认值0')
          cleanup()
          resolve(0)
        }
      }, 10000) // 10秒超时

      try {
        video.src = videoUrl
        // 手动触发加载
        video.load()
      } catch (error) {
        console.error('设置视频源失败:', error)
        cleanup()
        resolve(0)
      }
    })
  }

  /**
   * 将ArrayBuffer转换为Base64（分块处理避免调用栈溢出）
   */
  private static arrayBufferToBase64(uint8Array: Uint8Array): string {
    const chunkSize = 1024 // 1KB chunks，避免调用栈溢出
    const chunks: string[] = []

    for (let i = 0; i < uint8Array.length; i += chunkSize) {
      const chunk = uint8Array.slice(i, i + chunkSize)
      // 使用逐字符构建，避免apply调用栈溢出
      let chunkString = ''
      for (let j = 0; j < chunk.length; j++) {
        chunkString += String.fromCharCode(chunk[j])
      }
      chunks.push(chunkString)
    }

    const binaryString = chunks.join('')
    return btoa(binaryString)
  }

  /**
   * 生成视频ID
   */
  private static async generateVideoId(input: string): Promise<string> {
    try {
      if (typeof crypto !== 'undefined' && crypto.subtle) {
        const encoder = new TextEncoder()
        const data = encoder.encode(input)
        const hashBuffer = await crypto.subtle.digest('SHA-256', data)
        const hashArray = Array.from(new Uint8Array(hashBuffer))
        return hashArray
          .map(b => b.toString(16).padStart(2, '0'))
          .join('')
          .substring(0, 16)
      }
    } catch (error) {
      console.warn('crypto.subtle不可用，使用简单哈希')
    }

    // 降级到简单哈希
    let hash = 0
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i)
      hash = (hash << 5) - hash + char
      hash = hash & hash
    }
    return `imported_${Math.abs(hash).toString(36)}`
  }

  /**
   * 获取文件扩展名
   */
  private static getFileExtension(fileName: string): string {
    const parts = fileName.split('.')
    return parts.length > 1 ? parts[parts.length - 1].toLowerCase() : 'mp4'
  }

  /**
   * 从文件名提取标题
   */
  private static extractTitleFromFileName(fileName: string): string {
    const nameWithoutExt = fileName.replace(/\.[^/.]+$/, '')
    return nameWithoutExt || '未命名视频'
  }

  /**
   * 从URL提取标题
   */
  private static extractTitleFromUrl(url: string): string {
    const parts = url.split('/')
    const fileName = parts[parts.length - 1] || '在线视频'
    return this.extractTitleFromFileName(fileName)
  }

  /**
   * Web环境：保存导入视频记录到localStorage
   */
  private static saveImportedVideoRecord(video: StoredVideoItem, file: File | null): void {
    try {
      const key = 'imported_videos'
      const existingVideos = JSON.parse(localStorage.getItem(key) || '[]')

      // 避免重复添加
      const existingIndex = existingVideos.findIndex((v: StoredVideoItem) => v.id === video.id)
      if (existingIndex >= 0) {
        existingVideos[existingIndex] = video
      } else {
        existingVideos.push(video)
      }

      localStorage.setItem(key, JSON.stringify(existingVideos))
    } catch (error) {
      console.error('保存导入视频记录失败:', error)
    }
  }

  /**
   * Web环境：从localStorage获取导入视频
   */
  private static getImportedVideosFromStorage(): StoredVideoItem[] {
    try {
      const key = 'imported_videos'
      const videos = JSON.parse(localStorage.getItem(key) || '[]')
      return videos.filter((video: StoredVideoItem) => video.isLocal)
    } catch (error) {
      console.error('从localStorage获取导入视频失败:', error)
      return []
    }
  }

  /**
   * Web环境：从localStorage删除导入视频
   */
  private static deleteImportedVideoFromStorage(videoId: string): void {
    try {
      const key = 'imported_videos'
      const existingVideos = JSON.parse(localStorage.getItem(key) || '[]')
      const filteredVideos = existingVideos.filter((v: StoredVideoItem) => v.id !== videoId)
      localStorage.setItem(key, JSON.stringify(filteredVideos))
    } catch (error) {
      console.error('从localStorage删除导入视频失败:', error)
    }
  }

  /**
   * 原生环境：保存导入视频到数据库
   */
  private static async saveImportedVideoToDatabase(video: StoredVideoItem): Promise<void> {
    try {
      const { scriptDB } = await import('@/lib/database')

      const record = {
        id: video.id,
        url: video.url,
        mimeType: 'video/mp4', // 可以根据扩展名推断
        size: video.fileSize || 0,
        scriptId: this.VIDEO_SCRIPT_ID,
        downloadedAt: new Date(),
        lastAccessedAt: new Date(),
        filePath: video.filePath || '',
        webPath: video.webPath || '',
        // 额外的视频信息
        title: video.title,
        duration: video.duration || 0,
        isImported: true
      }

      // 检查是否已存在，如果存在则更新，否则添加
      const existing = await scriptDB.resourceFiles.get(video.id)
      if (existing) {
        await scriptDB.resourceFiles.update(video.id, record)
        console.log(`🔄 更新数据库记录: ${video.title}`)
      } else {
        await scriptDB.resourceFiles.add(record)
        console.log(`➕ 添加数据库记录: ${video.title}`)
      }
    } catch (error) {
      console.error('保存导入视频到数据库失败:', error)
      throw error
    }
  }

  /**
   * 原生环境：从数据库获取导入视频
   */
  private static async getImportedVideosFromDatabase(): Promise<StoredVideoItem[]> {
    try {
      const { scriptDB } = await import('@/lib/database')

      const records = await scriptDB.resourceFiles
        .where('scriptId')
        .equals(this.VIDEO_SCRIPT_ID)
        .and(record => (record as any).isImported === true)
        .toArray()

      const videos: StoredVideoItem[] = []

      for (const record of records) {
        const recordAny = record as any

        // 生成Web路径
        let webPath = recordAny.webPath
        if (recordAny.filePath && !webPath) {
          try {
            const fileUri = await Filesystem.getUri({
              path: recordAny.filePath,
              directory: Directory.Data
            })
            webPath = Capacitor.convertFileSrc(fileUri.uri)
          } catch (error) {
            console.warn('生成Web路径失败:', error)
            continue
          }
        }

        videos.push({
          id: recordAny.id,
          title: recordAny.title || this.extractTitleFromUrl(recordAny.url),
          duration: recordAny.duration || 0, // 从数据库读取时长
          url: webPath || recordAny.url,
          poster: '',
          isOnline: false,
          filePath: recordAny.filePath,
          webPath,
          fileSize: recordAny.size,
          isLocal: true
        })
      }

      return videos
    } catch (error) {
      console.error('从数据库获取导入视频失败:', error)
      return []
    }
  }

  /**
   * 添加视频更新监听器
   */
  static addUpdateListener(listener: (video: StoredVideoItem) => void): void {
    this.updateListeners.push(listener)
  }

  /**
   * 移除视频更新监听器
   */
  static removeUpdateListener(listener: (video: StoredVideoItem) => void): void {
    const index = this.updateListeners.indexOf(listener)
    if (index > -1) {
      this.updateListeners.splice(index, 1)
    }
  }

  /**
   * 通知视频更新
   */
  private static notifyVideoUpdated(video: StoredVideoItem): void {
    this.updateListeners.forEach(listener => {
      try {
        listener(video)
      } catch (error) {
        console.error('视频更新监听器执行失败:', error)
      }
    })
  }

  /**
   * 清理所有视频缓存
   */
  static async clearAllVideoCache(): Promise<void> {
    try {
      await FileSystemDownloadService.deleteScriptResources(this.VIDEO_SCRIPT_ID)

      // Web环境：清理localStorage
      if (!Capacitor.isNativePlatform()) {
        localStorage.removeItem('imported_videos')
      }

      console.log('🗑️ 清理所有视频缓存完成')
    } catch (error) {
      console.error('❌ 清理视频缓存失败:', error)
    }
  }

  /**
   * 获取视频存储统计信息
   */
  static async getVideoStorageStats(): Promise<{
    cachedVideos: number
    importedVideos: number
    totalSize: number
    totalSizeMB: number
  }> {
    try {
      const stats = await FileSystemDownloadService.getStorageStats()
      const importedVideos = await this.getImportedVideos()

      return {
        cachedVideos: stats.totalFiles - importedVideos.length,
        importedVideos: importedVideos.length,
        totalSize: stats.totalSize,
        totalSizeMB: stats.totalSizeMB
      }
    } catch (error) {
      console.error('获取视频存储统计失败:', error)
      return {
        cachedVideos: 0,
        importedVideos: 0,
        totalSize: 0,
        totalSizeMB: 0
      }
    }
  }
}
