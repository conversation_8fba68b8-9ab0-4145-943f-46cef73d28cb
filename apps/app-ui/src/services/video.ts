import type { VideoItem } from '@/types/video'
import { VideoStorageService, type StoredVideoItem } from './video-storage'

class VideoService {
  private onlineVideos: VideoItem[] = []
  private importedVideos: StoredVideoItem[] = []

  // 获取在线视频列表（带缓存检查）
  async getOnlineVideos(): Promise<StoredVideoItem[]> {
    try {
      // 在线视频数据 - 基于提供的URL格式
      const onlineVideoData: VideoItem[] = [
        {
          id: 'online-1',
          title: 'Video 1',
          duration: 180,
          url: 'https://assets.pleasurehub.app/device-materials/1.mp4',
          poster: '', // 不设置poster，避免显示默认播放按钮
          isOnline: true
        },
        {
          id: 'online-2',
          title: 'Video 2',
          duration: 200,
          url: 'https://assets.pleasurehub.app/device-materials/2.mp4',
          poster: '', // 不设置poster，避免显示默认播放按钮
          isOnline: true
        },
        {
          id: 'online-3',
          title: 'Video 3',
          duration: 240,
          url: 'https://assets.pleasurehub.app/device-materials/3.mp4',
          poster: '', // 不设置poster，避免显示默认播放按钮
          isOnline: true
        }
      ]

      // 并行检查所有视频的缓存状态，避免串行等待
      const videosWithCache: StoredVideoItem[] = await Promise.all(
        onlineVideoData.map(async video => {
          try {
            const cachedVideo = await VideoStorageService.isVideoCached(video.url)
            if (cachedVideo) {
              // 使用缓存版本，但保留原始信息
              console.log(`📁 使用缓存视频: ${video.title}`)
              return {
                ...cachedVideo,
                id: video.id,
                title: video.title,
                duration: video.duration,
                poster: video.poster,
                isOnline: video.isOnline
              }
            } else {
              // 未缓存，返回原始版本（在线播放）
              console.log(`🌐 在线视频: ${video.title}`)
              return {
                id: video.id,
                title: video.title,
                duration: video.duration,
                url: video.url,
                poster: video.poster,
                isOnline: video.isOnline,
                isLocal: false
              } as StoredVideoItem
            }
          } catch (error) {
            console.warn(`检查视频缓存失败: ${video.title}`, error)
            // 出错时返回原始版本
            return {
              id: video.id,
              title: video.title,
              duration: video.duration,
              url: video.url,
              poster: video.poster,
              isOnline: video.isOnline,
              isLocal: false
            } as StoredVideoItem
          }
        })
      )

      return videosWithCache
    } catch (error) {
      console.error('获取在线视频失败:', error)
      return []
    }
  }

  // 获取导入视频列表
  async getImportedVideos(): Promise<StoredVideoItem[]> {
    try {
      this.importedVideos = await VideoStorageService.getImportedVideos()
      return this.importedVideos
    } catch (error) {
      console.error('获取导入视频失败:', error)
      return []
    }
  }

  // 获取所有视频（在线 + 导入）
  async getAllVideos(): Promise<StoredVideoItem[]> {
    const [onlineVideos, importedVideos] = await Promise.all([
      this.getOnlineVideos(),
      this.getImportedVideos()
    ])
    return [...onlineVideos, ...importedVideos]
  }

  // 缓存在线视频到本地
  async cacheVideo(
    video: VideoItem,
    onProgress?: (loaded: number, total: number) => void
  ): Promise<StoredVideoItem> {
    return VideoStorageService.cacheOnlineVideo(video, onProgress)
  }

  // 导入本地视频
  async importVideo(file: File, onProgress?: (progress: number) => void): Promise<StoredVideoItem> {
    const importedVideo = await VideoStorageService.importLocalVideo(file, onProgress)
    // 刷新导入视频列表
    this.importedVideos = await VideoStorageService.getImportedVideos()
    return importedVideo
  }

  // 删除导入的视频
  async deleteImportedVideo(videoId: string): Promise<void> {
    await VideoStorageService.deleteImportedVideo(videoId)
    // 刷新导入视频列表
    this.importedVideos = await VideoStorageService.getImportedVideos()
  }

  // 根据ID获取视频
  async getVideoById(id: string): Promise<StoredVideoItem | null> {
    const allVideos = await this.getAllVideos()
    return allVideos.find(video => video.id === id) || null
  }

  // 添加导入视频
  async addImportedVideo(video: Omit<VideoItem, 'id' | 'isOnline'>): Promise<StoredVideoItem> {
    const newVideo: StoredVideoItem = {
      ...video,
      id: `imported-${Date.now()}`,
      isOnline: false,
      isLocal: true
    }

    this.importedVideos.push(newVideo)
    return newVideo
  }

  // 删除导入视频
  async removeImportedVideo(id: string): Promise<boolean> {
    const index = this.importedVideos.findIndex(video => video.id === id)
    if (index > -1) {
      this.importedVideos.splice(index, 1)
      return true
    }
    return false
  }
}

export const videoService = new VideoService()
