/**
 * 文件系统下载服务
 * 专门处理Capacitor环境中的文件下载和管理
 */
import { Filesystem, Directory } from '@capacitor/filesystem'
import { CapacitorHttp } from '@capacitor/core'
import { Capacitor } from '@capacitor/core'
import { scriptDB, type ResourceFile, type DownloadTask } from '@/lib/database'

export interface FileSystemResourceFile extends Omit<ResourceFile, 'blob'> {
  filePath: string // 文件系统路径
  webPath?: string // Web可访问路径
}

export class FileSystemDownloadService {
  private static readonly SCRIPTS_DIR = 'scripts'
  private static readonly RESOURCES_DIR = 'resources'

  /**
   * 初始化文件系统目录
   */
  static async initializeDirectories(): Promise<void> {
    try {
      // 创建scripts目录
      try {
        await Filesystem.mkdir({
          path: this.SCRIPTS_DIR,
          directory: Directory.Data,
          recursive: true
        })
      } catch (error) {
        // 目录已存在时忽略
      }

      // 创建resources目录
      try {
        await Filesystem.mkdir({
          path: this.RESOURCES_DIR,
          directory: Directory.Data,
          recursive: true
        })
      } catch (error) {
        // 目录已存在时忽略
      }

      console.log('📁 文件系统目录初始化完成')
    } catch (error) {
      console.warn('⚠️ 文件系统目录初始化失败:', error)
    }
  }

  /**
   * 将ArrayBuffer转换为Base64（分块处理避免调用栈溢出）
   */
  private static arrayBufferToBase64(uint8Array: Uint8Array): string {
    const chunkSize = 1024 // 1KB chunks，避免调用栈溢出
    const chunks: string[] = []

    for (let i = 0; i < uint8Array.length; i += chunkSize) {
      const chunk = uint8Array.slice(i, i + chunkSize)
      // 使用逐字符构建，避免apply调用栈溢出
      let chunkString = ''
      for (let j = 0; j < chunk.length; j++) {
        chunkString += String.fromCharCode(chunk[j])
      }
      chunks.push(chunkString)
    }

    const binaryString = chunks.join('')
    return btoa(binaryString)
  }

  /**
   * 生成文件ID（使用兼容的哈希方法）
   * 🔧 修复：移除时间戳，确保相同URL生成相同ID
   */
  private static async generateFileId(url: string): Promise<string> {
    // 🆕 使用更兼容的哈希方法
    try {
      // 优先尝试使用crypto.subtle
      if (typeof crypto !== 'undefined' && crypto.subtle) {
        const encoder = new TextEncoder()
        const data = encoder.encode(url)
        const hashBuffer = await crypto.subtle.digest('SHA-256', data)
        const hashArray = Array.from(new Uint8Array(hashBuffer))
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
      }
    } catch (error) {
      console.warn('⚠️ [FileSystem] crypto.subtle不可用，使用简单哈希:', error)
    }

    // 降级到简单哈希方法
    let hash = 0
    for (let i = 0; i < url.length; i++) {
      const char = url.charCodeAt(i)
      hash = (hash << 5) - hash + char
      hash = hash & hash // 转换为32位整数
    }

    // 🔧 移除时间戳，确保相同URL总是生成相同ID
    return Math.abs(hash).toString(36)
  }

  /**
   * 获取文件扩展名
   */
  private static getFileExtension(url: string): string {
    const parts = url.split('.')
    return parts.length > 1 ? parts[parts.length - 1].split('?')[0] : 'bin'
  }

  /**
   * 获取MIME类型
   */
  private static getMimeType(extension: string): string {
    const mimeMap: Record<string, string> = {
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      png: 'image/png',
      gif: 'image/gif',
      webp: 'image/webp',
      svg: 'image/svg+xml',
      mp4: 'video/mp4',
      webm: 'video/webm',
      mp3: 'audio/mpeg',
      wav: 'audio/wav',
      ogg: 'audio/ogg',
      aac: 'audio/aac',
      m4a: 'audio/mp4'
    }
    return mimeMap[extension.toLowerCase()] || 'application/octet-stream'
  }

  /**
   * 下载文件到文件系统
   */
  static async downloadFileToFileSystem(
    url: string,
    scriptId: string,
    onProgress?: (loaded: number, total: number) => void
  ): Promise<FileSystemResourceFile> {
    try {
      console.log(`📥 [FileSystem] 开始下载文件: ${url}`)

      // 生成文件ID和路径
      const fileId = await this.generateFileId(url)
      const extension = this.getFileExtension(url)
      const fileName = `${fileId}.${extension}`
      const filePath = `${this.RESOURCES_DIR}/${scriptId}/${fileName}`

      // 检查文件是否已存在
      try {
        const existingFile = await Filesystem.stat({
          path: filePath,
          directory: Directory.Data
        })

        if (existingFile.size > 0) {
          console.log(`✅ [FileSystem] 文件已存在，跳过下载: ${url}`)

          // 生成Web路径 - 使用正确的方法
          const fileUri = await Filesystem.getUri({
            path: filePath,
            directory: Directory.Data
          })
          const webPath = Capacitor.convertFileSrc(fileUri.uri)

          // 从数据库获取现有记录或创建新记录
          let resourceFile = (await scriptDB.resourceFiles.get(fileId)) as any
          if (!resourceFile) {
            resourceFile = {
              id: fileId,
              url,
              mimeType: this.getMimeType(extension),
              size: existingFile.size,
              scriptId,
              downloadedAt: new Date(),
              lastAccessedAt: new Date(),
              filePath,
              webPath
            }
            await scriptDB.resourceFiles.add(resourceFile)
          } else {
            // 更新现有记录
            await scriptDB.resourceFiles.update(fileId, {
              lastAccessedAt: new Date(),
              filePath
            })
            resourceFile.filePath = filePath
            resourceFile.webPath = webPath
          }

          return resourceFile
        }
      } catch (error) {
        // 文件不存在，继续下载
      }

      // 确保目录存在
      const scriptDir = `${this.RESOURCES_DIR}/${scriptId}`
      try {
        // 先检查目录是否存在
        await Filesystem.stat({
          path: scriptDir,
          directory: Directory.Data
        })
        console.log(`📁 [FileSystem] 目录已存在: ${scriptDir}`)
      } catch (error) {
        // 目录不存在，创建它
        try {
          await Filesystem.mkdir({
            path: scriptDir,
            directory: Directory.Data,
            recursive: true
          })
          console.log(`📁 [FileSystem] 创建目录: ${scriptDir}`)
        } catch (mkdirError) {
          console.warn(`⚠️ [FileSystem] 创建目录失败: ${scriptDir}`, mkdirError)
          // 继续执行，可能目录在并发创建中
        }
      }

      // 使用CapacitorHttp下载文件
      console.log(`🌐 [FileSystem] 开始网络下载: ${url}`)

      let response: any
      try {
        response = await CapacitorHttp.get({
          url,
          headers: {},
          responseType: 'blob'
        })
      } catch (httpError) {
        console.error(`❌ [FileSystem] HTTP下载失败: ${url}`, httpError)
        throw new Error(`网络下载失败: ${httpError}`)
      }

      if (response.status < 200 || response.status >= 300) {
        throw new Error(`HTTP ${response.status}: 下载失败`)
      }

      // 将数据转换为base64（Capacitor Filesystem需要）
      let base64Data: string

      if (response.data instanceof ArrayBuffer) {
        // 如果是ArrayBuffer，转换为base64（使用分块处理）
        const uint8Array = new Uint8Array(response.data)
        base64Data = this.arrayBufferToBase64(uint8Array)
      } else if (typeof response.data === 'string') {
        // 如果已经是base64字符串
        base64Data = response.data
      } else if (response.data instanceof Uint8Array) {
        // 如果是Uint8Array（使用分块处理）
        base64Data = this.arrayBufferToBase64(response.data)
      } else {
        // 尝试将其转换为字符串再编码
        const str = String(response.data)
        base64Data = btoa(str)
      }

      // 写入文件
      await Filesystem.writeFile({
        path: filePath,
        data: base64Data,
        directory: Directory.Data
      })

      // 获取文件信息
      const fileInfo = await Filesystem.stat({
        path: filePath,
        directory: Directory.Data
      })

      // 生成Web路径 - 使用正确的方法
      const fileUri = await Filesystem.getUri({
        path: filePath,
        directory: Directory.Data
      })
      const webPath = Capacitor.convertFileSrc(fileUri.uri)

      // 保存到数据库
      const resourceFile: FileSystemResourceFile = {
        id: fileId,
        url,
        mimeType: this.getMimeType(extension),
        size: fileInfo.size,
        scriptId,
        downloadedAt: new Date(),
        lastAccessedAt: new Date(),
        filePath,
        webPath
      }

      // 保存到数据库（移除blob字段）
      const dbRecord = { ...resourceFile }
      delete (dbRecord as any).webPath // webPath不保存到数据库，动态生成
      await scriptDB.resourceFiles.add(dbRecord as any)

      console.log(`✅ [FileSystem] 文件下载完成: ${url} -> ${webPath}`)
      return resourceFile
    } catch (error) {
      console.error(`❌ [FileSystem] 文件下载失败: ${url}`, error)
      throw error
    }
  }

  /**
   * 从文件系统获取资源文件信息
   */
  static async getResourceFile(originalUrl: string): Promise<FileSystemResourceFile | null> {
    try {
      const fileId = await this.generateFileId(originalUrl)
      const resourceFile = (await scriptDB.resourceFiles.get(fileId)) as any

      if (!resourceFile || !resourceFile.filePath) {
        return null
      }

      // 检查文件是否存在
      try {
        await Filesystem.stat({
          path: resourceFile.filePath,
          directory: Directory.Data
        })
      } catch (error) {
        console.warn(`⚠️ [FileSystem] 文件不存在: ${resourceFile.filePath}`)
        return null
      }

      // 生成Web路径 - 使用正确的方法
      const fileUri = await Filesystem.getUri({
        path: resourceFile.filePath,
        directory: Directory.Data
      })
      const webPath = Capacitor.convertFileSrc(fileUri.uri)

      // 更新最后访问时间
      await scriptDB.resourceFiles.update(fileId, {
        lastAccessedAt: new Date()
      })

      return {
        ...resourceFile,
        webPath
      }
    } catch (error) {
      console.error(`❌ [FileSystem] 获取资源文件失败: ${originalUrl}`, error)
      return null
    }
  }

  /**
   * 删除资源文件
   */
  static async deleteResourceFile(fileId: string): Promise<void> {
    try {
      const resourceFile = (await scriptDB.resourceFiles.get(fileId)) as any

      if (resourceFile && resourceFile.filePath) {
        try {
          await Filesystem.deleteFile({
            path: resourceFile.filePath,
            directory: Directory.Data
          })
        } catch (error) {
          console.warn(`⚠️ [FileSystem] 删除文件失败: ${resourceFile.filePath}`, error)
        }
      }

      // 从数据库删除记录
      await scriptDB.resourceFiles.delete(fileId)
    } catch (error) {
      console.error(`❌ [FileSystem] 删除资源文件失败: ${fileId}`, error)
    }
  }

  /**
   * 删除剧本的所有资源文件
   */
  static async deleteScriptResources(scriptId: string): Promise<void> {
    try {
      // 获取剧本的所有资源文件
      const resourceFiles = (await scriptDB.resourceFiles
        .where('scriptId')
        .equals(scriptId)
        .toArray()) as any[]

      // 删除文件系统中的文件
      for (const resourceFile of resourceFiles) {
        if (resourceFile.filePath) {
          try {
            await Filesystem.deleteFile({
              path: resourceFile.filePath,
              directory: Directory.Data
            })
          } catch (error) {
            console.warn(`⚠️ [FileSystem] 删除文件失败: ${resourceFile.filePath}`, error)
          }
        }
      }

      // 删除目录（如果为空）
      try {
        await Filesystem.rmdir({
          path: `${this.RESOURCES_DIR}/${scriptId}`,
          directory: Directory.Data
        })
      } catch (error) {
        console.warn(`⚠️ [FileSystem] 删除目录失败: ${this.RESOURCES_DIR}/${scriptId}`, error)
      }

      // 从数据库删除记录
      await scriptDB.resourceFiles.where('scriptId').equals(scriptId).delete()

      console.log(`🗑️ [FileSystem] 删除剧本资源完成: ${scriptId}`)
    } catch (error) {
      console.error(`❌ [FileSystem] 删除剧本资源失败: ${scriptId}`, error)
    }
  }

  /**
   * 获取存储统计信息
   */
  static async getStorageStats(): Promise<{
    totalFiles: number
    totalSize: number
    totalSizeMB: number
  }> {
    try {
      const resourceFiles = (await scriptDB.resourceFiles.toArray()) as any[]
      const totalFiles = resourceFiles.length
      const totalSize = resourceFiles.reduce((sum, file) => sum + (file.size || 0), 0)
      const totalSizeMB = Math.round((totalSize / 1024 / 1024) * 100) / 100

      return {
        totalFiles,
        totalSize,
        totalSizeMB
      }
    } catch (error) {
      console.error('❌ [FileSystem] 获取存储统计失败:', error)
      return {
        totalFiles: 0,
        totalSize: 0,
        totalSizeMB: 0
      }
    }
  }
}

// 初始化文件系统
if (Capacitor.isNativePlatform()) {
  FileSystemDownloadService.initializeDirectories().catch(console.error)
}
