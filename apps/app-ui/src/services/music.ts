import type { MusicItem, OnlineMusicConfig, ImportMusicOptions } from '@/types/music'
import { MusicStorageService, type StoredMusicItem } from './music-storage'

// 在线音乐配置
const ONLINE_MUSIC_LIST: OnlineMusicConfig[] = [
  {
    id: 'online-1',
    title: '律动节拍',
    url: 'https://assets.pleasurehub.app/device-materials/1.mp3',
    duration: 180 // 3分钟
  },
  {
    id: 'online-2',
    title: '梦幻旋律',
    url: 'https://assets.pleasurehub.app/device-materials/2.mp3',
    duration: 210 // 3分30秒
  },
  {
    id: 'online-3',
    title: '激情狂欢',
    url: 'https://assets.pleasurehub.app/device-materials/3.mp3',
    duration: 195 // 3分15秒
  }
]

// 导入音乐配置
const IMPORT_OPTIONS: ImportMusicOptions = {
  acceptedFormats: ['.mp3', '.wav', '.flac', '.m4a'],
  maxFileSize: 50, // 50MB
  maxFiles: 100
}

/**
 * 音乐服务类
 * 管理在线音乐和导入音乐的相关操作
 */
class MusicService {
  private onlineMusic: MusicItem[] = []
  private importedMusic: StoredMusicItem[] = []

  // 获取在线音乐列表（带缓存检查）
  async getOnlineMusic(): Promise<StoredMusicItem[]> {
    try {
      // 在线音乐数据
      const onlineMusicData: MusicItem[] = ONLINE_MUSIC_LIST.map(config => ({
        id: config.id,
        title: config.title,
        artist: '官方音乐',
        duration: config.duration,
        url: config.url,
        isOnline: true,
        createdAt: new Date()
      }))

      // 并行检查所有音乐的缓存状态
      const musicWithCache: StoredMusicItem[] = await Promise.all(
        onlineMusicData.map(async music => {
          try {
            const cachedMusic = await MusicStorageService.isMusicCached(music.url)
            if (cachedMusic) {
              // 使用缓存版本，但保留原始信息
              console.log(`📁 使用缓存音乐: ${music.title}`)
              return {
                ...cachedMusic,
                id: music.id,
                title: music.title,
                artist: music.artist,
                isOnline: true
              }
            } else {
              // 返回原始在线音乐信息
              return {
                ...music,
                isLocal: false
              }
            }
          } catch (error) {
            console.error(`检查音乐缓存失败: ${music.title}`, error)
            return {
              ...music,
              isLocal: false
            }
          }
        })
      )

      this.onlineMusic = musicWithCache
      return musicWithCache
    } catch (error) {
      console.error('获取在线音乐失败:', error)
      return []
    }
  }

  // 获取导入的音乐列表
  async getImportedMusic(): Promise<StoredMusicItem[]> {
    try {
      const importedMusic = await MusicStorageService.getAllImportedMusic()
      this.importedMusic = importedMusic
      return importedMusic
    } catch (error) {
      console.error('获取导入音乐失败:', error)
      return []
    }
  }

  // 获取所有音乐（在线 + 导入）
  async getAllMusic(): Promise<StoredMusicItem[]> {
    try {
      const [onlineMusic, importedMusic] = await Promise.all([
        this.getOnlineMusic(),
        this.getImportedMusic()
      ])
      return [...onlineMusic, ...importedMusic]
    } catch (error) {
      console.error('获取所有音乐失败:', error)
      return []
    }
  }

  // 导入音乐文件
  async importMusic(file: File, onProgress?: (progress: number) => void): Promise<StoredMusicItem> {
    try {
      // 验证文件格式
      if (!this.isValidMusicFile(file)) {
        throw new Error('不支持的音乐格式')
      }

      // 验证文件大小
      if (file.size > IMPORT_OPTIONS.maxFileSize * 1024 * 1024) {
        throw new Error(`文件大小超过限制 (${IMPORT_OPTIONS.maxFileSize}MB)`)
      }

      // 检查导入数量限制
      const currentCount = this.importedMusic.length
      if (currentCount >= IMPORT_OPTIONS.maxFiles) {
        throw new Error(`导入音乐数量已达上限 (${IMPORT_OPTIONS.maxFiles})`)
      }

      // 使用存储服务导入音乐
      const importedMusic = await MusicStorageService.importLocalMusic(file, onProgress)

      // 更新本地缓存
      this.importedMusic.push(importedMusic)

      return importedMusic
    } catch (error) {
      console.error('导入音乐失败:', error)
      throw error
    }
  }

  // 删除导入的音乐
  async deleteImportedMusic(musicId: string): Promise<void> {
    try {
      await MusicStorageService.deleteImportedMusic(musicId)

      // 更新本地缓存
      this.importedMusic = this.importedMusic.filter(music => music.id !== musicId)
    } catch (error) {
      console.error('删除音乐失败:', error)
      throw error
    }
  }

  // 缓存在线音乐
  async cacheOnlineMusic(
    music: MusicItem,
    onProgress?: (loaded: number, total: number) => void
  ): Promise<StoredMusicItem> {
    try {
      return await MusicStorageService.cacheOnlineMusic(music, onProgress)
    } catch (error) {
      console.error('缓存音乐失败:', error)
      throw error
    }
  }

  // 验证音乐文件格式
  private isValidMusicFile(file: File): boolean {
    const extension = '.' + file.name.split('.').pop()?.toLowerCase()
    return IMPORT_OPTIONS.acceptedFormats.includes(extension)
  }

  // 获取导入选项
  getImportOptions(): ImportMusicOptions {
    return { ...IMPORT_OPTIONS }
  }
}

// 创建单例实例
export const musicService = new MusicService()
