import { useEffect, type ReactNode } from 'react'
import { Outlet } from 'react-router'
import { useAuth } from '@/contexts/auth-context'
import { RoleStoreProvider } from '@/stores/role-store'
import { LoadingOverlay } from '@/components/common/loading'
import { useSmartNavigation } from '@/lib/navigation'

interface AuthedLayoutProps {
  children?: ReactNode
}

/**
 * 鉴权布局
 * 用于处理登录状态检查和重定向
 * 在用户认证成功后提供角色状态管理
 */
export function AuthedLayout({ children }: AuthedLayoutProps) {
  const { status, user, session } = useAuth()
  const { smartNavigate } = useSmartNavigation()

  useEffect(() => {
    console.log('AuthedLayout-当前认证状态:', status)
  }, [status, user, session])

  // 重定向未登录用户到登录页面
  useEffect(() => {
    if (status === 'unauthenticated') {
      console.log('AuthedLayout-检测到未认证状态，重定向到登录页面')
      smartNavigate('/login')
    } else if (status === 'authenticated') {
      console.log('AuthedLayout-认证成功，用户:', user?.email)
    }
  }, [status, smartNavigate, user])

  // 如果正在加载会话信息，显示加载状态
  if (status === 'loading') {
    console.log('AuthedLayout-显示加载状态')
    return <LoadingOverlay show />
  }

  // 如果未登录，不显示内容
  if (status === 'unauthenticated') {
    console.log('AuthedLayout-未认证，不显示内容')
    return null
  }

  // 认证成功后，提供角色状态管理并渲染内容
  const content = children ? <>{children}</> : <Outlet />

  return <RoleStoreProvider>{content}</RoleStoreProvider>
}
