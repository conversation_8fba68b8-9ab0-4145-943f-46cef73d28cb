import { useState, useCallback, useRef, useEffect } from 'react'
import { addToast } from '@heroui/react'
import { waveformStorageDexie } from '@/services/waveform-storage-dexie'
import type {
  RecordingPoint,
  WaveformRecording,
  RecordingState,
  RecordingActions
} from '@/types/recording'

export interface UseRecordingOptions {
  onSaveSuccess?: (waveform: import('@/types/swipe-mode').WaveformLibraryItem) => void
  waveformData?: ReturnType<typeof import('./useWaveformData').useWaveformData>
}

export function useRecording(options: UseRecordingOptions = {}) {
  const { onSaveSuccess, waveformData } = options

  // 录制相关的引用
  const recordingStartTimeRef = useRef<number>(0)
  const recordingPointsRef = useRef<RecordingPoint[]>([])
  const recordingTimerRef = useRef<NodeJS.Timeout | null>(null)
  const canvasSizeRef = useRef<{ width: number; height: number }>({ width: 0, height: 0 })
  const lastPositionRef = useRef<{ x: number; y: number } | null>(null)
  const touchCountRef = useRef<number>(0)
  const totalDistanceRef = useRef<number>(0)

  // 新增：用于跟踪强度变化的ref
  const currentIntensityRef = useRef<number>(0)
  const lastBroadcastIntensityRef = useRef<number>(-1)

  const [isSaving, setIsSaving] = useState(false)

  // 录制状态
  const [recordingState, setRecordingState] = useState<RecordingState>({
    isRecording: false,
    hasStarted: false,
    currentIntensity: 0,
    duration: 0,
    hasUnsavedData: false,
    touchCount: 0,
    maxIntensity: 0,
    totalDistance: 0,
    canvasSize: { width: 0, height: 0 }
  })

  // 初始化画布尺寸
  const initializeCanvas = useCallback((width: number, height: number) => {
    canvasSizeRef.current = { width, height }
    setRecordingState(prev => ({
      ...prev,
      canvasSize: { width, height }
    }))
  }, [])

  // 开始录制（第一次触摸时调用）
  const startRecording = useCallback(() => {
    if (recordingState.hasStarted) return

    const now = Date.now()
    recordingStartTimeRef.current = now
    recordingPointsRef.current = []
    touchCountRef.current = 0
    totalDistanceRef.current = 0
    currentIntensityRef.current = 0
    lastBroadcastIntensityRef.current = -1

    console.log('🎬 录制开始')

    setRecordingState(prev => ({
      ...prev,
      isRecording: true,
      hasStarted: true,
      duration: 0,
      maxIntensity: 0,
      touchCount: 0,
      totalDistance: 0
    }))

    // 启动定时器，每50ms记录一次状态
    recordingTimerRef.current = setInterval(() => {
      const currentTime = Date.now() - recordingStartTimeRef.current
      const intensity = currentIntensityRef.current
      const position = lastPositionRef.current

      // 创建录制点
      const point: RecordingPoint =
        position && intensity > 0
          ? [
              currentTime,
              intensity,
              Math.round((position.x / canvasSizeRef.current.width) * 1000) / 1000,
              Math.round((position.y / canvasSizeRef.current.height) * 1000) / 1000,
              1
            ]
          : [currentTime, intensity]

      recordingPointsRef.current.push(point)

      // 更新状态
      setRecordingState(prev => ({
        ...prev,
        duration: currentTime,
        hasUnsavedData: currentTime > 1000
      }))

      // 只在强度真正变化时才广播
      if (intensity !== lastBroadcastIntensityRef.current) {
        waveformData?.addDataPoint(intensity)
        lastBroadcastIntensityRef.current = intensity
        console.log('📡 广播强度变化:', intensity, intensity === 0 ? '(停顿)' : '(活动)')
      }
    }, 50)
  }, [recordingState.hasStarted, waveformData])

  // 更新触摸位置
  const updateTouch = useCallback(
    (x: number, y: number, pressure: number) => {
      // 计算距离
      if (lastPositionRef.current) {
        const distance = Math.sqrt(
          Math.pow(x - lastPositionRef.current.x, 2) + Math.pow(y - lastPositionRef.current.y, 2)
        )
        totalDistanceRef.current += distance

        setRecordingState(prev => ({
          ...prev,
          totalDistance: totalDistanceRef.current
        }))
      }

      lastPositionRef.current = { x, y }

      // 如果还没开始录制，现在开始
      if (!recordingState.hasStarted) {
        touchCountRef.current = 1
        setRecordingState(prev => ({
          ...prev,
          touchCount: 1
        }))
        startRecording()
      }
    },
    [recordingState.hasStarted, startRecording]
  )

  // 更新强度
  const updateIntensity = useCallback((intensity: number) => {
    currentIntensityRef.current = intensity
    setRecordingState(prev => ({
      ...prev,
      currentIntensity: intensity,
      maxIntensity: Math.max(prev.maxIntensity, intensity)
    }))
  }, [])

  // 结束触摸
  const endTouch = useCallback(() => {
    lastPositionRef.current = null

    // 立即将强度设为0，确保停顿被正确记录
    currentIntensityRef.current = 0
    setRecordingState(prev => ({
      ...prev,
      currentIntensity: 0
    }))

    // 立即广播强度变化为0，确保波形显示停顿
    if (lastBroadcastIntensityRef.current !== 0) {
      waveformData?.addDataPoint(0)
      lastBroadcastIntensityRef.current = 0
      console.log('📡 广播停顿（强度归零）')
    }
  }, [waveformData])

  // 停止录制并保存
  const stopRecording = useCallback(async (): Promise<WaveformRecording | null> => {
    if (!recordingState.isRecording || !recordingState.hasUnsavedData) {
      addToast({
        title: '无法保存',
        description: '录制时间太短，请至少录制1秒',
        color: 'warning'
      })
      return null
    }

    try {
      setIsSaving(true)

      // 停止定时器
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current)
        recordingTimerRef.current = null
      }

      const endTime = Date.now()
      const duration = endTime - recordingStartTimeRef.current

      // 创建录制数据
      const recording: WaveformRecording = {
        id: `recording_${Date.now()}`,
        name: `轨迹 ${new Date().toLocaleTimeString()}`,
        startTime: recordingStartTimeRef.current,
        endTime,
        duration,
        points: [...recordingPointsRef.current], // 复制数组
        canvasSize: [canvasSizeRef.current.width, canvasSizeRef.current.height],
        metadata: [
          recordingState.maxIntensity,
          recordingState.maxIntensity > 0 ? recordingState.maxIntensity / 2 : 0, // 简单的平均值
          touchCountRef.current,
          totalDistanceRef.current
        ]
      }

      console.log('💾 录制数据:', {
        duration: duration + 'ms',
        points: recording.points.length,
        size: JSON.stringify(recording).length + ' bytes'
      })

      // 保存到存储服务
      await waveformStorageDexie.initialize()
      const waveformItem = await waveformStorageDexie.saveRecording(recording, false)

      // 更新状态
      setRecordingState(prev => ({
        ...prev,
        isRecording: false,
        currentIntensity: 0
      }))

      // 调用成功回调
      onSaveSuccess?.(waveformItem)
      return recording
    } catch (error) {
      console.error('保存录制失败:', error)
      addToast({
        title: '保存失败',
        description: '录制保存时发生错误',
        color: 'danger'
      })
      return null
    } finally {
      setIsSaving(false)
    }
  }, [
    recordingState.isRecording,
    recordingState.hasUnsavedData,
    recordingState.maxIntensity,
    onSaveSuccess
  ])

  // 重置录制
  const resetRecording = useCallback(() => {
    // 清理定时器
    if (recordingTimerRef.current) {
      clearInterval(recordingTimerRef.current)
      recordingTimerRef.current = null
    }

    // 重置所有状态
    recordingStartTimeRef.current = 0
    recordingPointsRef.current = []
    lastPositionRef.current = null
    touchCountRef.current = 0
    totalDistanceRef.current = 0
    currentIntensityRef.current = 0
    lastBroadcastIntensityRef.current = -1

    setRecordingState({
      isRecording: false,
      hasStarted: false,
      currentIntensity: 0,
      duration: 0,
      hasUnsavedData: false,
      touchCount: 0,
      maxIntensity: 0,
      totalDistance: 0,
      canvasSize: canvasSizeRef.current
    })

    setIsSaving(false)
    waveformData?.clearData()

    console.log('🔄 录制已重置')
  }, [waveformData])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current)
      }
    }
  }, [])

  const actions: RecordingActions = {
    startRecording,
    stopRecording,
    resetRecording,
    updateTouch,
    updateIntensity,
    endTouch
  }

  return {
    recordingState,
    actions,
    isSaving,
    initializeCanvas
  }
}
