import { useNavigate, useLocation } from 'react-router'
import { useCallback } from 'react'
import {
  getNavigationStrategy,
  cleanPath,
  isValidPath,
  getParentPath,
  isTabPage
} from '@/lib/navigation/utils'
import { NavigationStrategy, type SmartNavigationOptions } from '@/lib/navigation/types'

/**
 * 智能导航 Hook
 * 提供统一的路由跳转接口，自动选择合适的导航策略
 */
export function useSmartNavigation() {
  const navigate = useNavigate()
  const location = useLocation()

  /**
   * 智能导航函数
   * @param to 目标路径或返回步数（负数）
   * @param options 导航选项
   */
  const smartNavigate = useCallback(
    (to: string | number, options: SmartNavigationOptions = {}) => {
      const currentPath = cleanPath(location.pathname)

      // 处理返回操作
      if (typeof to === 'number') {
        handleBackNavigation(to, options)
        return
      }

      const targetPath = cleanPath(to)

      // 验证目标路径
      if (!isValidPath(targetPath)) {
        console.warn(`[SmartNavigation] 无效的路径: ${targetPath}`)
        return
      }

      // 如果目标路径与当前路径相同，不进行导航
      if (targetPath === currentPath) {
        return
      }

      // 获取导航策略
      const strategy = getNavigationStrategy(currentPath, targetPath, options)

      // 执行导航
      executeNavigation(targetPath, strategy, options)

      // 调试日志
      console.log(`[SmartNavigation] ${currentPath} -> ${targetPath} (${strategy})`)
    },
    [location.pathname, navigate]
  )

  /**
   * 处理返回导航
   */
  const handleBackNavigation = useCallback(
    (steps: number, options: SmartNavigationOptions) => {
      const currentPath = cleanPath(location.pathname)

      // 如果是标签页，不允许返回，应该显示退出确认
      if (isTabPage(currentPath)) {
        console.log(`[SmartNavigation] 标签页不允许返回: ${currentPath}`)
        return
      }

      // 检查是否有历史记录可以返回
      if (window.history.length <= 1) {
        // 没有历史记录，返回到父级标签页
        const parentPath = getParentPath(currentPath)
        if (parentPath) {
          smartNavigate(parentPath, { replace: true })
          return
        }
      }

      // 执行返回操作
      navigate(steps)
      console.log(`[SmartNavigation] 返回 ${Math.abs(steps)} 步: ${currentPath}`)
    },
    [location.pathname, navigate]
  )

  /**
   * 执行具体的导航操作
   */
  const executeNavigation = useCallback(
    (targetPath: string, strategy: NavigationStrategy, options: SmartNavigationOptions) => {
      const navigateOptions = { ...options }

      switch (strategy) {
        case NavigationStrategy.REPLACE:
          navigateOptions.replace = true
          navigate(targetPath, navigateOptions)
          break

        case NavigationStrategy.MODAL:
          navigateOptions.state = {
            ...navigateOptions.state,
            isModal: true
          }
          navigate(targetPath, navigateOptions)
          break

        case NavigationStrategy.PUSH:
        default:
          navigate(targetPath, navigateOptions)
          break
      }
    },
    [navigate]
  )

  /**
   * 返回到指定的父级页面
   * 如果父级页面是标签页，使用 replace 导航
   */
  const navigateToParent = useCallback(
    (parentPath?: string) => {
      const currentPath = cleanPath(location.pathname)
      const targetParent = parentPath || getParentPath(currentPath)

      if (targetParent) {
        smartNavigate(targetParent, { replace: isTabPage(targetParent) })
      }
    },
    [location.pathname, smartNavigate]
  )

  /**
   * 替换当前页面（不产生历史记录）
   */
  const replaceNavigation = useCallback(
    (to: string, options: SmartNavigationOptions = {}) => {
      smartNavigate(to, { ...options, replace: true, force: true })
    },
    [smartNavigate]
  )

  /**
   * 强制使用 push 导航（产生历史记录）
   */
  const pushNavigation = useCallback(
    (to: string, options: SmartNavigationOptions = {}) => {
      smartNavigate(to, { ...options, replace: false, force: true })
    },
    [smartNavigate]
  )

  /**
   * 返回上一页，如果没有历史记录则返回父级页面
   */
  const goBack = useCallback(() => {
    smartNavigate(-1)
  }, [smartNavigate])

  return {
    // 主要接口
    smartNavigate,

    // 便捷方法
    navigateToParent,
    replaceNavigation,
    pushNavigation,
    goBack,

    // 当前路径信息
    currentPath: cleanPath(location.pathname),
    currentPageType: location.state?.pageType,
    isModal: location.state?.isModal || false
  }
}
