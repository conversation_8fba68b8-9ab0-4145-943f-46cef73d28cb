import { useRef, useCallback, useEffect, useState } from 'react'

interface UseSmartScrollOptions {
  /**
   * 是否自动滚动到底部
   * @default true
   */
  autoScroll?: boolean
  /**
   * 滚动阈值，当用户滚动到距离底部多少像素内时认为在底部
   * @default 100
   */
  threshold?: number
  /**
   * 流式输出时的滚动间隔（毫秒）
   * @default 1000
   */
  streamingScrollInterval?: number
}

export function useSmartScroll(options: UseSmartScrollOptions = {}) {
  const { autoScroll = true, threshold = 100, streamingScrollInterval = 1000 } = options

  const containerRef = useRef<HTMLDivElement>(null)
  const endRef = useRef<HTMLDivElement>(null)
  const [isUserScrolling, setIsUserScrolling] = useState(false)
  const [isNearBottom, setIsNearBottom] = useState(true)

  const scrollTimeoutRef = useRef<NodeJS.Timeout>()
  const streamingIntervalRef = useRef<NodeJS.Timeout>()

  // 检查是否接近底部
  const checkIfNearBottom = useCallback(() => {
    if (!containerRef.current) return false

    const { scrollTop, scrollHeight, clientHeight } = containerRef.current
    const distanceFromBottom = scrollHeight - scrollTop - clientHeight
    return distanceFromBottom <= threshold
  }, [threshold])

  // 平滑滚动到底部
  const scrollToBottom = useCallback(
    (smooth = true) => {
      if (!endRef.current || !autoScroll) return

      // 如果用户正在主动滚动且不在底部，不要自动滚动
      if (isUserScrolling && !isNearBottom) return

      endRef.current.scrollIntoView({
        behavior: smooth ? 'smooth' : 'instant',
        block: 'end'
      })
    },
    [autoScroll, isUserScrolling, isNearBottom]
  )

  // 立即滚动到底部（无动画）
  const scrollToBottomInstant = useCallback(() => {
    scrollToBottom(false)
  }, [scrollToBottom])

  // 处理滚动事件
  const handleScroll = useCallback(() => {
    if (!containerRef.current) return

    // 检查是否接近底部
    const nearBottom = checkIfNearBottom()
    setIsNearBottom(nearBottom)

    // 设置用户正在滚动的状态
    setIsUserScrolling(true)

    // 清除之前的超时
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current)
    }

    // 500ms 后认为用户停止滚动
    scrollTimeoutRef.current = setTimeout(() => {
      setIsUserScrolling(false)
    }, 500)
  }, [checkIfNearBottom])

  // 开始流式滚动
  const startStreamingScroll = useCallback(() => {
    if (streamingIntervalRef.current) {
      clearInterval(streamingIntervalRef.current)
    }

    streamingIntervalRef.current = setInterval(() => {
      // 只有当用户在底部附近且没有主动滚动时才自动滚动
      if (isNearBottom && !isUserScrolling) {
        scrollToBottom(true)
      }
    }, streamingScrollInterval)
  }, [isNearBottom, isUserScrolling, scrollToBottom, streamingScrollInterval])

  // 停止流式滚动
  const stopStreamingScroll = useCallback(() => {
    if (streamingIntervalRef.current) {
      clearInterval(streamingIntervalRef.current)
      streamingIntervalRef.current = undefined
    }
  }, [])

  // 强制滚动到底部（忽略用户滚动状态）
  const forceScrollToBottom = useCallback((smooth = true) => {
    if (!endRef.current) return

    endRef.current.scrollIntoView({
      behavior: smooth ? 'smooth' : 'instant',
      block: 'end'
    })

    // 更新状态
    setIsNearBottom(true)
  }, [])

  // 监听容器滚动
  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    container.addEventListener('scroll', handleScroll, { passive: true })

    return () => {
      container.removeEventListener('scroll', handleScroll)
    }
  }, [handleScroll])

  // 监听键盘弹出（通过 resize 事件）
  useEffect(() => {
    let initialHeight = window.innerHeight
    let isKeyboardEvent = false

    const handleResize = () => {
      const currentHeight = window.innerHeight
      const heightDiff = initialHeight - currentHeight

      // 检测键盘弹出（高度减少超过 150px）
      if (heightDiff > 150) {
        isKeyboardEvent = true
        // 键盘弹出时，延迟滚动确保输入框可见
        setTimeout(() => {
          forceScrollToBottom(true)
        }, 300)
      } else if (isKeyboardEvent && heightDiff < 50) {
        // 键盘收起
        isKeyboardEvent = false
        initialHeight = currentHeight
      }
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [forceScrollToBottom])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current)
      }
      if (streamingIntervalRef.current) {
        clearInterval(streamingIntervalRef.current)
      }
    }
  }, [])

  return {
    containerRef,
    endRef,
    isNearBottom,
    isUserScrolling,
    scrollToBottom,
    scrollToBottomInstant,
    forceScrollToBottom,
    startStreamingScroll,
    stopStreamingScroll
  }
}
