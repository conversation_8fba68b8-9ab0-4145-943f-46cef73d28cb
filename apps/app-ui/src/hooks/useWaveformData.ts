import { useRef, useCallback, useMemo } from 'react'
import {
  BroadcastStrategyManager,
  BroadcastPresets,
  type BroadcastDataPoint
} from '@/utils/broadcast/broadcast-strategy'
import { RemoteBroadcastStrategyManager } from '@/utils/broadcast/remote-broadcast-strategy'
import type { WaveformDataPoint } from '@/components/device/waveform/WaveformViewer'

export interface UseWaveformDataOptions {
  mode?: 'swipe' | 'music' | 'video' | 'performance'
  maxPoints?: number
  isRemoteMode?: boolean // 新增：是否为远程模式
}

export const useWaveformData = (options: UseWaveformDataOptions = {}) => {
  const { mode = 'swipe', maxPoints = 1000, isRemoteMode = false } = options

  const broadcastStrategyRef = useRef<BroadcastStrategyManager | null>(null)

  // 初始化广播策略
  const initializeBroadcastStrategy = useCallback(() => {
    if (!broadcastStrategyRef.current) {
      const preset = BroadcastPresets[mode]
      // 根据是否为远程模式选择不同的策略管理器
      broadcastStrategyRef.current = isRemoteMode
        ? new RemoteBroadcastStrategyManager(preset)
        : new BroadcastStrategyManager(preset)
    }
    return broadcastStrategyRef.current
  }, [mode, isRemoteMode])

  // 添加数据点
  const addDataPoint = useCallback(
    (intensity: number) => {
      const strategy = initializeBroadcastStrategy()
      strategy.addDataPoint({
        intensity,
        timestamp: Date.now()
      })
    },
    [initializeBroadcastStrategy]
  )

  // 获取波形数据
  const getWaveformData = useCallback((): WaveformDataPoint[] => {
    const strategy = broadcastStrategyRef.current
    if (!strategy) return []

    const historyData = strategy.getCompressedHistoryData(maxPoints)
    return historyData.map((point: BroadcastDataPoint) => ({
      intensity: point.intensity,
      timestamp: point.timestamp
    }))
  }, [maxPoints])

  // 清除数据
  const clearData = useCallback(() => {
    const strategy = broadcastStrategyRef.current
    if (strategy) {
      strategy.clear()
    }
  }, [])

  // 获取性能统计
  const getPerformanceStats = useCallback(() => {
    const strategy = broadcastStrategyRef.current
    if (!strategy) return null

    return strategy.getPerformanceStats()
  }, [])

  // 检查蓝牙状态
  const isBluetoothReady = useCallback(() => {
    const strategy = broadcastStrategyRef.current
    if (!strategy) return false

    return strategy.isBluetoothReady()
  }, [])

  // 重新初始化蓝牙
  const reinitializeBluetooth = useCallback(async () => {
    const strategy = broadcastStrategyRef.current
    if (strategy) {
      await strategy.reinitializeBluetooth()
    }
  }, [])

  // 获取配置
  const getConfig = useCallback(() => {
    const strategy = broadcastStrategyRef.current
    if (!strategy) return null

    return strategy.getConfig()
  }, [])

  // 更新配置
  const updateConfig = useCallback((newConfig: any) => {
    const strategy = broadcastStrategyRef.current
    if (strategy) {
      strategy.updateConfig(newConfig)
    }
  }, [])

  // 返回的API
  return useMemo(
    () => ({
      // 数据操作
      addDataPoint,
      getWaveformData,
      clearData,

      // 蓝牙相关
      isBluetoothReady,
      reinitializeBluetooth,

      // 配置相关
      getConfig,
      updateConfig,

      // 性能统计
      getPerformanceStats,

      // 内部引用（仅在需要时使用）
      broadcastStrategyRef
    }),
    [
      addDataPoint,
      getWaveformData,
      clearData,
      isBluetoothReady,
      reinitializeBluetooth,
      getConfig,
      updateConfig,
      getPerformanceStats
    ]
  )
}
