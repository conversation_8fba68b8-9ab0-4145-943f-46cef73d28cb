import { useEffect, useState, useCallback } from 'react'
import { useLocation } from 'react-router'
import { Capacitor } from '@capacitor/core'
import { App as CapacitorApp } from '@capacitor/app'
import { useSmartNavigation } from './use-smart-navigation'
import { isTabPage, isSwipeDisabled, cleanPath, getParentPath } from '@/lib/navigation/utils'

/**
 * 智能返回 Hook
 * 处理左滑返回、硬件返回按钮和退出确认
 */
export function useSmartBack() {
  const location = useLocation()
  const { smartNavigate, currentPath } = useSmartNavigation()
  const [showExitConfirm, setShowExitConfirm] = useState(false)

  // 路径历史记录，用于判断导航类型
  const [pathHistory, setPathHistory] = useState<string[]>([])

  // 更新路径历史
  useEffect(() => {
    const cleanedPath = cleanPath(location.pathname)
    setPathHistory(prev => {
      // 避免重复添加相同路径
      if (prev.length === 0 || prev[prev.length - 1] !== cleanedPath) {
        const newHistory = [...prev, cleanedPath]
        // 限制历史记录长度
        return newHistory.length > 20 ? newHistory.slice(-20) : newHistory
      }
      return prev
    })
  }, [location.pathname])

  /**
   * 处理返回操作的核心逻辑
   */
  const handleBackAction = useCallback(() => {
    const currentCleanPath = cleanPath(location.pathname)

    console.log(`[SmartBack] 处理返回操作: ${currentCleanPath}`)
    console.log(`[SmartBack] 路径历史:`, pathHistory)

    // 1. 如果是标签页，显示退出确认
    if (isTabPage(currentCleanPath)) {
      console.log(`[SmartBack] 标签页返回，显示退出确认`)
      setShowExitConfirm(true)
      return
    }

    // 2. 如果是模态页面，直接关闭
    if (location.state?.isModal) {
      console.log(`[SmartBack] 模态页面返回`)
      smartNavigate(-1)
      return
    }

    // 3. 检查是否有有效的历史记录可以返回
    if (pathHistory.length > 1) {
      const previousPath = pathHistory[pathHistory.length - 2]
      const currentPagePath = pathHistory[pathHistory.length - 1]

      // 确保当前路径匹配
      if (currentPagePath === currentCleanPath) {
        console.log(`[SmartBack] 返回到历史页面: ${previousPath}`)
        smartNavigate(-1)
        return
      }
    }

    // 4. 没有有效历史记录，返回到父级页面
    const parentPath = getParentPath(currentCleanPath)
    if (parentPath) {
      console.log(`[SmartBack] 返回到父级页面: ${parentPath}`)
      smartNavigate(parentPath, { replace: true })
      return
    }

    // 5. 无法确定返回目标，显示退出确认
    console.log(`[SmartBack] 无法确定返回目标，显示退出确认`)
    setShowExitConfirm(true)
  }, [location.pathname, location.state, pathHistory, smartNavigate])

  /**
   * 检查是否可以执行返回操作
   */
  const canGoBack = useCallback((): boolean => {
    const currentCleanPath = cleanPath(location.pathname)

    // 标签页不能返回
    if (isTabPage(currentCleanPath)) {
      return false
    }

    // 禁用左滑的页面不能返回
    if (isSwipeDisabled(currentCleanPath)) {
      return false
    }

    // 有历史记录或有父级页面
    return pathHistory.length > 1 || !!getParentPath(currentCleanPath)
  }, [location.pathname, pathHistory])

  /**
   * 处理硬件返回按钮（Android）
   */
  useEffect(() => {
    if (!Capacitor.isNativePlatform()) {
      return
    }

    const handleHardwareBack = ({ canGoBack: capacitorCanGoBack }: { canGoBack: boolean }) => {
      console.log(`[SmartBack] 硬件返回按钮, canGoBack: ${capacitorCanGoBack}`)

      if (capacitorCanGoBack && canGoBack()) {
        handleBackAction()
      } else {
        setShowExitConfirm(true)
      }
    }

    // 添加监听器
    CapacitorApp.addListener('backButton', handleHardwareBack)

    // 清理监听器
    return () => {
      CapacitorApp.removeAllListeners()
    }
  }, [handleBackAction, canGoBack])

  /**
   * 处理左滑返回（iOS）
   * 注意：这里只是提供逻辑，实际的左滑手势需要在具体组件中实现
   */
  const handleSwipeBack = useCallback(() => {
    const currentCleanPath = cleanPath(location.pathname)

    // 检查是否禁用左滑
    if (isSwipeDisabled(currentCleanPath)) {
      console.log(`[SmartBack] 左滑返回被禁用: ${currentCleanPath}`)
      return false
    }

    // 标签页不响应左滑返回
    if (isTabPage(currentCleanPath)) {
      console.log(`[SmartBack] 标签页不响应左滑返回: ${currentCleanPath}`)
      return false
    }

    // 执行返回操作
    handleBackAction()
    return true
  }, [location.pathname, handleBackAction])

  /**
   * 退出应用
   */
  const handleExitApp = useCallback(() => {
    if (Capacitor.isNativePlatform()) {
      CapacitorApp.exitApp()
    } else {
      // Web 环境下关闭窗口
      window.close()
    }
    setShowExitConfirm(false)
  }, [])

  /**
   * 取消退出确认
   */
  const cancelExit = useCallback(() => {
    setShowExitConfirm(false)
  }, [])

  /**
   * 清除路径历史（用于重置状态）
   */
  const clearPathHistory = useCallback(() => {
    setPathHistory([currentPath])
  }, [currentPath])

  return {
    // 主要方法
    handleBackAction,
    handleSwipeBack,
    canGoBack: canGoBack(),

    // 退出确认相关
    showExitConfirm,
    handleExitApp,
    cancelExit,

    // 工具方法
    clearPathHistory,

    // 状态信息
    pathHistory,
    currentPath,
    isTabPage: isTabPage(currentPath),
    isSwipeDisabled: isSwipeDisabled(currentPath)
  }
}
