import { useState, useCallback, useEffect, useRef } from 'react'
import { addToast } from '@heroui/react'
import { PlaybackStatus } from '@/types/recording'
import type {
  WaveformRecording,
  PlaybackState,
  PlaybackActions,
  RecordingPoint
} from '@/types/recording'

export interface UsePlaybackOptions {
  recordingId: string
  waveformData?: ReturnType<typeof import('./useWaveformData').useWaveformData>
  onComplete?: () => void
  onError?: (error: string) => void
  loadRecording?: (id: string) => Promise<WaveformRecording | null>
}

export function usePlayback(options: UsePlaybackOptions) {
  const { recordingId, waveformData, onComplete, onError, loadRecording } = options

  // 使用 ref 保存函数引用，避免依赖数组问题
  const loadRecordingRef = useRef(loadRecording)
  const onErrorRef = useRef(onError)

  // 更新 ref
  useEffect(() => {
    loadRecordingRef.current = loadRecording
    onErrorRef.current = onError
  })

  // 回放相关引用
  const playbackTimerRef = useRef<NodeJS.Timeout | null>(null)
  const playbackStartTimeRef = useRef<number>(0)
  const currentPointIndexRef = useRef<number>(0)
  const canvasSizeRef = useRef<{ width: number; height: number }>({ width: 0, height: 0 })

  const [playbackState, setPlaybackState] = useState<PlaybackState>({
    status: PlaybackStatus.LOADING,
    recording: null,
    currentTime: 0,
    currentIntensity: 0,
    currentPosition: null,
    progress: 0
  })

  // 加载录制数据
  const loadRecordingData = useCallback(async () => {
    if (!recordingId || !loadRecordingRef.current) return

    try {
      setPlaybackState(prev => ({
        ...prev,
        status: PlaybackStatus.LOADING
      }))

      const recording = await loadRecordingRef.current(recordingId)

      if (recording) {
        setPlaybackState(prev => ({
          ...prev,
          status: PlaybackStatus.READY,
          recording,
          currentTime: 0,
          currentIntensity: 0,
          currentPosition: null,
          progress: 0
        }))

        // 重建波形数据
        if (waveformData) {
          waveformData.clearData()
          // 从录制数据重建完整波形
          recording.points.forEach(point => {
            const [, intensity] = point
            waveformData.addDataPoint(intensity)
          })
        }

        console.log('📼 录制数据加载完成:', {
          duration: recording.duration + 'ms',
          points: recording.points.length
        })
      } else {
        throw new Error('Recording not found')
      }
    } catch (error) {
      const errorMessage = '无法找到指定的录制数据'
      console.error('Failed to load recording:', error)
      setPlaybackState(prev => ({
        ...prev,
        status: PlaybackStatus.ERROR,
        error: errorMessage
      }))
      onErrorRef.current?.(errorMessage)
      addToast({
        title: '加载失败',
        description: errorMessage,
        color: 'danger'
      })
    }
  }, [recordingId])

  // 初始化画布尺寸
  const initializeCanvas = useCallback((width: number, height: number) => {
    canvasSizeRef.current = { width, height }
  }, [])

  // 开始回放
  const start = useCallback(() => {
    if (!playbackState.recording || playbackState.status !== PlaybackStatus.READY) return

    const recording = playbackState.recording
    playbackStartTimeRef.current = Date.now()
    currentPointIndexRef.current = 0

    console.log('▶️ 开始回放')

    setPlaybackState(prev => ({
      ...prev,
      status: PlaybackStatus.PLAYING,
      currentTime: 0,
      progress: 0
    }))

    // 波形数据已在加载时重建，不需要再次清空

    // 启动回放定时器，每10ms检查一次
    playbackTimerRef.current = setInterval(() => {
      const elapsedTime = Date.now() - playbackStartTimeRef.current
      const points = recording.points
      let hasFoundPoint = false
      let currentIntensity = 0
      let currentPosition: { x: number; y: number } | null = null

      // 找到当前时间应该播放的所有点
      while (
        currentPointIndexRef.current < points.length &&
        points[currentPointIndexRef.current][0] <= elapsedTime // timestamp
      ) {
        const point = points[currentPointIndexRef.current]
        const [timestamp, intensity, x, y] = point

        hasFoundPoint = true
        currentIntensity = intensity

        // 更新位置信息
        currentPosition =
          x !== undefined && y !== undefined
            ? {
                x: x * canvasSizeRef.current.width,
                y: y * canvasSizeRef.current.height
              }
            : null

        currentPointIndexRef.current++
      }

      // 如果没有找到新的点，但还在录制时间范围内，保持当前状态
      if (!hasFoundPoint && currentPointIndexRef.current > 0 && elapsedTime < recording.duration) {
        // 获取最后一个已播放点的强度
        if (currentPointIndexRef.current - 1 >= 0) {
          const lastPoint = points[currentPointIndexRef.current - 1]
          currentIntensity = lastPoint[1] // 使用上一个点的强度
        }
      }

      // 更新播放状态
      setPlaybackState(prev => ({
        ...prev,
        currentTime: elapsedTime,
        currentIntensity: currentIntensity,
        currentPosition: currentPosition,
        progress: Math.min(elapsedTime / recording.duration, 1)
      }))

      // 始终广播当前强度（包括0强度的停顿期间）
      waveformData?.addDataPoint(currentIntensity)

      // 检查是否播放完成
      if (elapsedTime >= recording.duration) {
        // 停止定时器
        if (playbackTimerRef.current) {
          clearInterval(playbackTimerRef.current)
          playbackTimerRef.current = null
        }

        setPlaybackState(prev => ({
          ...prev,
          status: PlaybackStatus.COMPLETED,
          progress: 1,
          currentIntensity: 0 // 播放结束时强度归零
        }))

        // 最后广播一次0强度
        waveformData?.addDataPoint(0)

        console.log('✅ 回放完成')
        onComplete?.()

        addToast({
          title: '播放完成',
          description: '录制回放已结束',
          color: 'success'
        })
      }
    }, 10) // 10ms精度
  }, [playbackState.recording, playbackState.status, waveformData, onComplete])

  // 重新开始回放
  const restart = useCallback(() => {
    if (!playbackState.recording) return

    // 停止当前回放
    if (playbackTimerRef.current) {
      clearInterval(playbackTimerRef.current)
      playbackTimerRef.current = null
    }

    // 重置状态
    setPlaybackState(prev => ({
      ...prev,
      status: PlaybackStatus.READY,
      currentTime: 0,
      currentIntensity: 0,
      currentPosition: null,
      progress: 0
    }))

    // 重置波形显示
    waveformData?.clearData()

    // 重建完整波形数据（用于预览）
    if (playbackState.recording) {
      playbackState.recording.points.forEach(point => {
        const [, intensity] = point
        waveformData?.addDataPoint(intensity)
      })
    }

    console.log('🔄 重置回放')

    // 立即开始新的回放
    setTimeout(() => start(), 100)
  }, [playbackState.recording, start])

  // 停止回放
  const stop = useCallback(() => {
    if (playbackTimerRef.current) {
      clearInterval(playbackTimerRef.current)
      playbackTimerRef.current = null
    }

    setPlaybackState(prev => ({
      ...prev,
      status: prev.recording ? PlaybackStatus.READY : PlaybackStatus.LOADING,
      currentTime: 0,
      currentIntensity: 0,
      currentPosition: null,
      progress: 0
    }))

    console.log('⏹️ 停止回放')
  }, [])

  // 组件挂载时加载录制数据
  useEffect(() => {
    if (waveformData && recordingId) {
      loadRecordingData()
    }
  }, [loadRecordingData, waveformData, recordingId])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (playbackTimerRef.current) {
        clearInterval(playbackTimerRef.current)
      }
    }
  }, [])

  const actions: PlaybackActions = {
    start,
    restart,
    stop
  }

  return {
    playbackState,
    actions,
    isLoading: playbackState.status === PlaybackStatus.LOADING,
    isReady: playbackState.status === PlaybackStatus.READY,
    isPlaying: playbackState.status === PlaybackStatus.PLAYING,
    isCompleted: playbackState.status === PlaybackStatus.COMPLETED,
    hasError: playbackState.status === PlaybackStatus.ERROR,
    initializeCanvas
  }
}
