import React, { ReactNode } from 'react'
import { DeviceControlProvider, type DeviceControlContext } from './device-control-context'
import { useDeviceStore } from '../stores/device-store'
import { modes } from '@/utils/deviceModes'
import { commandQueueManager } from '@/utils/bluetooth/bluetoothUtils'
import { shortBroadcastModes } from '@/utils/deviceModes'

interface LocalDeviceControlProviderProps {
  children: ReactNode
}

export const LocalDeviceControlProvider: React.FC<LocalDeviceControlProviderProps> = ({
  children
}) => {
  const { connectedDevice } = useDeviceStore()

  // 本地控制实现
  const deviceControlValue: DeviceControlContext = {
    // 发送经典模式指令 - 调用现有的蓝牙广播逻辑
    sendClassicCommand: async (level: number) => {
      try {
        console.log('📡 [本地广播] 发送经典模式指令:', level)

        // 查找对应的模式命令（经典模式使用modes数组，id为4-9）
        const mode = modes.find(m => m.id === level)
        if (mode) {
          console.log(`发送模式命令: ${mode.command} (模式: ${mode.name}, id: ${mode.id})`)
          commandQueueManager.addCommands([mode.command])
        } else {
          console.warn(`未找到 id 为 ${level} 的模式命令`)
          throw new Error(`未找到模式 ${level}`)
        }
      } catch (error) {
        console.error('发送经典模式指令失败:', error)
        throw error
      }
    },

    // 发送划屏模式指令 - 调用现有的蓝牙广播逻辑
    sendSwipeCommand: async (intensity: number, direction?: string) => {
      try {
        console.log('📡 [本地广播] 发送划屏模式指令:', { intensity, direction })

        // 划屏模式使用shortBroadcastModes数组，根据强度选择指令
        if (intensity === 0) {
          // 强度为0时不发送指令，让设备自然停止
          console.log('📡 强度为0，跳过广播')
          return
        }

        // 将强度(1-9)映射到shortBroadcastModes数组索引(0-8)
        const modeIndex = Math.min(
          Math.max(Math.floor(intensity) - 1, 0),
          shortBroadcastModes.length - 1
        )
        const command = shortBroadcastModes[modeIndex]

        if (command) {
          console.log(`发送划屏指令: ${command} (强度: ${intensity}, 索引: ${modeIndex})`)
          commandQueueManager.addCommands([command])
        } else {
          console.warn(`未找到强度 ${intensity} 对应的划屏指令`)
        }
      } catch (error) {
        console.error('发送划屏模式指令失败:', error)
        throw error
      }
    },

    // 本地模式配置
    isRemoteMode: false,
    canSave: true,
    isConnected: !!connectedDevice
  }

  return <DeviceControlProvider value={deviceControlValue}>{children}</DeviceControlProvider>
}
