import React, { ReactNode } from 'react'
import { DeviceControlProvider, type DeviceControlContext } from './device-control-context'
import { useRemoteControlStore } from '../stores/remote-control-store'
import { remoteControlService } from '../api/services/remote-control'

interface RemoteDeviceControlProviderProps {
  children: ReactNode
}

export const RemoteDeviceControlProvider: React.FC<RemoteDeviceControlProviderProps> = ({
  children
}) => {
  const { controller } = useRemoteControlStore()

  // 远程控制实现
  const deviceControlValue: DeviceControlContext = {
    // 发送经典模式指令 - 通过Socket发送给被控端
    sendClassicCommand: async (level: number) => {
      try {
        console.log('🌐 [远程控制] 发送经典模式指令:', level)

        // 调用远程控制服务发送指令
        await remoteControlService.sendClassicCommand(level)

        console.log('✅ [远程控制] 经典模式指令发送成功')
      } catch (error) {
        console.error('❌ [远程控制] 发送经典模式指令失败:', error)
        throw error
      }
    },

    // 发送划屏模式指令 - 通过Socket发送给被控端
    sendSwipeCommand: async (intensity: number, direction?: string) => {
      try {
        console.log('🌐 [远程控制] 发送划屏模式指令:', { intensity, direction })

        // 调用远程控制服务发送指令
        await remoteControlService.sendSwipeCommand(intensity, direction || 'up')

        console.log('✅ [远程控制] 划屏模式指令发送成功')
      } catch (error) {
        console.error('❌ [远程控制] 发送划屏模式指令失败:', error)
        throw error
      }
    },

    // 远程模式配置
    isRemoteMode: true,
    canSave: false, // 远程模式禁用保存
    isConnected: controller.isConnected
  }

  return <DeviceControlProvider value={deviceControlValue}>{children}</DeviceControlProvider>
}
