import React, { createContext, useContext, ReactNode } from 'react'

// 设备控制接口定义
export interface DeviceControlContext {
  // 发送经典模式指令
  sendClassicCommand: (level: number) => Promise<void>

  // 发送划屏模式指令
  sendSwipeCommand: (intensity: number, direction?: string) => Promise<void>

  // 是否为远程模式
  isRemoteMode: boolean

  // 是否可以保存（远程模式禁用保存）
  canSave: boolean

  // 是否已连接设备
  isConnected: boolean
}

// 创建Context
const DeviceControlContext = createContext<DeviceControlContext | null>(null)

// Hook for using device control context
export const useDeviceControl = (): DeviceControlContext => {
  const context = useContext(DeviceControlContext)
  if (!context) {
    throw new Error('useDeviceControl must be used within a DeviceControlProvider')
  }
  return context
}

// Provider组件的Props类型
export interface DeviceControlProviderProps {
  children: ReactNode
  value: DeviceControlContext
}

// 通用Provider组件
export const DeviceControlProvider: React.FC<DeviceControlProviderProps> = ({
  children,
  value
}) => {
  return <DeviceControlContext.Provider value={value}>{children}</DeviceControlContext.Provider>
}

export default DeviceControlContext
