import { shortBroadcastModes } from '../deviceModes'
import { bluetoothService } from '../bluetooth/BluetoothService'

/**
 * 广播数据点接口
 */
export interface BroadcastDataPoint {
  intensity: number
  timestamp: number
  mode?: string
  metadata?: Record<string, any>
}

/**
 * 广播策略配置
 */
export interface BroadcastStrategyConfig {
  maxHistoryPoints: number // 最大历史点数
  similarityThreshold: number // 相似度阈值 (0-1)
  compressionRatio: number // 压缩比例 (0-1)
  broadcastInterval: number // 广播间隔 (ms)
  enableSmoothing: boolean // 是否启用平滑
  enableCompression: boolean // 是否启用压缩
  useSimplifiedMode: boolean // 是否使用简化模式（减少缓冲延迟）
}

/**
 * 广播指令接口
 */
export interface BroadcastCommand {
  mode: string
  intensity: number
  duration: number
  timestamp: number
}

/**
 * 高频广播策略管理器
 */
export class BroadcastStrategyManager {
  private historyData: BroadcastDataPoint[] = []
  private config: BroadcastStrategyConfig
  private lastBroadcastTime = 0
  private compressionBuffer: BroadcastDataPoint[] = []
  private broadcastQueue: BroadcastCommand[] = []
  private bluetoothInitialized = false

  constructor(config: Partial<BroadcastStrategyConfig> = {}) {
    this.config = {
      maxHistoryPoints: 500,
      similarityThreshold: 0.85,
      compressionRatio: 0.3,
      broadcastInterval: 100, // 100ms快速响应，强度变化时立即广播
      enableSmoothing: true,
      enableCompression: true,
      useSimplifiedMode: true, // 默认使用简化模式
      ...config
    }

    // 异步初始化蓝牙服务
    this.initializeBluetooth()
  }

  /**
   * 初始化蓝牙服务
   */
  private async initializeBluetooth(): Promise<void> {
    try {
      const success = await bluetoothService.initialize()
      this.bluetoothInitialized = success
    } catch (error) {
      this.bluetoothInitialized = false
    }
  }

  /**
   * 添加数据点 - 简化版本，统一频率控制入口
   */
  public addDataPoint(dataPoint: BroadcastDataPoint): void {
    // 添加时间戳
    const point = {
      ...dataPoint,
      timestamp: dataPoint.timestamp || Date.now()
    }

    // 统一的频率和智能过滤控制
    if (this.shouldBroadcast(point)) {
      if (this.config.useSimplifiedMode) {
        this.executeBroadcastDirectly(point) // 简化模式：直接广播
      } else {
        this.processForBroadcast(point) // 复杂模式：使用压缩缓冲区
      }
    }

    // 添加到历史数据
    this.historyData.push(point)

    // 限制历史数据长度
    if (this.historyData.length > this.config.maxHistoryPoints) {
      this.historyData = this.historyData.slice(-this.config.maxHistoryPoints)
    }
  }

  /**
   * 判断是否应该广播 - 统一的频率和智能过滤控制
   */
  private shouldBroadcast(dataPoint: BroadcastDataPoint): boolean {
    const now = Date.now()

    // 1. 频率控制：这是唯一的频率检查点
    if (now - this.lastBroadcastTime < this.config.broadcastInterval) {
      return false // 频率过高，跳过
    }

    // 2. 特殊情况：强度为0时立即广播（停止指令）
    if (dataPoint.intensity === 0) {
      return true // 立即发送停止指令
    }

    // 3. 智能过滤：避免相似数据的重复广播
    if (this.historyData.length > 0) {
      const lastPoint = this.historyData[this.historyData.length - 1]
      const similarity = this.calculateSimilarity(dataPoint, lastPoint)

      if (similarity > this.config.similarityThreshold) {
        return false // 相似度过高，跳过广播
      }
    }

    return true // 通过所有检查，允许广播
  }

  /**
   * 计算两个数据点的相似度
   */
  private calculateSimilarity(point1: BroadcastDataPoint, point2: BroadcastDataPoint): number {
    // 强度相似度 (权重 0.7)
    const intensityDiff = Math.abs(point1.intensity - point2.intensity) / 9
    const intensitySimilarity = 1 - intensityDiff

    // 时间间隔相似度 (权重 0.3)
    const timeDiff = Math.abs(point1.timestamp - point2.timestamp)
    const timeSimilarity = Math.max(0, 1 - timeDiff / 1000) // 1秒内相似度递减

    return intensitySimilarity * 0.7 + timeSimilarity * 0.3
  }

  /**
   * 直接执行广播 - 简化版本，减少不必要的缓冲延迟
   */
  private executeBroadcastDirectly(dataPoint: BroadcastDataPoint): void {
    // 直接生成并执行广播指令，不使用复杂的压缩缓冲区
    this.generateBroadcastCommand(dataPoint)
    this.lastBroadcastTime = Date.now()
  }

  /**
   * 处理数据点用于广播 - 保留原方法以防需要
   */
  private processForBroadcast(dataPoint: BroadcastDataPoint): void {
    if (this.config.enableCompression) {
      this.compressionBuffer.push(dataPoint)

      // 当缓冲区达到一定大小时进行压缩
      if (this.compressionBuffer.length >= 5) {
        const compressed = this.compressDataPoints(this.compressionBuffer)
        this.generateBroadcastCommand(compressed)
        this.compressionBuffer = []
      }
    } else {
      this.generateBroadcastCommand(dataPoint)
    }

    this.lastBroadcastTime = Date.now()
  }

  /**
   * 压缩数据点
   */
  private compressDataPoints(points: BroadcastDataPoint[]): BroadcastDataPoint {
    if (points.length === 0) {
      throw new Error('Cannot compress empty data points')
    }

    if (points.length === 1) {
      return points[0]
    }

    // 使用加权平均进行压缩
    let totalWeight = 0
    let weightedIntensity = 0
    const latestTimestamp = points[points.length - 1].timestamp

    points.forEach((point, index) => {
      // 越新的点权重越高
      const weight = (index + 1) / points.length
      totalWeight += weight
      weightedIntensity += point.intensity * weight
    })

    return {
      intensity: Math.round(weightedIntensity / totalWeight),
      timestamp: latestTimestamp,
      mode: points[points.length - 1].mode,
      metadata: {
        compressed: true,
        originalCount: points.length
      }
    }
  }

  /**
   * 生成广播指令
   */
  private generateBroadcastCommand(dataPoint: BroadcastDataPoint): void {
    // 根据强度选择广播模式
    const modeIndex = Math.min(Math.floor(dataPoint.intensity), shortBroadcastModes.length - 1)
    const mode = shortBroadcastModes[modeIndex] || shortBroadcastModes[0]

    const command: BroadcastCommand = {
      mode,
      intensity: dataPoint.intensity,
      duration: this.calculateDuration(dataPoint.intensity),
      timestamp: dataPoint.timestamp
    }

    this.broadcastQueue.push(command)

    // 立即执行广播（异步，不阻塞）
    this.executeBroadcast(command).catch(error => {
      console.error('Failed to execute broadcast:', error)
    })
  }

  /**
   * 计算广播持续时间
   * 注意：shortBroadcastModes指令会让设备执行2秒后自动停止，
   * 这里的duration主要用于日志和统计
   */
  private calculateDuration(intensity: number): number {
    // shortBroadcastModes指令固定2秒执行时间
    return 2000 // 2秒固定执行时间
  }

  /**
   * 执行广播指令
   * shortBroadcastModes指令特点：广播后硬件立即执行新强度，2秒后自动停止
   */
  protected async executeBroadcast(command: BroadcastCommand): Promise<void> {
    try {
      // 检查蓝牙是否已初始化
      if (!this.bluetoothInitialized) {
        return
      }

      // 使用现有的蓝牙服务进行广播
      // shortBroadcastModes指令只需要广播一次，设备会自动执行2秒后停止
      await bluetoothService.broadcastCommand(command.mode)
    } catch (error) {
      // 静默处理错误，避免干扰用户体验
    }
  }

  /**
   * 获取历史数据（用于波形显示）
   */
  public getHistoryData(): BroadcastDataPoint[] {
    return [...this.historyData]
  }

  /**
   * 获取压缩后的历史数据（性能优化）
   */
  public getCompressedHistoryData(targetPoints = 200): BroadcastDataPoint[] {
    if (this.historyData.length <= targetPoints) {
      return [...this.historyData]
    }

    const step = this.historyData.length / targetPoints
    const compressed: BroadcastDataPoint[] = []

    for (let i = 0; i < targetPoints; i++) {
      const index = Math.floor(i * step)
      compressed.push(this.historyData[index])
    }

    return compressed
  }

  /**
   * 应用平滑算法
   */
  public getSmoothHistoryData(windowSize = 5): BroadcastDataPoint[] {
    if (!this.config.enableSmoothing || this.historyData.length < windowSize) {
      return [...this.historyData]
    }

    const smoothed: BroadcastDataPoint[] = []

    for (let i = 0; i < this.historyData.length; i++) {
      const startIndex = Math.max(0, i - Math.floor(windowSize / 2))
      const endIndex = Math.min(this.historyData.length - 1, i + Math.floor(windowSize / 2))

      let sum = 0
      let count = 0

      for (let j = startIndex; j <= endIndex; j++) {
        sum += this.historyData[j].intensity
        count++
      }

      smoothed.push({
        ...this.historyData[i],
        intensity: sum / count
      })
    }

    return smoothed
  }

  /**
   * 清除所有数据
   */
  public clear(): void {
    this.historyData = []
    this.compressionBuffer = []
    this.broadcastQueue = []
    this.lastBroadcastTime = 0
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<BroadcastStrategyConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * 获取当前配置
   */
  public getConfig(): BroadcastStrategyConfig {
    return { ...this.config }
  }

  /**
   * 获取性能统计
   */
  public getPerformanceStats(): {
    totalPoints: number
    compressionRatio: number
    averageBroadcastInterval: number
    queueLength: number
    bluetoothInitialized: boolean
  } {
    return {
      totalPoints: this.historyData.length,
      compressionRatio: this.compressionBuffer.length / Math.max(1, this.historyData.length),
      averageBroadcastInterval: this.config.broadcastInterval,
      queueLength: this.broadcastQueue.length,
      bluetoothInitialized: this.bluetoothInitialized
    }
  }

  /**
   * 检查蓝牙状态
   */
  public isBluetoothReady(): boolean {
    return this.bluetoothInitialized
  }

  /**
   * 手动重新初始化蓝牙
   */
  public async reinitializeBluetooth(): Promise<boolean> {
    await this.initializeBluetooth()
    return this.bluetoothInitialized
  }
}

/**
 * 创建预设配置
 */
export const BroadcastPresets = {
  // 划屏模式 - 实时响应，硬件会在2秒后自动停止
  swipe: {
    maxHistoryPoints: 300,
    similarityThreshold: 0.9, // 高相似度阈值，避免微小变化频繁广播
    compressionRatio: 0.2,
    broadcastInterval: 100, // 100ms快速响应，强度变化时立即广播
    enableSmoothing: true,
    enableCompression: true,
    useSimplifiedMode: true // 使用简化模式，减少延迟
  } as BroadcastStrategyConfig,

  // 音乐模式 - 跟随音频变化，硬件2秒后自动停止（优化性能）
  music: {
    maxHistoryPoints: 500,
    similarityThreshold: 0.8,
    compressionRatio: 0.4,
    broadcastInterval: 200, // 从150ms优化到200ms，减少CPU负载
    enableSmoothing: true,
    enableCompression: true,
    useSimplifiedMode: true // 使用简化模式，减少延迟
  } as BroadcastStrategyConfig,

  // 视频模式 - 跟随视频内容，硬件2秒后自动停止
  video: {
    maxHistoryPoints: 400,
    similarityThreshold: 0.85,
    compressionRatio: 0.3,
    broadcastInterval: 200, // 跟随视频场景变化
    enableSmoothing: true,
    enableCompression: true,
    useSimplifiedMode: true // 使用简化模式，减少延迟
  } as BroadcastStrategyConfig,

  // 性能优先 - 最大压缩，最低延迟
  performance: {
    maxHistoryPoints: 200,
    similarityThreshold: 0.7,
    compressionRatio: 0.6,
    broadcastInterval: 200,
    enableSmoothing: false,
    enableCompression: true,
    useSimplifiedMode: true // 性能优先，必须使用简化模式
  } as BroadcastStrategyConfig
}
