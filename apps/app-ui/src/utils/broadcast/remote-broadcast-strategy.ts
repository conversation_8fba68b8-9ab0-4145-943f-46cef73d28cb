import {
  BroadcastStrategyManager,
  type BroadcastStrategyConfig,
  type BroadcastCommand
} from './broadcast-strategy'
import { remoteControlService } from '@/api/services/remote-control'

/**
 * 远程广播策略管理器
 * 继承自BroadcastStrategyManager，重写广播行为以发送远程控制指令
 */
export class RemoteBroadcastStrategyManager extends BroadcastStrategyManager {
  constructor(config: Partial<BroadcastStrategyConfig> = {}) {
    super(config)
  }

  /**
   * 重写执行广播指令方法
   * 发送远程控制指令而不是蓝牙广播
   */
  protected async executeBroadcast(command: BroadcastCommand): Promise<void> {
    try {
      console.log('🌐 [远程广播] 发送划屏指令:', {
        intensity: command.intensity,
        mode: command.mode,
        timestamp: command.timestamp
      })

      // 发送远程控制指令
      await remoteControlService.sendSwipeCommand(command.intensity, 'move')
    } catch (error) {
      console.error('远程广播指令发送失败:', error)
      // 静默处理错误，避免干扰用户体验
    }
  }
}
