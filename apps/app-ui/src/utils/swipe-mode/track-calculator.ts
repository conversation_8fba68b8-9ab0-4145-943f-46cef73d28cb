import type { TrackPoint, Track, WaveformPoint } from '@/types/swipe-mode'

/**
 * 轨迹计算工具类
 * 负责轨迹数据的各种计算和分析
 */
export class TrackCalculator {
  /**
   * 计算两点之间的距离
   */
  static calculateDistance(point1: TrackPoint, point2: TrackPoint): number {
    const dx = point2.x - point1.x
    const dy = point2.y - point1.y
    return Math.sqrt(dx * dx + dy * dy)
  }

  /**
   * 计算轨迹点的瞬时速度（像素/毫秒）
   */
  static calculateSpeed(points: TrackPoint[], index: number, smoothingWindow = 3): number {
    if (points.length < 2 || index < 1) return 0

    // 使用滑动窗口平滑速度计算
    const start = Math.max(0, index - smoothingWindow)
    const end = Math.min(points.length - 1, index + smoothingWindow)

    if (start === end) return 0

    const totalDistance = this.calculateDistance(points[start], points[end])
    const totalTime = points[end].timestamp - points[start].timestamp

    return totalTime > 0 ? totalDistance / totalTime : 0
  }

  /**
   * 根据速度计算强度等级（1-9）
   */
  static calculateIntensity(speed: number, minSpeed = 0.1, maxSpeed = 2.0): number {
    // 将速度映射到 1-9 的强度等级
    const normalizedSpeed = Math.max(0, Math.min(1, (speed - minSpeed) / (maxSpeed - minSpeed)))
    return Math.max(1, Math.min(9, Math.floor(normalizedSpeed * 8) + 1))
  }

  /**
   * 计算轨迹的总距离
   */
  static calculateTotalDistance(points: TrackPoint[]): number {
    if (points.length < 2) return 0

    let totalDistance = 0
    for (let i = 1; i < points.length; i++) {
      totalDistance += this.calculateDistance(points[i - 1], points[i])
    }
    return totalDistance
  }

  /**
   * 计算轨迹的平均速度
   */
  static calculateAverageSpeed(points: TrackPoint[]): number {
    if (points.length < 2) return 0

    const totalDistance = this.calculateTotalDistance(points)
    const totalTime = points[points.length - 1].timestamp - points[0].timestamp

    return totalTime > 0 ? totalDistance / totalTime : 0
  }

  /**
   * 生成波形数据
   */
  static generateWaveformData(points: TrackPoint[], sampleCount = 100): WaveformPoint[] {
    if (points.length < 2) return []

    const waveform: WaveformPoint[] = []
    const totalDuration = points[points.length - 1].timestamp - points[0].timestamp
    const sampleInterval = totalDuration / sampleCount

    for (let i = 0; i < sampleCount; i++) {
      const targetTime = points[0].timestamp + i * sampleInterval

      // 找到最接近目标时间的点
      let closestIndex = 0
      let minTimeDiff = Math.abs(points[0].timestamp - targetTime)

      for (let j = 1; j < points.length; j++) {
        const timeDiff = Math.abs(points[j].timestamp - targetTime)
        if (timeDiff < minTimeDiff) {
          minTimeDiff = timeDiff
          closestIndex = j
        }
      }

      const speed = this.calculateSpeed(points, closestIndex)
      const intensity = this.calculateIntensity(speed)

      waveform.push({
        time: targetTime,
        intensity,
        speed
      })
    }

    return waveform
  }

  /**
   * 创建轨迹对象
   */
  static createTrack(points: TrackPoint[], name?: string): Track {
    if (points.length === 0) {
      throw new Error('Cannot create track with empty points')
    }

    const startTime = points[0].timestamp
    const endTime = points[points.length - 1].timestamp
    const duration = endTime - startTime
    const totalDistance = this.calculateTotalDistance(points)

    // 计算最大和平均强度
    let maxIntensity = 0
    let totalIntensity = 0

    for (let i = 0; i < points.length; i++) {
      const speed = this.calculateSpeed(points, i)
      const intensity = this.calculateIntensity(speed)
      maxIntensity = Math.max(maxIntensity, intensity)
      totalIntensity += intensity
    }

    const averageIntensity = points.length > 0 ? totalIntensity / points.length : 0

    return {
      id: `track_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: name || `轨迹 ${new Date().toLocaleTimeString()}`,
      points,
      startTime,
      endTime,
      duration,
      maxIntensity,
      averageIntensity,
      totalDistance,
      createdAt: Date.now()
    }
  }

  /**
   * 平滑轨迹点（减少噪声）
   */
  static smoothTrack(points: TrackPoint[], windowSize = 3): TrackPoint[] {
    if (points.length <= windowSize) return [...points]

    const smoothed: TrackPoint[] = []

    for (let i = 0; i < points.length; i++) {
      if (i < windowSize / 2 || i >= points.length - windowSize / 2) {
        // 边界点保持不变
        smoothed.push({ ...points[i] })
      } else {
        // 使用滑动窗口平均值
        let sumX = 0,
          sumY = 0
        const start = i - Math.floor(windowSize / 2)
        const end = i + Math.floor(windowSize / 2)

        for (let j = start; j <= end; j++) {
          sumX += points[j].x
          sumY += points[j].y
        }

        smoothed.push({
          ...points[i],
          x: sumX / windowSize,
          y: sumY / windowSize
        })
      }
    }

    return smoothed
  }
}
