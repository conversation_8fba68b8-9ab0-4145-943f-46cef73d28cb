import type { TrackPoint, CanvasConfig } from '@/types/swipe-mode'
import { TrackCalculator } from './track-calculator'

/**
 * Canvas 绘制管理器
 * 负责轨迹的绘制和视觉效果
 */
export class CanvasManager {
  private canvas: HTMLCanvasElement
  private ctx: CanvasRenderingContext2D
  private config: CanvasConfig
  private animationId?: number

  constructor(canvas: HTMLCanvasElement, config: Partial<CanvasConfig> = {}) {
    this.canvas = canvas
    const ctx = canvas.getContext('2d')
    if (!ctx) {
      throw new Error('无法获取 Canvas 2D 上下文')
    }
    this.ctx = ctx

    // 默认配置
    this.config = {
      width: canvas.width,
      height: canvas.height,
      backgroundColor: '#121521',
      gridColor: 'rgba(255, 255, 255, 0.1)',
      trackColors: {
        slow: '#00d4ff', // 蓝色 - 慢速
        medium: '#892fff', // 紫色 - 中速
        fast: '#ff2d97' // 粉色 - 快速
      },
      lineWidth: 3,
      showGrid: true,
      ...config
    }

    this.setupCanvas()
  }

  /**
   * 设置 Canvas 基本属性（根据父容器尺寸）
   */
  private setupCanvas(): void {
    const dpr = window.devicePixelRatio || 1
    let targetWidth: number
    let targetHeight: number

    // 获取父容器的尺寸
    const parentElement = this.canvas.parentElement
    if (parentElement) {
      const parentRect = parentElement.getBoundingClientRect()
      targetWidth = parentRect.width
      targetHeight = parentRect.height

      // 如果父容器尺寸为0，使用视窗尺寸作为备用
      if (targetWidth === 0 || targetHeight === 0) {
        targetWidth = window.innerWidth
        targetHeight = window.innerHeight * 0.75 // 75vh
        console.warn('Parent container size is 0, using viewport size as fallback')
      }
    } else {
      // 备用方案：使用视窗尺寸
      targetWidth = window.innerWidth
      targetHeight = window.innerHeight * 0.75 // 75vh
      console.warn('Canvas parent element not found, using viewport size')
    }

    // 设置Canvas的实际尺寸（考虑高DPI）
    this.canvas.width = targetWidth * dpr
    this.canvas.height = targetHeight * dpr

    // 设置Canvas的显示尺寸
    this.canvas.style.width = targetWidth + 'px'
    this.canvas.style.height = targetHeight + 'px'

    // 缩放绘制上下文以适应高DPI
    this.ctx.scale(dpr, dpr)

    // 更新配置中的尺寸
    this.config.width = targetWidth
    this.config.height = targetHeight

    // 设置绘制属性
    this.ctx.lineCap = 'round'
    this.ctx.lineJoin = 'round'
    this.ctx.imageSmoothingEnabled = true

    console.log('Canvas setup:', {
      canvasSize: { width: targetWidth, height: targetHeight },
      dpr,
      actualCanvasSize: { width: this.canvas.width, height: this.canvas.height },
      source: parentElement ? 'parent container' : 'viewport fallback'
    })
  }

  /**
   * 清空画布
   */
  clear(): void {
    this.ctx.clearRect(0, 0, this.config.width, this.config.height)
    this.drawBackground()
  }

  /**
   * 绘制背景（透明）
   */
  private drawBackground(): void {
    // 透明背景，不绘制任何颜色
    // this.ctx.fillStyle = this.config.backgroundColor
    // this.ctx.fillRect(0, 0, this.config.width, this.config.height)

    // 网格线（可选）
    if (this.config.showGrid) {
      this.drawGrid()
    }
  }

  /**
   * 绘制网格
   */
  private drawGrid(): void {
    const gridSize = 30
    this.ctx.strokeStyle = this.config.gridColor
    this.ctx.lineWidth = 1

    // 垂直线
    for (let x = 0; x <= this.config.width; x += gridSize) {
      this.ctx.beginPath()
      this.ctx.moveTo(x, 0)
      this.ctx.lineTo(x, this.config.height)
      this.ctx.stroke()
    }

    // 水平线
    for (let y = 0; y <= this.config.height; y += gridSize) {
      this.ctx.beginPath()
      this.ctx.moveTo(0, y)
      this.ctx.lineTo(this.config.width, y)
      this.ctx.stroke()
    }
  }

  /**
   * 根据速度获取轨迹颜色
   */
  private getTrackColor(speed: number): string {
    if (speed < 0.5) return this.config.trackColors.slow
    if (speed < 1.2) return this.config.trackColors.medium
    return this.config.trackColors.fast
  }

  /**
   * 绘制单条轨迹（升级版：动态粗细 + 光晕效果）
   */
  drawTrack(points: TrackPoint[], opacity = 1): void {
    if (points.length < 2) return

    this.ctx.lineCap = 'round'
    this.ctx.lineJoin = 'round'

    for (let i = 1; i < points.length; i++) {
      const prevPoint = points[i - 1]
      const currentPoint = points[i]
      const speed = TrackCalculator.calculateSpeed(points, i)
      const intensity = TrackCalculator.calculateIntensity(speed)

      this.drawEnhancedSegment(prevPoint, currentPoint, intensity, speed, opacity)
    }

    this.ctx.globalAlpha = 1
  }

  /**
   * 绘制增强的线段（粗壮有质感的轨迹）
   */
  private drawEnhancedSegment(
    startPoint: TrackPoint,
    endPoint: TrackPoint,
    intensity: number,
    speed: number,
    baseOpacity = 1
  ): void {
    const color = this.getTrackColor(speed)
    // 大幅增加线条粗细，让轨迹更粗壮
    const dynamicWidth = Math.max(12, Math.min(28, intensity * 2.5 + 8))

    // 绘制多层效果，营造立体感
    this.drawLayeredTrack(startPoint, endPoint, color, dynamicWidth, baseOpacity)
  }

  /**
   * 绘制分层轨迹效果
   */
  private drawLayeredTrack(
    startPoint: TrackPoint,
    endPoint: TrackPoint,
    color: string,
    width: number,
    baseOpacity: number
  ): void {
    this.ctx.lineCap = 'round'
    this.ctx.lineJoin = 'round'

    // 第一层：外层光晕（最粗）
    this.ctx.globalAlpha = 0.15 * baseOpacity
    this.ctx.lineWidth = width * 1.8
    this.ctx.strokeStyle = color
    this.ctx.beginPath()
    this.ctx.moveTo(startPoint.x, startPoint.y)
    this.ctx.lineTo(endPoint.x, endPoint.y)
    this.ctx.stroke()

    // 第二层：中层光晕
    this.ctx.globalAlpha = 0.3 * baseOpacity
    this.ctx.lineWidth = width * 1.4
    this.ctx.strokeStyle = color
    this.ctx.beginPath()
    this.ctx.moveTo(startPoint.x, startPoint.y)
    this.ctx.lineTo(endPoint.x, endPoint.y)
    this.ctx.stroke()

    // 第三层：主体线条
    this.ctx.globalAlpha = 0.8 * baseOpacity
    this.ctx.lineWidth = width
    this.ctx.strokeStyle = color
    this.ctx.beginPath()
    this.ctx.moveTo(startPoint.x, startPoint.y)
    this.ctx.lineTo(endPoint.x, endPoint.y)
    this.ctx.stroke()

    // 第四层：内层高光
    this.ctx.globalAlpha = 0.9 * baseOpacity
    this.ctx.lineWidth = width * 0.6
    this.ctx.strokeStyle = this.getLighterColor(color)
    this.ctx.beginPath()
    this.ctx.moveTo(startPoint.x, startPoint.y)
    this.ctx.lineTo(endPoint.x, endPoint.y)
    this.ctx.stroke()

    // 第五层：核心亮线
    this.ctx.globalAlpha = 1.0 * baseOpacity
    this.ctx.lineWidth = width * 0.25
    this.ctx.strokeStyle = '#ffffff'
    this.ctx.beginPath()
    this.ctx.moveTo(startPoint.x, startPoint.y)
    this.ctx.lineTo(endPoint.x, endPoint.y)
    this.ctx.stroke()
  }

  /**
   * 获取更亮的颜色
   */
  private getLighterColor(color: string): string {
    // 根据原色返回更亮的版本
    const colorMap: { [key: string]: string } = {
      '#00d4ff': '#66e5ff', // 蓝色 -> 亮蓝色
      '#892fff': '#b366ff', // 紫色 -> 亮紫色
      '#ff2d97': '#ff66b3', // 粉色 -> 亮粉色
      '#ffaa00': '#ffcc66', // 橙色 -> 亮橙色
      '#ff4444': '#ff7777' // 红色 -> 亮红色
    }
    return colorMap[color] || '#ffffff'
  }

  /**
   * 绘制多条轨迹
   */
  drawMultipleTracks(
    tracksList: TrackPoint[][],
    currentTrack?: TrackPoint[],
    isDrawing = false
  ): void {
    this.clear()

    // 不绘制已完成的轨迹，只保留当前轨迹的尾巴效果

    // 绘制当前正在绘制的轨迹（带尾巴效果）
    if (currentTrack && currentTrack.length > 0) {
      this.drawTrackWithTail(currentTrack)

      // 只有在正在绘制时才显示高亮效果
      if (isDrawing) {
        const lastPoint = currentTrack[currentTrack.length - 1]
        this.drawPointHighlight(lastPoint)
      }
    }
  }

  /**
   * 绘制带尾巴效果的轨迹（更连贯的绘制）
   */
  private drawTrackWithTail(points: TrackPoint[]): void {
    if (points.length < 2) return

    // 增加尾巴长度，让效果更流畅
    const tailLength = Math.min(50, points.length) // 进一步增加到50个点
    const startIndex = Math.max(0, points.length - tailLength)
    const tailPoints = points.slice(startIndex)

    this.ctx.lineCap = 'round'
    this.ctx.lineJoin = 'round'

    // 使用连贯的路径绘制，而不是分段绘制
    this.drawSmoothTrailPath(tailPoints, points, startIndex)

    this.ctx.globalAlpha = 1
  }

  /**
   * 绘制平滑连贯的轨迹路径（减少闪烁）
   */
  private drawSmoothTrailPath(
    tailPoints: TrackPoint[],
    allPoints: TrackPoint[],
    startIndex: number
  ): void {
    // 减少层数和降低透明度，避免过度闪烁
    const layers = [
      { widthMultiplier: 1.8, opacity: 0.08 }, // 最外层光晕（更透明）
      { widthMultiplier: 1.4, opacity: 0.15 }, // 外层光晕
      { widthMultiplier: 1.0, opacity: 0.5 }, // 主体（降低透明度）
      { widthMultiplier: 0.6, opacity: 0.4 } // 内层高光（去掉过亮的核心线）
    ]

    layers.forEach((layer, layerIndex) => {
      this.ctx.beginPath()

      // 创建连续的路径
      for (let i = 0; i < tailPoints.length; i++) {
        const point = tailPoints[i]
        const progress = i / (tailPoints.length - 1)
        const smoothProgress = Math.pow(progress, 0.6)

        // 计算当前点的属性
        const speed = TrackCalculator.calculateSpeed(allPoints, startIndex + i)
        const intensity = TrackCalculator.calculateIntensity(speed)
        const color = this.getTrackColor(speed)

        // 动态宽度
        const baseWidth = Math.max(12, Math.min(28, intensity * 2.5 + 8))
        const currentWidth = baseWidth * layer.widthMultiplier

        // 更平缓的透明度渐变，减少闪烁
        const fadeOpacity = 0.2 + smoothProgress * 0.6 // 从0.2到0.8，范围更小
        const finalOpacity = layer.opacity * fadeOpacity

        if (i === 0) {
          this.ctx.moveTo(point.x, point.y)
        } else {
          // 使用二次贝塞尔曲线创建更平滑的连接
          const prevPoint = tailPoints[i - 1]
          const controlX = (prevPoint.x + point.x) / 2
          const controlY = (prevPoint.y + point.y) / 2
          this.ctx.quadraticCurveTo(prevPoint.x, prevPoint.y, controlX, controlY)
        }

        // 设置当前段的样式（使用最新点的颜色）
        if (i === tailPoints.length - 1) {
          this.ctx.globalAlpha = finalOpacity
          this.ctx.lineWidth = currentWidth

          // 使用更柔和的颜色，避免过亮
          if (layerIndex === layers.length - 1) {
            // 内层高光使用稍亮的颜色，而不是纯白
            this.ctx.strokeStyle = this.getLighterColor(color)
          } else {
            this.ctx.strokeStyle = color // 其他层使用原色
          }
        }
      }

      // 绘制整个路径
      this.ctx.stroke()
    })
  }

  /**
   * 绘制点的高亮效果 - 柔和版感官刺激
   */
  private drawPointHighlight(point: TrackPoint): void {
    const time = Date.now()
    const pulseScale = 1 + Math.sin(time * 0.006) * 0.3 // 减少脉冲幅度
    const rotationAngle = time * 0.003 // 减慢旋转速度

    this.ctx.save()

    // 外层爆炸光环（减少强度）
    this.drawSoftExplosionRing(point, pulseScale, time)

    // 多层光晕效果
    this.drawMultiLayerGlow(point, pulseScale)

    // 核心亮点（降低亮度）
    this.drawSoftCoreHighlight(point, pulseScale)

    // 旋转能量射线（减少数量和亮度）
    this.drawSoftEnergyRays(point, pulseScale, rotationAngle)

    // 脉冲波纹
    this.drawPulseRipples(point, time)

    this.ctx.restore()
  }

  /**
   * 绘制柔和爆炸光环效果
   */
  private drawSoftExplosionRing(point: TrackPoint, scale: number, time: number): void {
    const ringCount = 2 // 减少光环数量
    for (let i = 0; i < ringCount; i++) {
      const phase = (time * 0.001 + i * 0.6) % 1 // 减慢动画速度
      const radius = 20 + phase * 30 * scale // 减小半径
      const opacity = (1 - phase) * 0.2 // 降低透明度

      // 创建径向渐变
      const gradient = this.ctx.createRadialGradient(
        point.x,
        point.y,
        radius * 0.8,
        point.x,
        point.y,
        radius
      )
      gradient.addColorStop(0, `rgba(255, 45, 151, ${opacity})`)
      gradient.addColorStop(0.5, `rgba(137, 47, 255, ${opacity * 0.5})`)
      gradient.addColorStop(1, `rgba(0, 212, 255, 0)`)

      this.ctx.strokeStyle = gradient
      this.ctx.lineWidth = 2 // 减少线条宽度
      this.ctx.beginPath()
      this.ctx.arc(point.x, point.y, radius, 0, Math.PI * 2)
      this.ctx.stroke()
    }
  }

  /**
   * 柔和版核心高亮
   */
  private drawSoftCoreHighlight(point: TrackPoint, scale: number): void {
    // 外层核心光晕（降低亮度）
    const outerGradient = this.ctx.createRadialGradient(
      point.x,
      point.y,
      0,
      point.x,
      point.y,
      15 * scale // 减小半径
    )
    outerGradient.addColorStop(0, 'rgba(255, 255, 255, 0.6)') // 降低白色亮度
    outerGradient.addColorStop(0.3, 'rgba(255, 45, 151, 0.4)') // 降低透明度
    outerGradient.addColorStop(0.7, 'rgba(137, 47, 255, 0.2)')
    outerGradient.addColorStop(1, 'rgba(0, 212, 255, 0)')

    this.ctx.fillStyle = outerGradient
    this.ctx.beginPath()
    this.ctx.arc(point.x, point.y, 15 * scale, 0, Math.PI * 2)
    this.ctx.fill()

    // 内层柔和核心
    const innerGradient = this.ctx.createRadialGradient(
      point.x,
      point.y,
      0,
      point.x,
      point.y,
      6 * scale // 减小半径
    )
    innerGradient.addColorStop(0, 'rgba(255, 255, 255, 0.7)') // 降低亮度
    innerGradient.addColorStop(0.6, 'rgba(255, 255, 255, 0.5)')
    innerGradient.addColorStop(1, 'rgba(255, 45, 151, 0.2)')

    this.ctx.fillStyle = innerGradient
    this.ctx.beginPath()
    this.ctx.arc(point.x, point.y, 6 * scale, 0, Math.PI * 2)
    this.ctx.fill()
  }

  /**
   * 柔和版能量射线
   */
  private drawSoftEnergyRays(point: TrackPoint, scale: number, rotation: number): void {
    const rayCount = 8 // 减少射线数量
    const rayLength = 20 * scale // 缩短射线长度

    for (let i = 0; i < rayCount; i++) {
      const angle = (i / rayCount) * Math.PI * 2 + rotation
      const startRadius = 10 * scale
      const endRadius = startRadius + rayLength

      const startX = point.x + Math.cos(angle) * startRadius
      const startY = point.y + Math.sin(angle) * startRadius
      const endX = point.x + Math.cos(angle) * endRadius
      const endY = point.y + Math.sin(angle) * endRadius

      // 创建柔和射线渐变
      const gradient = this.ctx.createLinearGradient(startX, startY, endX, endY)
      gradient.addColorStop(0, 'rgba(255, 255, 255, 0.4)') // 降低亮度
      gradient.addColorStop(0.3, 'rgba(255, 45, 151, 0.3)')
      gradient.addColorStop(0.7, 'rgba(137, 47, 255, 0.2)')
      gradient.addColorStop(1, 'rgba(0, 212, 255, 0)')

      this.ctx.strokeStyle = gradient
      this.ctx.lineWidth = 2 // 减少线条宽度
      this.ctx.beginPath()
      this.ctx.moveTo(startX, startY)
      this.ctx.lineTo(endX, endY)
      this.ctx.stroke()
    }
  }

  /**
   * 绘制多层光晕
   */
  private drawMultiLayerGlow(point: TrackPoint, scale: number): void {
    // 外层光晕
    const outerGradient = this.ctx.createRadialGradient(
      point.x,
      point.y,
      0,
      point.x,
      point.y,
      35 * scale
    )
    outerGradient.addColorStop(0, 'rgba(255, 45, 151, 0.6)')
    outerGradient.addColorStop(0.4, 'rgba(137, 47, 255, 0.4)')
    outerGradient.addColorStop(1, 'rgba(0, 212, 255, 0)')

    this.ctx.fillStyle = outerGradient
    this.ctx.beginPath()
    this.ctx.arc(point.x, point.y, 35 * scale, 0, Math.PI * 2)
    this.ctx.fill()

    // 中层光晕
    const middleGradient = this.ctx.createRadialGradient(
      point.x,
      point.y,
      0,
      point.x,
      point.y,
      20 * scale
    )
    middleGradient.addColorStop(0, 'rgba(255, 45, 151, 0.8)')
    middleGradient.addColorStop(0.6, 'rgba(255, 45, 151, 0.4)')
    middleGradient.addColorStop(1, 'rgba(255, 45, 151, 0)')

    this.ctx.fillStyle = middleGradient
    this.ctx.beginPath()
    this.ctx.arc(point.x, point.y, 20 * scale, 0, Math.PI * 2)
    this.ctx.fill()
  }

  /**
   * 绘制脉冲波纹（修复错位问题）
   */
  private drawPulseRipples(point: TrackPoint, time: number): void {
    // 不重置变换，直接绘制脉冲波纹
    this.ctx.save()

    for (let i = 0; i < 3; i++) {
      const phase = (time * 0.003 + i * 0.5) % 1
      const radius = 15 + phase * 30
      const opacity = (1 - phase) * 0.3

      this.ctx.globalAlpha = opacity
      this.ctx.strokeStyle = '#ff2d97'
      this.ctx.lineWidth = 2
      this.ctx.beginPath()
      this.ctx.arc(point.x, point.y, radius, 0, Math.PI * 2)
      this.ctx.stroke()
    }

    this.ctx.restore()
  }

  /**
   * 开始动画循环（用于高亮效果）
   */
  startAnimation(renderCallback: () => void): void {
    const animate = () => {
      renderCallback()
      this.animationId = requestAnimationFrame(animate)
    }
    animate()
  }

  /**
   * 停止动画循环
   */
  stopAnimation(): void {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
      this.animationId = undefined
    }
  }

  /**
   * 将屏幕坐标转换为Canvas坐标
   */
  screenToCanvas(screenX: number, screenY: number): { x: number; y: number } {
    const rect = this.canvas.getBoundingClientRect()
    return {
      x: screenX - rect.left,
      y: screenY - rect.top
    }
  }

  /**
   * 调整Canvas大小
   */
  resize(): void {
    this.setupCanvas()
    this.drawBackground()
  }

  /**
   * 强制设置Canvas尺寸（基于父容器）
   */
  forceResize(): void {
    // 等待一帧，确保父容器尺寸更新完成
    requestAnimationFrame(() => {
      this.setupCanvas()
      this.drawBackground()
    })
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<CanvasConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * 获取Canvas数据URL（用于保存图片）
   */
  toDataURL(type = 'image/png', quality = 1): string {
    return this.canvas.toDataURL(type, quality)
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.stopAnimation()
  }
}
