import { addToast } from '@heroui/react'
import type { DeviceFunction } from '@/api/services/devices'
import { bluetoothService } from './BluetoothService'
import { modes } from '../deviceModes' // 引入模式静态数据
import i18n from '@/i18n'

// 命令队列管理器
class CommandQueueManager {
  private queue: string[] = []
  private isProcessing = false
  private delayBetweenCommands = 300 // 命令之间的延迟时间(毫秒)
  private processedMessages = new Set<string>() // 已处理的消息ID集合
  private pageLoadTimestamp: number // 页面加载时间戳

  constructor() {
    // 记录页面加载时间戳
    this.pageLoadTimestamp = Date.now()
    console.log('CommandQueueManager初始化, 页面加载时间戳:', this.pageLoadTimestamp)
  }

  // 添加命令到队列
  public addCommands(commands: string[]): void {
    // 过滤掉空命令
    const validCommands = commands.filter(cmd => cmd && cmd.trim().length > 0)
    if (validCommands.length === 0) return

    // 添加到队列
    this.queue.push(...validCommands)
    console.log(`添加了${validCommands.length}个命令到队列，当前队列长度: ${this.queue.length}`)

    // 如果未在处理中，开始处理队列
    if (!this.isProcessing) {
      this.processQueue()
    }
  }

  // 检查消息是否应该处理（基于消息时间戳和页面加载时间）
  public shouldProcessMessage(message: any): boolean {
    // 1. 检查消息是否已处理过
    if (this.hasProcessedMessage(message.id)) {
      console.log(`消息 ${message.id} 已处理过，跳过命令解析`)
      return false
    }

    // 2. 检查消息是否有创建时间
    if (message.createdAt) {
      try {
        // 尝试解析消息时间戳
        const messageTimestamp = new Date(message.createdAt).getTime()

        // 如果消息创建时间早于页面加载时间，且差距大于5秒，认为是历史消息，不处理
        // 5秒是一个缓冲时间，处理页面加载时可能刚好收到的新消息
        if (messageTimestamp < this.pageLoadTimestamp - 5000) {
          console.log(
            `消息 ${message.id} 是历史消息 (${new Date(
              messageTimestamp
            ).toISOString()}), 当前页面加载于 ${new Date(
              this.pageLoadTimestamp
            ).toISOString()}, 跳过处理`
          )
          // 标记为已处理，避免再次检查
          this.markMessageAsProcessed(message.id)
          return false
        }
      } catch (error) {
        console.error('解析消息时间戳出错:', error)
        // 解析出错时，为安全起见仍然处理消息
      }
    }

    return true
  }

  // 处理命令队列
  private async processQueue(): Promise<void> {
    if (this.queue.length === 0) {
      this.isProcessing = false
      return
    }

    this.isProcessing = true
    const command = this.queue.shift()!

    try {
      console.log(`正在处理命令: ${command}`)
      // 初始化蓝牙服务
      await bluetoothService.initialize()
      // 发送命令
      await bluetoothService.broadcastCommand(command)

      // 延迟一段时间处理下一个命令
      setTimeout(() => {
        this.processQueue()
      }, this.delayBetweenCommands)
    } catch (error) {
      console.error('处理命令出错:', error)
      // 即使出错也继续处理下一个命令
      setTimeout(() => {
        this.processQueue()
      }, this.delayBetweenCommands)
    }
  }

  // 检查消息是否已处理过
  public hasProcessedMessage(messageId: string): boolean {
    return this.processedMessages.has(messageId)
  }

  // 标记消息为已处理
  public markMessageAsProcessed(messageId: string): void {
    this.processedMessages.add(messageId)

    // 如果集合太大，清理一下（保留最近1000条）
    if (this.processedMessages.size > 1000) {
      const toKeep = Array.from(this.processedMessages).slice(-1000)
      this.processedMessages = new Set(toKeep)
    }
  }

  // 处理AI返回的文本中包含的设备命令
  public handleDeviceCommands(text: string, message: any): string {
    const messageId = message.id

    // 检查消息是否应该处理
    if (!this.shouldProcessMessage(message)) {
      return text.replace(/<device>\[.*?\]<\/device>/gs, '')
    }

    // 提取并解析设备命令
    const deviceCommandRegex = /<device>\[(.*?)\]<\/device>/
    const commands = this.parseDeviceCommands(text)

    if (commands.length > 0) {
      console.log(`消息 ${messageId} 检测到设备命令:`, commands)
      this.addCommands(commands)
    }

    // 无论是否有命令，都标记该消息已处理
    this.markMessageAsProcessed(messageId)

    // 返回过滤掉设备命令的文本
    return text.replace(deviceCommandRegex, '')
  }

  // 解析AI返回的设备命令
  public parseDeviceCommands(text: string): string[] {
    // 使用正则表达式匹配设备命令，添加s标志使.匹配换行符
    const deviceCommandRegex = /<device>\[(.*?)\]<\/device>/s
    const match = text.match(deviceCommandRegex)

    if (!match || !match[1]) return []

    try {
      // 解析命令字符串，去除所有空白字符（包括空格、制表符、换行符等）
      const commandsStr = match[1].trim()
      const cleanCommandsStr = commandsStr.replace(/\s+/g, '')

      console.log('解析设备命令，原始命令字符串:', commandsStr)
      console.log('清理后的命令字符串:', cleanCommandsStr)

      // 1. 首先尝试解析JSON格式（如果命令被引号包围）
      try {
        // 将命令字符串包装成数组格式，然后尝试解析
        const jsonStr = `[${cleanCommandsStr}]`
        const jsonCommands = JSON.parse(jsonStr)
        if (Array.isArray(jsonCommands) && jsonCommands.length > 0) {
          // 确保去除引号并过滤有效命令
          const validCommands = jsonCommands
            .map(cmd => String(cmd).trim())
            .filter(cmd => cmd.length > 0 && /^[0-9a-f]+$/i.test(cmd)) // 只保留16进制字符串

          if (validCommands.length > 0) {
            console.log('通过JSON解析成功获取命令:', validCommands)
            return validCommands
          }
        }
      } catch (jsonError) {
        console.log('JSON解析失败，尝试其他方法')
      }

      // 2. 尝试按逗号分隔（处理简单的逗号分隔格式）
      const commands = cleanCommandsStr
        .split(',')
        .map(cmd => cmd.trim().replace(/^["']|["']$/g, '')) // 去除首尾的引号
        .filter(cmd => cmd.length > 0 && /^[0-9a-f]+$/i.test(cmd)) // 只保留有效的16进制字符串

      if (commands.length > 0) {
        console.log('通过逗号分隔成功解析命令:', commands)
        return commands
      }

      // 3. 尝试匹配可能的命令格式（16进制字符串）
      const hexPattern = /[0-9a-f]{6,}/gi
      const hexMatches = cleanCommandsStr.match(hexPattern) || []
      if (hexMatches.length > 0) {
        console.log('通过正则匹配提取可能的命令:', hexMatches)
        return hexMatches
      }

      console.warn('无法解析命令，返回空数组')
      return []
    } catch (error) {
      console.error('解析设备命令错误:', error)
      return []
    }
  }
}

// 导出单例实例
export const commandQueueManager = new CommandQueueManager()

/**
 * 发送蓝牙命令
 * @param functionKey 功能键名
 * @param intensity 强度值
 * @param deviceFunctions 设备功能列表
 */
export const sendBluetoothCommand = async (
  functionKey: string,
  intensity: number,
  deviceFunctions: DeviceFunction[] | undefined
): Promise<void> => {
  try {
    // 处理 4-9 模式，直接查找并广播模式命令
    if (intensity >= 4 && intensity <= 9) {
      const mode = modes.find(m => m.id === intensity)
      if (mode) {
        console.log(`发送模式命令: ${mode.command} (模式: ${mode.name}, id: ${mode.id})`)
        commandQueueManager.addCommands([mode.command])
        return
      } else {
        console.warn(`未找到 id 为 ${intensity} 的模式命令`)
      }
    }

    if (!deviceFunctions) {
      console.warn('无法发送蓝牙命令: 设备功能列表为空')
      return
    }

    const targetFunction = deviceFunctions.find(func => func.key === functionKey)

    if (!targetFunction) {
      console.warn(`无法发送蓝牙命令: 找不到功能 ${functionKey}`)
      return
    }

    const command = targetFunction.commands.find(cmd => cmd.intensity === intensity)

    if (!command) {
      // 尝试查找最接近的强度
      const closestCommand = findClosestIntensityCommand(targetFunction.commands, intensity)

      if (closestCommand) {
        // 发送最接近的命令
        console.log(
          `使用最接近的强度命令: ${closestCommand.command} (请求强度: ${intensity}, 实际强度: ${closestCommand.intensity})`
        )
        commandQueueManager.addCommands([closestCommand.command])
        return
      }

      console.warn(`无法发送蓝牙命令: 找不到强度 ${intensity} 的命令`)
      return
    }

    // 发送匹配的命令
    console.log(`发送精确匹配的命令: ${command.command} (功能: ${functionKey}, 强度: ${intensity})`)
    commandQueueManager.addCommands([command.command])
  } catch (error) {
    console.error('蓝牙命令发送过程中出错:', error)
    const t = i18n.getFixedT(null, 'interactive')
    addToast({
      title: t('toast.bluetoothCommandFailed.title'),
      description: t('toast.bluetoothCommandFailed.description'),
      color: 'danger'
    })
  }
}

/**
 * 查找最接近的强度命令
 * @param commands 命令列表
 * @param targetIntensity 目标强度
 * @returns 最接近的命令，如果没有找到则返回null
 */
const findClosestIntensityCommand = (
  commands: DeviceFunction['commands'],
  targetIntensity: number
) => {
  if (commands.length === 0) return null

  // 如果是关闭命令（-1），直接查找
  if (targetIntensity === -1) {
    return commands.find(cmd => cmd.intensity === -1) || null
  }

  // 对于正常强度，查找最接近的
  const positiveCommands = commands.filter(cmd => cmd.intensity > 0)
  if (positiveCommands.length === 0) return null

  return positiveCommands.reduce((prev, curr) =>
    Math.abs(curr.intensity - targetIntensity) < Math.abs(prev.intensity - targetIntensity)
      ? curr
      : prev
  )
}
