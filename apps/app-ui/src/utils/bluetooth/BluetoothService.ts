import { Capacitor } from '@capacitor/core'

/**
 * 动态导入BleAdvertiser插件
 * 如果插件不可用则返回null
 */
const getBleAdvertiser = async () => {
  try {
    if (!Capacitor.isNativePlatform()) {
      console.log('非原生环境，无法使用蓝牙广播')
      return null
    }

    // 使用动态导入确保错误处理
    console.log('尝试导入BleAdvertiser插件...')

    try {
      const module = await import('capacitor-ble-advertiser')
      console.log('BleAdvertiser插件模块导入成功!')

      if (!module.BleAdvertiser) {
        console.error('BleAdvertiser对象不存在')
        return null
      }

      // 确保插件接口可用
      console.log('检查BleAdvertiser接口...')
      if (typeof module.BleAdvertiser.initialize !== 'function') {
        console.error('BleAdvertiser.initialize方法不可用')
        return null
      }

      console.log('BleAdvertiser插件导入成功!')
      return module.BleAdvertiser
    } catch (importError) {
      console.error('导入BleAdvertiser模块失败:', importError)
      return null
    }
  } catch (error) {
    console.error('导入BleAdvertiser插件失败:', error)
    return null
  }
}

/**
 * 蓝牙服务类
 * 处理蓝牙广播
 */
export class BluetoothService {
  private static instance: BluetoothService
  private isInitialized = false
  private advertiserInitialized = false
  private activeAdvertisingId: number | null = null
  private bleAdvertiser: any = null

  // 单例模式
  public static getInstance(): BluetoothService {
    if (!BluetoothService.instance) {
      BluetoothService.instance = new BluetoothService()
    }
    return BluetoothService.instance
  }

  private constructor() {}

  /**
   * 初始化蓝牙服务
   */
  public async initialize(): Promise<boolean> {
    if (this.isInitialized) {
      console.log('蓝牙服务已初始化')
      return true
    }

    try {
      // 检查是否在原生应用环境中运行
      if (Capacitor.isNativePlatform()) {
        console.log('在原生环境中初始化蓝牙服务...')

        // 加载广播插件
        this.bleAdvertiser = await getBleAdvertiser()

        if (!this.bleAdvertiser) {
          console.error('蓝牙广播插件不可用')
          return false
        }

        // 初始化广播服务
        try {
          console.log('初始化蓝牙广播服务...')
          const result = await this.bleAdvertiser.initialize()
          console.log('蓝牙广播初始化结果:', result)

          const success = result?.success === true
          this.advertiserInitialized = success

          if (success) {
            this.isInitialized = true
            console.log('蓝牙服务初始化成功')
            return true
          } else {
            console.error('蓝牙广播服务初始化失败:', result?.message || '未知错误')
            return false
          }
        } catch (error) {
          console.error('蓝牙广播服务初始化错误:', error)
          if (error instanceof Error) {
            console.error('错误信息:', error.message)
            console.error('堆栈:', error.stack)
          }
          return false
        }
      } else {
        console.warn('非原生环境，模拟蓝牙功能')
        // 在非原生环境中模拟成功
        this.isInitialized = true
        return true
      }
    } catch (error) {
      console.error('蓝牙服务初始化失败:', error)
      if (error instanceof Error) {
        console.error('错误信息:', error.message)
        console.error('堆栈:', error.stack)
      }
      return false
    }
  }

  /**
   * 检查蓝牙是否已启用
   */
  public async checkBluetooth(): Promise<boolean> {
    if (!this.isInitialized) {
      console.log('蓝牙服务未初始化，尝试初始化...')
      const initResult = await this.initialize()
      if (!initResult) {
        console.error('蓝牙服务初始化失败')
        return false
      }
    }

    if (!Capacitor.isNativePlatform()) {
      console.warn('蓝牙检查仅在原生应用中可用')
      return true // 在非原生环境中模拟启用
    }

    try {
      if (!this.advertiserInitialized || !this.bleAdvertiser) {
        console.warn('蓝牙广播服务未初始化')
        return false
      }

      // 使用广播插件检查蓝牙状态
      console.log('检查蓝牙状态...')
      const result = await this.bleAdvertiser.isBluetoothEnabled()
      console.log('蓝牙状态检查结果:', result)

      const enabled = result?.enabled === true
      if (!enabled) {
        console.warn('蓝牙未启用')
      }
      return enabled
    } catch (error) {
      console.error('检查蓝牙状态失败:', error)
      if (error instanceof Error) {
        console.error('错误信息:', error.message)
        console.error('堆栈:', error.stack)
      }
      return false
    }
  }

  /**
   * 将十六进制字符串转换为字节数组
   * @param hexString 十六进制字符串
   */
  public static hexStringToBytes(hexString: string): Uint8Array {
    // 移除空格并转换为小写
    const cleanHex = hexString.replace(/\s/g, '').toLowerCase()

    // 确保长度为偶数
    const normalizedHex = cleanHex.length % 2 === 0 ? cleanHex : `0${cleanHex}`

    // 创建字节数组
    const bytes = new Uint8Array(normalizedHex.length / 2)

    for (let i = 0; i < normalizedHex.length; i += 2) {
      const byteValue = Number.parseInt(normalizedHex.substring(i, i + 2), 16)
      bytes[i / 2] = byteValue
    }

    return bytes
  }

  /**
   * 通过广告数据包广播命令
   *
   * @param command 十六进制命令字符串
   * @returns 是否成功发送广告
   */
  public async broadcastAdvertisement(command: string): Promise<boolean> {
    console.log(`开始广播广告，命令: ${command}`)

    if (!this.isInitialized) {
      console.log('蓝牙服务未初始化，尝试初始化...')
      const initResult = await this.initialize()
      if (!initResult) {
        console.error('蓝牙服务初始化失败')
        return false
      }
    }

    // 如果不在原生应用中，则模拟广播
    if (!Capacitor.isNativePlatform()) {
      console.log(`非原生应用，模拟广播广告数据: ${command}`)
      return true
    }

    // 检查广播服务是否已初始化
    if (!this.advertiserInitialized || !this.bleAdvertiser) {
      console.error('蓝牙广播服务未初始化或不可用，无法发送广告')
      return false
    }

    try {
      console.log('检查蓝牙是否已启用...')
      // 检查蓝牙是否已启用
      const result = await this.bleAdvertiser.isBluetoothEnabled()
      console.log(`蓝牙状态检查结果:`, result)

      const enabled = result?.enabled === true
      if (!enabled) {
        console.error('蓝牙未启用，无法发送广告')
        return false
      }

      // 如果有正在进行的广播，先停止
      if (this.activeAdvertisingId !== null) {
        console.log(`存在活动广播，实例ID: ${this.activeAdvertisingId}，尝试停止...`)
        await this.stopBroadcasting()
      }

      // 开始广播
      const manufacturerId = 255 // 使用默认制造商ID

      // 将十六进制字符串转换为字节数组
      const cleanedCommand = command.replace(/\s/g, '')
      const dataBytes = BluetoothService.hexStringToBytes(cleanedCommand)
      const dataArray = Array.from(dataBytes) // 转换为数字数组

      console.log(`准备开始广播，制造商ID: ${manufacturerId}, 模式: 2 (低功耗模式)`)
      console.log(`数据(Hex): ${cleanedCommand}`)
      console.log(`数据(Array): [${dataArray.join(', ')}]`)

      console.log('调用BleAdvertiser.startAdvertising...')
      try {
        const result = await this.bleAdvertiser.startAdvertising({
          manufacturerId,
          data: dataArray, // 使用数组格式，模拟Flutter的实现
          mode: 2 // 使用低功耗模式
        })

        console.log(`广播调用结果:`, JSON.stringify(result))

        const success = result?.success === true
        const instanceId = result?.instanceId
        const message = result?.message

        if (success && instanceId !== undefined) {
          this.activeAdvertisingId = instanceId
          console.log(`广播已启动, ID: ${instanceId}, 数据: ${command}`)
          return true
        } else {
          console.error(`启动广播失败: ${message || '未知错误'}`)
          return false
        }
      } catch (error) {
        console.error('调用startAdvertising时发生错误:', error)
        if (error instanceof Error) {
          console.error('错误信息:', error.message)
          console.error('堆栈:', error.stack)
        }
        throw error
      }
    } catch (error) {
      console.error('广播广告数据失败:', error)
      // 打印详细错误信息
      if (error instanceof Error) {
        console.error('错误详情:', {
          name: error.name,
          message: error.message,
          stack: error.stack
        })
      } else {
        console.error('未知错误类型:', error)
      }
      return false
    }
  }

  /**
   * 停止广播
   * @returns 是否成功停止广播
   */
  public async stopBroadcasting(): Promise<boolean> {
    if (!Capacitor.isNativePlatform()) {
      console.log('非原生应用，模拟停止广播')
      this.activeAdvertisingId = null
      return true
    }

    if (!this.advertiserInitialized || !this.bleAdvertiser) {
      console.error('蓝牙广播服务未初始化或不可用，无法停止广播')
      return false
    }

    if (this.activeAdvertisingId === null) {
      console.log('没有活动的广播实例，无需停止')
      return true
    }

    try {
      console.log(`停止广播，实例ID: ${this.activeAdvertisingId}`)
      const result = await this.bleAdvertiser.stopAdvertising({
        instanceId: this.activeAdvertisingId
      })

      console.log('停止广播结果:', result)

      const success = result?.success === true
      if (success) {
        console.log(`成功停止广播，实例ID: ${this.activeAdvertisingId}`)
        this.activeAdvertisingId = null
        return true
      } else {
        console.error(`停止广播失败: ${result?.message || '未知错误'}`)
        return false
      }
    } catch (error) {
      console.error('停止广播出错:', error)
      if (error instanceof Error) {
        console.error('错误详情:', {
          name: error.name,
          message: error.message,
          stack: error.stack
        })
      }
      return false
    }
  }

  /**
   * 广播命令
   * @param command 十六进制命令字符串
   * @returns 是否成功发送命令
   */
  public async broadcastCommand(command: string): Promise<boolean> {
    try {
      console.log(`准备广播命令: ${command}`)

      // 检查蓝牙状态
      const bluetoothEnabled = await this.checkBluetooth()
      if (!bluetoothEnabled) {
        console.error('蓝牙未启用，无法广播命令')
        return false
      }

      // 确保之前的广播已停止
      if (this.activeAdvertisingId !== null) {
        console.log('尝试停止之前的广播')
        await this.stopBroadcasting()
        // 增加停止后的等待时间
        await new Promise(resolve => setTimeout(resolve, 200))
      }

      // 发送广播
      const result = await this.broadcastAdvertisement(command)

      // 等待一段时间，确保命令被接收
      if (result) {
        console.log(`命令广播成功: ${command}`)
        // 广播持续一小段时间后自动停止，增加广播时间
        setTimeout(async () => {
          try {
            await this.stopBroadcasting()
            console.log('自动停止广播')
          } catch (error) {
            console.error('自动停止广播失败:', error)
          }
        }, 1000) // 增加到1000ms
      }

      return result
    } catch (error) {
      console.error('广播命令失败:', error)
      if (error instanceof Error) {
        console.error('错误详情:', {
          name: error.name,
          message: error.message,
          stack: error.stack
        })
      }
      return false
    }
  }
}

// 导出单例实例
export const bluetoothService = BluetoothService.getInstance()
