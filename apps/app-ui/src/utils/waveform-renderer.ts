/**
 * 波形绘制引擎
 * 统一WaveformViewer和WaveformPreview的绘制逻辑
 */

export interface WaveformPoint {
  intensity: number
  timestamp?: number
}

export interface WaveformRenderOptions {
  width: number
  height: number
  data: WaveformPoint[]
  showAnimation?: boolean
  currentIntensity?: number
  showCurrentIndicator?: boolean
  lineWidth?: number
  pulseThreshold?: number
  glowThreshold?: number
}

export class WaveformRenderer {
  private static getIntensityColor(intensity: number): string {
    if (intensity >= 7) return '#ff2d97' // 高强度 - 粉色
    if (intensity >= 4) return '#892fff' // 中强度 - 紫色
    if (intensity >= 2) return '#00d4ff' // 中低强度 - 蓝色
    return '#4ade80' // 低强度 - 绿色
  }

  private static drawBackground(ctx: CanvasRenderingContext2D, width: number, height: number) {
    // 绘制背景渐变
    const backgroundGradient = ctx.createLinearGradient(0, 0, 0, height)
    backgroundGradient.addColorStop(0, 'rgba(29, 33, 53, 0.8)')
    backgroundGradient.addColorStop(0.5, 'rgba(29, 33, 53, 0.5)')
    backgroundGradient.addColorStop(1, 'rgba(29, 33, 53, 0.3)')
    ctx.fillStyle = backgroundGradient
    ctx.fillRect(0, 0, width, height)
  }

  private static drawWaveformSegment(
    ctx: CanvasRenderingContext2D,
    prevX: number,
    prevY: number,
    currentX: number,
    currentY: number,
    prevIntensity: number,
    currentIntensity: number,
    time: number,
    index: number,
    options: {
      showAnimation: boolean
      lineWidth: number
      glowThreshold: number
      pulseThreshold: number
    }
  ) {
    // 创建渐变色
    const lineGradient = ctx.createLinearGradient(prevX, prevY, currentX, currentY)
    lineGradient.addColorStop(0, this.getIntensityColor(prevIntensity))
    lineGradient.addColorStop(1, this.getIntensityColor(currentIntensity))

    // 绘制主线条
    ctx.globalAlpha = 0.9
    ctx.strokeStyle = lineGradient
    ctx.lineWidth = options.showAnimation
      ? options.lineWidth + Math.sin(time * 2 + index * 0.2) * 1
      : options.lineWidth

    ctx.beginPath()
    ctx.moveTo(prevX, prevY)
    ctx.lineTo(currentX, currentY)
    ctx.stroke()

    // 为高强度点添加发光效果
    if (currentIntensity > options.glowThreshold) {
      ctx.globalAlpha = options.showAnimation ? 0.3 + Math.sin(time * 4 + index * 0.4) * 0.1 : 0.3
      ctx.strokeStyle = this.getIntensityColor(currentIntensity)
      ctx.lineWidth = 6
      ctx.filter = 'blur(2px)'

      ctx.beginPath()
      ctx.moveTo(prevX, prevY)
      ctx.lineTo(currentX, currentY)
      ctx.stroke()

      ctx.filter = 'none'
    }

    // 绘制数据点脉冲效果（仅在动画模式下）
    if (options.showAnimation && currentIntensity > options.pulseThreshold && index % 6 === 0) {
      const pulseRadius = 2 + Math.sin(time * 5 + index * 0.8) * 1.5
      const pulseOpacity = 0.5 + Math.sin(time * 4 + index * 0.6) * 0.4

      ctx.globalAlpha = pulseOpacity
      ctx.fillStyle = this.getIntensityColor(currentIntensity)

      ctx.beginPath()
      ctx.arc(currentX, currentY, pulseRadius, 0, Math.PI * 2)
      ctx.fill()

      // 外圈光晕
      ctx.globalAlpha = 0.1
      ctx.beginPath()
      ctx.arc(currentX, currentY, pulseRadius * 1.8, 0, Math.PI * 2)
      ctx.fill()
    }
  }

  private static drawCurrentIndicator(
    ctx: CanvasRenderingContext2D,
    width: number,
    height: number,
    currentIntensity: number,
    time: number
  ) {
    const y = height - (currentIntensity / 9) * height
    const breathe = 0.5 + Math.sin(time * 3) * 0.3

    // 主指示线
    ctx.strokeStyle = '#ffffff'
    ctx.lineWidth = 2
    ctx.setLineDash([4, 4])
    ctx.globalAlpha = breathe

    ctx.beginPath()
    ctx.moveTo(0, y)
    ctx.lineTo(width, y)
    ctx.stroke()

    // 发光效果
    ctx.strokeStyle = this.getIntensityColor(currentIntensity)
    ctx.lineWidth = 3
    ctx.globalAlpha = breathe * 0.4
    ctx.filter = 'blur(2px)'

    ctx.beginPath()
    ctx.moveTo(0, y)
    ctx.lineTo(width, y)
    ctx.stroke()

    ctx.filter = 'none'
    ctx.setLineDash([])
  }

  private static drawBreathingEffect(
    ctx: CanvasRenderingContext2D,
    data: WaveformPoint[],
    width: number,
    height: number,
    time: number
  ) {
    const breathe = Math.sin(time * 1.2) * 0.1 + 0.9
    ctx.globalAlpha = breathe * 0.15
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.4)'
    ctx.lineWidth = 1

    // 重新描边整条波形，创造呼吸效果
    for (let i = 1; i < data.length; i++) {
      const prevIntensity = data[i - 1].intensity
      const currentIntensity = data[i].intensity

      const prevX = ((i - 1) / (data.length - 1)) * width
      const prevY = height - (prevIntensity / 9) * height
      const currentX = (i / (data.length - 1)) * width
      const currentY = height - (currentIntensity / 9) * height

      ctx.beginPath()
      ctx.moveTo(prevX, prevY)
      ctx.lineTo(currentX, currentY)
      ctx.stroke()
    }
  }

  /**
   * 渲染波形
   */
  static render(canvas: HTMLCanvasElement, options: WaveformRenderOptions): void {
    const ctx = canvas.getContext('2d')
    if (!ctx) return

    const {
      width,
      height,
      data,
      showAnimation = false,
      currentIntensity = 0,
      showCurrentIndicator = false,
      lineWidth = 3,
      pulseThreshold = 3,
      glowThreshold = 5
    } = options

    const dpr = window.devicePixelRatio || 1

    // 设置Canvas的实际尺寸（考虑高DPI）
    canvas.width = width * dpr
    canvas.height = height * dpr

    // 设置Canvas的显示尺寸
    canvas.style.width = width + 'px'
    canvas.style.height = height + 'px'

    // 缩放绘制上下文以适应高DPI
    ctx.scale(dpr, dpr)

    // 清空画布
    ctx.clearRect(0, 0, width, height)

    // 绘制背景
    this.drawBackground(ctx, width, height)

    // 检查数据有效性
    if (data.length < 2) return

    // 绘制波形线段
    ctx.lineCap = 'round'
    ctx.lineJoin = 'round'

    const time = showAnimation ? Date.now() * 0.002 : 0

    for (let i = 1; i < data.length; i++) {
      const prevPoint = data[i - 1]
      const currentPoint = data[i]

      const prevX = ((i - 1) / (data.length - 1)) * width
      const prevY = height - (prevPoint.intensity / 9) * height
      const currentX = (i / (data.length - 1)) * width
      const currentY = height - (currentPoint.intensity / 9) * height

      this.drawWaveformSegment(
        ctx,
        prevX,
        prevY,
        currentX,
        currentY,
        prevPoint.intensity,
        currentPoint.intensity,
        time,
        i,
        {
          showAnimation,
          lineWidth,
          glowThreshold,
          pulseThreshold
        }
      )
    }

    // 绘制当前强度指示线
    if (showCurrentIndicator && currentIntensity > 0) {
      this.drawCurrentIndicator(ctx, width, height, currentIntensity, time)
    }

    // 如果启用动画，添加整体呼吸效果
    if (showAnimation) {
      this.drawBreathingEffect(ctx, data, width, height, time)
    }

    ctx.globalAlpha = 1
  }

  /**
   * 生成缩略图
   */
  static generateThumbnail(
    data: WaveformPoint[],
    width: number = 200,
    height: number = 40
  ): Promise<string> {
    return new Promise(resolve => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')

      if (!ctx) {
        resolve('')
        return
      }

      canvas.width = width
      canvas.height = height

      // 使用简化的绘制逻辑
      this.drawBackground(ctx, width, height)

      if (data.length < 2) {
        resolve(canvas.toDataURL())
        return
      }

      ctx.lineWidth = 2
      ctx.lineCap = 'round'

      for (let i = 1; i < data.length; i++) {
        const intensity = data[i].intensity
        const color = this.getIntensityColor(intensity)

        const x = (i / (data.length - 1)) * width
        const y = height - (intensity / 9) * height

        ctx.strokeStyle = color
        ctx.beginPath()

        if (i === 1) {
          const prevX = ((i - 1) / (data.length - 1)) * width
          const prevY = height - (data[i - 1].intensity / 9) * height
          ctx.moveTo(prevX, prevY)
        }

        ctx.lineTo(x, y)
        ctx.stroke()
      }

      resolve(canvas.toDataURL())
    })
  }
}
