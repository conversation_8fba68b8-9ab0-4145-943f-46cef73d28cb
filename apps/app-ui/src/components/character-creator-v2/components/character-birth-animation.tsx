import React, { useState, useEffect } from 'react'
import { Button } from '@heroui/react'
import { Icon } from '@iconify/react'
import { motion, AnimatePresence } from 'framer-motion'
import { useRoleStore } from '@/stores/role-store'
import { generateUUID } from '@/lib/utils'
import { useTranslation } from 'react-i18next'
import { useSmartNavigation } from '@/lib/navigation'

interface CharacterBirthAnimationProps {
  isVisible: boolean
  characterName: string
  characterGender: 'male' | 'female'
  generatedImage: string | null
  onComplete: () => void
  onClose?: () => void // 新增关闭回调
  characterId?: string | null // 新增角色ID
}

type AnimationStage = 'trigger' | 'generating' | 'revealing' | 'completed'

export const CharacterBirthAnimation: React.FC<CharacterBirthAnimationProps> = ({
  isVisible,
  characterName,
  characterGender,
  generatedImage,
  onComplete,
  onClose,
  characterId
}) => {
  const { t } = useTranslation('character-creator-v2')
  const { smartNavigate } = useSmartNavigation()
  const [stage, setStage] = useState<AnimationStage>('trigger')
  const [imageLoaded, setImageLoaded] = useState(false)
  const [isChatLoading, setIsChatLoading] = useState(false)

  // 使用全局角色状态管理
  const { setRole: setGlobalRole } = useRoleStore()

  // 阶段控制逻辑
  useEffect(() => {
    if (!isVisible) {
      setStage('trigger')
      setImageLoaded(false)
      return
    }

    // 第一阶段：显示柔光圆环 (2秒)
    const timer1 = setTimeout(() => {
      setStage('generating')
    }, 2000)

    return () => clearTimeout(timer1)
  }, [isVisible])

  // 当图片生成完成时，切换到显现阶段
  useEffect(() => {
    if (generatedImage && imageLoaded && stage === 'generating') {
      console.log('🎭 图片已加载，准备显现阶段', { generatedImage, imageLoaded, stage })

      const timer2 = setTimeout(() => {
        console.log('🎨 切换到显现阶段')
        setStage('revealing')
      }, 500) // 缩短等待时间

      return () => {
        clearTimeout(timer2)
      }
    }
  }, [generatedImage, imageLoaded, stage])

  // 显现阶段结束后，切换到完成阶段
  useEffect(() => {
    if (stage === 'revealing') {
      const timer3 = setTimeout(() => {
        console.log('🎉 切换到完成阶段')
        setStage('completed')
      }, 3000) // 显现阶段持续3秒

      return () => {
        clearTimeout(timer3)
      }
    }
  }, [stage])

  // 图片预加载
  useEffect(() => {
    if (generatedImage) {
      console.log('🖼️ 开始预加载图片:', generatedImage)
      const img = new Image()
      img.onload = () => {
        console.log('✅ 图片加载完成')
        setImageLoaded(true)
      }
      img.onerror = () => {
        console.error('❌ 图片加载失败:', generatedImage)
        setImageLoaded(true) // 即使失败也设为true，避免卡住
      }
      img.src = generatedImage
    } else {
      setImageLoaded(false)
    }
  }, [generatedImage])

  // 添加当前阶段的调试日志
  useEffect(() => {
    console.log('🎭 当前动画阶段:', stage, {
      isVisible,
      generatedImage: !!generatedImage,
      imageLoaded
    })
  }, [stage, isVisible, generatedImage, imageLoaded])

  // 根据性别获取文案 - 多语言版本
  const getGenderText = (text: string) => {
    // 获取性别代词
    const pronoun =
      characterGender === 'female'
        ? t('genders.pronoun_female', '她')
        : t('genders.pronoun_male', '他')

    // 替换中文代词（用于中文翻译的向后兼容）
    return text.replace(/她|他/g, pronoun)
  }

  if (!isVisible) return null

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className="fixed inset-0 z-[9999] overflow-hidden"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          style={{ backgroundColor: 'transparent' }}
        >
          {/* 背景遮罩 - 毛玻璃效果 */}
          <motion.div
            className="absolute inset-0"
            initial={{ opacity: 0 }}
            animate={{
              opacity: stage === 'trigger' ? 0.4 : 0.8
            }}
            style={{
              backdropFilter: stage === 'trigger' ? 'blur(8px)' : 'blur(16px)',
              background: stage === 'trigger' ? 'rgba(0, 0, 0, 0.1)' : 'rgba(0, 0, 0, 0.3)'
            }}
            transition={{ duration: 1, ease: 'easeOut' }}
          />

          {/* 【阶段1】柔光圆环 */}
          <AnimatePresence>
            {stage === 'trigger' && (
              <motion.div
                className="absolute inset-0 flex items-center justify-center"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.5 }}
              >
                <div className="relative">
                  {/* 外圆环 - 旋转收缩 */}
                  <motion.div
                    className="w-32 h-32 border-4 rounded-full"
                    style={{ borderColor: 'rgba(233, 30, 99, 0.6)' }}
                    initial={{ opacity: 0, scale: 1.2, rotate: 0 }}
                    animate={{
                      opacity: 1,
                      scale: 1,
                      rotate: 360
                    }}
                    transition={{
                      opacity: { duration: 0.8, ease: 'easeOut' },
                      scale: { duration: 0.8, ease: 'easeOut' },
                      rotate: { duration: 3, ease: 'linear', repeat: Infinity }
                    }}
                  >
                    <motion.div
                      className="w-full h-full border-2 rounded-full"
                      style={{ borderColor: 'rgba(233, 30, 99, 0.4)' }}
                      animate={{
                        scale: [1, 1.05, 1],
                        opacity: [0.4, 1, 0.4]
                      }}
                      transition={{
                        duration: 2,
                        ease: 'easeInOut',
                        repeat: Infinity
                      }}
                    />
                  </motion.div>

                  {/* 内圆环 - 反向旋转 */}
                  <motion.div
                    className="absolute inset-4 border-2 rounded-full"
                    style={{ borderColor: 'rgba(233, 30, 99, 0.8)' }}
                    initial={{ opacity: 0, scale: 0.8, rotate: 0 }}
                    animate={{
                      opacity: 1,
                      scale: 1,
                      rotate: -360
                    }}
                    transition={{
                      opacity: { duration: 1.2, delay: 0.3, ease: 'easeOut' },
                      scale: { duration: 1.2, delay: 0.3, ease: 'easeOut' },
                      rotate: { duration: 4, ease: 'linear', repeat: Infinity }
                    }}
                  >
                    <div
                      className="w-full h-full rounded-full"
                      style={{
                        background:
                          'linear-gradient(to right, rgba(233, 30, 99, 0.2), rgba(233, 30, 99, 0.3))'
                      }}
                    />
                  </motion.div>

                  {/* 中心光点 */}
                  <motion.div
                    className="absolute inset-1/2 w-2 h-2 -ml-1 -mt-1 rounded-full"
                    style={{ backgroundColor: 'rgb(233, 30, 99)' }}
                    animate={{
                      scale: [1, 1.5, 1],
                      opacity: [1, 0.5, 1]
                    }}
                    transition={{
                      duration: 1,
                      ease: 'easeInOut',
                      repeat: Infinity
                    }}
                  />

                  {/* 中心文字提示 */}
                  <div className="absolute inset-0 flex items-center justify-center mt-20">
                    <motion.div
                      className="text-center"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 1, delay: 0.5 }}
                    >
                      <h3 className="text-lg font-light text-white mb-1">
                        {t('birth_animation.preparing_summon', { name: characterName })}
                      </h3>
                      <p className="text-sm text-pink-100/90">
                        {getGenderText(t('birth_animation.about_to_be_born'))}
                      </p>
                    </motion.div>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* 【阶段2】生成中 - 粒子光流 */}
          <AnimatePresence>
            {stage === 'generating' && (
              <motion.div
                className="absolute inset-0 flex items-center justify-center"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.5 }}
              >
                {/* 粒子背景 */}
                <div className="absolute inset-0">
                  {[...Array(20)].map((_, i) => (
                    <motion.div
                      key={i}
                      className="absolute w-1 h-1 rounded-full"
                      style={{
                        backgroundColor: 'rgba(233, 30, 99, 0.6)',
                        left: `${20 + Math.random() * 60}%`,
                        top: `${20 + Math.random() * 60}%`
                      }}
                      animate={{
                        y: [-20, 20, -20],
                        x: [0, 10, 0],
                        opacity: [0.3, 0.8, 0.3]
                      }}
                      transition={{
                        duration: 3 + Math.random() * 2,
                        ease: 'easeInOut',
                        repeat: Infinity,
                        delay: Math.random() * 3
                      }}
                    />
                  ))}
                </div>

                {/* 中央光晕 */}
                <div className="relative text-center">
                  <motion.div
                    className="w-40 h-40 rounded-full"
                    style={{
                      background:
                        'radial-gradient(circle, rgba(233, 30, 99, 0.3) 0%, rgba(233, 30, 99, 0.2) 50%, transparent 100%)'
                    }}
                    animate={{
                      scale: [1, 1.1, 1],
                      opacity: [0.7, 1, 0.7]
                    }}
                    transition={{
                      duration: 2,
                      ease: 'easeInOut',
                      repeat: Infinity
                    }}
                  />

                  {/* 生成文字 */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center">
                      <motion.h2
                        className="text-2xl font-light text-white mb-2"
                        animate={{
                          opacity: [0.8, 1, 0.8],
                          scale: [1, 1.02, 1]
                        }}
                        transition={{
                          duration: 3,
                          ease: 'easeInOut',
                          repeat: Infinity
                        }}
                      >
                        {getGenderText(t('birth_animation.character_being_born'))}
                      </motion.h2>
                      <motion.p
                        className="text-sm text-pink-100/90"
                        animate={{
                          opacity: [0.6, 1, 0.6]
                        }}
                        transition={{
                          duration: 2,
                          ease: 'easeInOut',
                          repeat: Infinity
                        }}
                      >
                        {getGenderText(t('birth_animation.descending'))}
                      </motion.p>
                    </div>
                  </div>
                </div>

                {/* 环绕光流 */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <motion.div
                    className="w-60 h-60 border rounded-full"
                    style={{ borderColor: 'rgba(233, 30, 99, 0.3)' }}
                    animate={{ rotate: 360 }}
                    transition={{
                      duration: 8,
                      ease: 'linear',
                      repeat: Infinity
                    }}
                  >
                    <div className="relative w-full h-full">
                      {[...Array(8)].map((_, i) => (
                        <motion.div
                          key={i}
                          className="absolute w-2 h-2 rounded-full"
                          style={{
                            backgroundColor: 'rgba(233, 30, 99, 0.7)',
                            top: '50%',
                            left: '50%',
                            transform: `rotate(${i * 45}deg) translateY(-120px) translateX(-4px)`
                          }}
                          animate={{
                            opacity: [0.4, 1, 0.4],
                            scale: [0.8, 1.2, 0.8]
                          }}
                          transition={{
                            duration: 1.5,
                            ease: 'easeInOut',
                            repeat: Infinity,
                            delay: i * 0.2
                          }}
                        />
                      ))}
                    </div>
                  </motion.div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* 【阶段3】角色觉醒 - 轮廓显现 */}
          <AnimatePresence>
            {stage === 'revealing' && generatedImage && imageLoaded && (
              <motion.div
                className="absolute inset-0 flex items-center justify-center"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.5 }}
              >
                {/* 神秘轮廓容器 */}
                <div className="relative">
                  <motion.div
                    className="w-72 h-96 rounded-2xl relative overflow-hidden"
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ duration: 1, ease: 'easeOut' }}
                  >
                    {/* 人形轮廓 */}
                    <motion.div
                      className="absolute inset-0 rounded-2xl"
                      style={{
                        background:
                          'linear-gradient(45deg, rgba(233, 30, 99, 0.1), rgba(233, 30, 99, 0.05))',
                        border: '2px solid rgba(233, 30, 99, 0.3)',
                        boxShadow:
                          'inset 0 0 50px rgba(233, 30, 99, 0.2), 0 0 50px rgba(233, 30, 99, 0.3)'
                      }}
                      animate={{
                        boxShadow: [
                          'inset 0 0 50px rgba(233, 30, 99, 0.2), 0 0 50px rgba(233, 30, 99, 0.3)',
                          'inset 0 0 80px rgba(233, 30, 99, 0.4), 0 0 80px rgba(233, 30, 99, 0.5)',
                          'inset 0 0 50px rgba(233, 30, 99, 0.2), 0 0 50px rgba(233, 30, 99, 0.3)'
                        ]
                      }}
                      transition={{ duration: 2, ease: 'easeInOut', repeat: Infinity }}
                    />

                    {/* 神秘光粒子 */}
                    {[...Array(12)].map((_, i) => (
                      <motion.div
                        key={i}
                        className="absolute w-1 h-1 rounded-full"
                        style={{
                          backgroundColor: 'rgb(233, 30, 99)',
                          left: `${20 + Math.random() * 60}%`,
                          top: `${20 + Math.random() * 60}%`
                        }}
                        animate={{
                          scale: [0, 1, 0],
                          opacity: [0, 1, 0]
                        }}
                        transition={{
                          duration: 2,
                          ease: 'easeInOut',
                          repeat: Infinity,
                          delay: i * 0.2
                        }}
                      />
                    ))}

                    {/* 中心脉冲 */}
                    <motion.div
                      className="absolute top-1/2 left-1/2 w-4 h-4 -ml-2 -mt-2 rounded-full"
                      style={{ backgroundColor: 'rgb(233, 30, 99)' }}
                      animate={{
                        scale: [1, 3, 1],
                        opacity: [1, 0.3, 1]
                      }}
                      transition={{
                        duration: 3,
                        ease: 'easeInOut',
                        repeat: Infinity
                      }}
                    />
                  </motion.div>

                  {/* 觉醒文字 */}
                  <div className="absolute -bottom-20 left-1/2 transform -translate-x-1/2 text-center">
                    <motion.h2
                      className="text-2xl font-light text-white"
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 1, delay: 0.5, ease: 'easeOut' }}
                    >
                      {t('birth_animation.awakening', { name: characterName })}
                    </motion.h2>
                    <motion.p
                      className="text-base text-pink-100/90 mt-2"
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 1, delay: 0.8, ease: 'easeOut' }}
                    >
                      {getGenderText(t('birth_animation.feel_presence'))}
                    </motion.p>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* 【阶段4】完成阶段 - 角色展示和操作 */}
          <AnimatePresence>
            {stage === 'completed' && generatedImage && imageLoaded && (
              <motion.div
                className="absolute inset-0 flex items-center justify-center"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.5 }}
              >
                <div className="relative text-center">
                  {/* 角色图片容器 - 带流光效果 */}
                  <motion.div
                    className="relative w-72 h-96 mb-8"
                    animate={{
                      y: [0, -5, 0]
                    }}
                    transition={{
                      duration: 4,
                      ease: 'easeInOut',
                      repeat: Infinity
                    }}
                  >
                    {/* 流光背景效果 */}
                    <div className="absolute inset-0 -m-6">
                      {/* 外层旋转光环 */}
                      <motion.div
                        className="absolute inset-0 rounded-full opacity-40"
                        style={{
                          background:
                            'conic-gradient(from 0deg, #ec4899, #8b5cf6, #06b6d4, #10b981, #f59e0b, #ec4899)',
                          filter: 'blur(15px)'
                        }}
                        animate={{
                          rotate: 360
                        }}
                        transition={{
                          duration: 8,
                          repeat: Infinity,
                          ease: 'linear'
                        }}
                      />

                      {/* 内层反向旋转光环 */}
                      <motion.div
                        className="absolute inset-3 rounded-full opacity-30"
                        style={{
                          background:
                            'conic-gradient(from 180deg, #8b5cf6, #ec4899, #06b6d4, #8b5cf6)',
                          filter: 'blur(12px)'
                        }}
                        animate={{
                          rotate: -360
                        }}
                        transition={{
                          duration: 12,
                          repeat: Infinity,
                          ease: 'linear'
                        }}
                      />
                    </div>

                    {/* 卡片边框发光效果 */}
                    <div className="absolute -inset-1 rounded-2xl opacity-75">
                      <motion.div
                        className="w-full h-full rounded-2xl"
                        style={{
                          background: 'linear-gradient(45deg, #ec4899, #8b5cf6, #06b6d4, #10b981)',
                          filter: 'blur(6px)'
                        }}
                        animate={{
                          opacity: [0.4, 0.8, 0.4]
                        }}
                        transition={{
                          duration: 3,
                          repeat: Infinity,
                          ease: 'easeInOut'
                        }}
                      />
                    </div>

                    {/* 主图片 */}
                    <motion.div
                      className="relative z-10 w-full h-full rounded-2xl overflow-hidden shadow-2xl"
                      whileHover={{ scale: 1.02 }}
                      transition={{ duration: 0.3 }}
                    >
                      <img
                        src={generatedImage}
                        alt={characterName}
                        className="w-full h-full object-cover"
                      />

                      {/* 图片上的光效 */}
                      <motion.div
                        className="absolute inset-0"
                        style={{
                          background:
                            'linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%)',
                          filter: 'blur(1px)'
                        }}
                        animate={{
                          x: ['-100%', '100%']
                        }}
                        transition={{
                          duration: 3,
                          repeat: Infinity,
                          ease: 'easeInOut',
                          repeatDelay: 2
                        }}
                      />
                    </motion.div>

                    {/* 环绕粒子效果 */}
                    <div className="absolute inset-0 pointer-events-none">
                      {[...Array(8)].map((_, i) => (
                        <motion.div
                          key={i}
                          className="absolute w-2 h-2 bg-white rounded-full opacity-60"
                          style={{
                            left: `${50 + 40 * Math.cos((i * Math.PI * 2) / 8)}%`,
                            top: `${50 + 40 * Math.sin((i * Math.PI * 2) / 8)}%`
                          }}
                          animate={{
                            scale: [0, 1, 0],
                            opacity: [0, 0.8, 0],
                            rotate: [0, 360]
                          }}
                          transition={{
                            duration: 2,
                            repeat: Infinity,
                            delay: i * 0.25,
                            ease: 'easeInOut'
                          }}
                        />
                      ))}
                    </div>
                  </motion.div>

                  {/* 角色信息 */}
                  <motion.div
                    className="mb-8"
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, ease: 'easeOut' }}
                  >
                    <h1 className="text-3xl font-light text-white mb-2">{characterName}</h1>
                    <p className="text-pink-100/90">
                      {getGenderText(t('birth_animation.opened_eyes'))}
                    </p>
                  </motion.div>

                  {/* 操作按钮 */}
                  <motion.div
                    className="flex gap-4 justify-center"
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.4, ease: 'easeOut' }}
                  >
                    <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                      <Button
                        size="lg"
                        variant="bordered"
                        className="border-primary text-white font-medium px-8 py-3 rounded-xl hover:bg-primary/10 transition-all duration-300"
                        onPress={() => {
                          // 关闭动画然后跳转到首页
                          onClose?.()
                          onComplete()
                          setTimeout(() => smartNavigate('/'), 300)
                        }}
                      >
                        {t('birth_animation.back_to_home')}
                      </Button>
                    </motion.div>

                    <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                      <Button
                        size="lg"
                        className="bg-gradient-to-r from-primary to-primary/80 text-white font-medium px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                        isLoading={isChatLoading}
                        isDisabled={!characterId || isChatLoading}
                        onPress={async () => {
                          if (!characterId || isChatLoading) return

                          setIsChatLoading(true)
                          try {
                            // 关闭动画
                            onClose?.()
                            onComplete()

                            // 设置全局角色状态
                            await setGlobalRole(characterId)

                            // 生成新的聊天UUID
                            const newChatId = generateUUID()
                            console.log('🎭 角色诞生完成，开始新聊天:', { characterId, newChatId })

                            // 跳转到新聊天页面
                            setTimeout(() => {
                              smartNavigate(`/chat/${newChatId}`)
                            }, 300)
                          } catch (error) {
                            console.error('设置角色或创建聊天失败:', error)
                            // 即使失败也尝试跳转
                            const fallbackChatId = generateUUID()
                            setTimeout(() => {
                              smartNavigate(`/chat/${fallbackChatId}`)
                            }, 300)
                          } finally {
                            setIsChatLoading(false)
                          }
                        }}
                      >
                        {isChatLoading
                          ? t('birth_animation.preparing')
                          : characterId
                          ? t('birth_animation.chat_now')
                          : t('birth_animation.character_generating')}
                      </Button>
                    </motion.div>
                  </motion.div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
