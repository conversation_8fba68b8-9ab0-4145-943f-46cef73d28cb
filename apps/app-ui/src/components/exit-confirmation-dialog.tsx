import React from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  ModalHeader,
  ModalBody,
  Modal<PERSON>ooter,
  Button,
  ModalProps
} from '@heroui/react'
import { useTranslation } from 'react-i18next'

interface ExitConfirmationDialogProps extends Omit<ModalProps, 'children'> {
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: () => void
  onCancel: () => void
}

/**
 * 退出确认弹窗组件
 * 当用户试图在没有上一页面的情况下左滑退出时显示
 */
export function ExitConfirmationDialog({
  open,
  onOpenChange,
  onConfirm,
  onCancel,
  ...modalProps
}: ExitConfirmationDialogProps) {
  const { t } = useTranslation('common')

  return (
    <Modal
      isOpen={open}
      onOpenChange={onOpenChange}
      placement="center"
      backdrop="blur"
      classNames={{
        base: 'bg-transparent shadow-none',
        wrapper: 'bg-transparent',
        backdrop: 'bg-black/60 backdrop-blur-sm border-none'
      }}
      {...modalProps}
    >
      <ModalContent className="bg-transparent shadow-none w-[85%] mx-4 rounded-3xl">
        {onClose => (
          <div className="relative">
            {/* 主要内容容器 */}
            <div className="relative z-10 backdrop-blur-xl rounded-3xl overflow-hidden bg-background">
              {/* 左上角渐变装饰 */}
              <div className="absolute top-0 left-0 size-full pointer-events-none z-1">
                <img
                  src="/images/decorate/decorate-9.svg"
                  alt=""
                  className="w-full h-full opacity-80"
                />
              </div>

              {/* 模态框头部 */}
              <ModalHeader className="relative z-10 flex items-center justify-center p-6 pb-2">
                <h3 className="text-white text-base font-semibold">{t('exitDialog.title')}</h3>
              </ModalHeader>

              {/* 模态框内容 */}
              <ModalBody className="relative z-10 px-6 pb-4">
                <div className="text-white/90 text-center">{t('exitDialog.message')}</div>
              </ModalBody>

              {/* 模态框底部 */}
              <ModalFooter className="relative z-10 p-6 pt-4">
                <div className="flex gap-3 w-full">
                  <Button
                    variant="bordered"
                    onPress={() => {
                      onCancel()
                      onClose()
                    }}
                    className="flex-1 border-white/20 text-white hover:bg-white/10 transition-colors h-12 px-11 py-3.5 rounded-2xl"
                  >
                    {t('exitDialog.cancel')}
                  </Button>
                  <Button
                    onPress={() => {
                      onConfirm()
                      onClose()
                    }}
                    className="flex-1 text-white font-medium transition-all bg-red-500 hover:bg-red-600 h-12 px-11 py-3.5 rounded-2xl"
                  >
                    {t('exitDialog.exit')}
                  </Button>
                </div>
              </ModalFooter>
            </div>
          </div>
        )}
      </ModalContent>
    </Modal>
  )
}
