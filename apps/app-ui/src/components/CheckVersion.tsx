import { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import {
  <PERSON>dal,
  Modal<PERSON>ontent,
  ModalHeader,
  ModalBody,
  Button,
  useDisclosure,
  Divider,
  Progress,
  Chip
} from '@heroui/react'
import { Icon } from '@iconify/react'
import { apiClient } from '@/api/client'
import { App } from '@capacitor/app'
import { Device } from '@capacitor/device'
import { apkUpdater, type ApkDownloadProgress } from '@/services/apk-updater'

interface VersionInfo {
  versionName: string
  versionCode: number
  fileUrl: string
  fileSize?: number
  releaseNotes?: string
}

interface UpdateInfo {
  hasUpdate: boolean
  version?: VersionInfo
  policy?: {
    updateStrategy: 'force' | 'optional' | 'silent'
    rolloutPercentage: number
  }
}

interface CheckVersionProps {
  trigger?: React.ReactNode
  autoCheck?: boolean
}

export default function CheckVersion({ trigger, autoCheck = false }: CheckVersionProps) {
  const { t } = useTranslation('my')
  const { isOpen, onOpen, onClose } = useDisclosure()
  const [isChecking, setIsChecking] = useState(false)
  const [isUpdating, setIsUpdating] = useState(false)
  const [updateInfo, setUpdateInfo] = useState<UpdateInfo | null>(null)
  const [currentVersion, setCurrentVersion] = useState<string>('')
  const [downloadProgress, setDownloadProgress] = useState(0)
  const [error, setError] = useState<string>('')

  // 获取平台信息
  const getPlatform = () => {
    // 这里可以根据实际需要检测平台
    // 目前默认为 android
    return 'android'
  }

  // 获取当前应用版本
  useEffect(() => {
    const getAppInfo = async () => {
      try {
        const appInfo = await App.getInfo()
        setCurrentVersion(appInfo.version)
      } catch (error) {
        console.error('获取应用信息失败:', error)
        setCurrentVersion('1.0.0') // 默认版本
      }
    }
    getAppInfo()
  }, [])

  // 自动检查更新
  useEffect(() => {
    if (autoCheck) {
      checkForUpdates()
    }
  }, [autoCheck])

  // 组件挂载时自动检查一次更新
  useEffect(() => {
    if (currentVersion) {
      checkForUpdates()
    }
  }, [currentVersion])

  // 检查更新
  const checkForUpdates = async () => {
    if (isChecking) return

    setIsChecking(true)
    setError('')
    setUpdateInfo(null)

    try {
      const deviceInfo = await Device.getId()
      
      // 构建查询参数
      const queryParams = new URLSearchParams({
        platform: getPlatform(),
        currentVersion,
        versionCode: parseInt(currentVersion.split('.').join('')).toString(),
        channel: 'production',
        deviceId: deviceInfo.identifier
      })

      const response = await apiClient.get<{
        success: boolean
        data: {
          hasApkUpdate: boolean
          hasHotfixUpdate: boolean
          apkUpdate?: {
            version: VersionInfo
            policy?: {
              updateStrategy: 'force' | 'optional' | 'silent'
              rolloutPercentage: number
            }
          }
          hotfixUpdate?: {
            version: VersionInfo
            policy?: {
              updateStrategy: 'force' | 'optional' | 'silent'
              rolloutPercentage: number
            }
          }
        }
      }>(`/api/app-update/check?${queryParams.toString()}`)

      if (response.success) {
        const { hasApkUpdate, apkUpdate } = response.data
        
        if (hasApkUpdate && apkUpdate) {
          // 如果有更新，请求 /latest-apk/:platform 接口获取最新的APK信息
          try {
            const latestApkResponse = await apiClient.get<{
              success: boolean
              data: {
                id: string
                versionName: string
                versionCode: number
                fileUrl: string
                fileSize?: number
                fileHash?: string
                releaseNotes?: string
                minCompatibleVersion?: string
                platform: string
                channel: string
                updatePolicy?: {
                  updateStrategy: 'force' | 'optional' | 'silent'
                  rolloutPercentage: number
                  targetVersionMin?: string
                  targetVersionMax?: string
                }
                createdAt: string
                updatedAt: string
              }
            }>(`/api/app-update/latest-apk/${getPlatform()}`)

            if (latestApkResponse.success && latestApkResponse.data) {
              const latestApk = latestApkResponse.data
              
              setUpdateInfo({
                hasUpdate: true,
                version: {
                  versionName: latestApk.versionName,
                  versionCode: latestApk.versionCode,
                  fileUrl: latestApk.fileUrl,
                  fileSize: latestApk.fileSize,
                  releaseNotes: latestApk.releaseNotes
                },
                policy: latestApk.updatePolicy || apkUpdate.policy
              })
              
              // 如果是强制更新，自动打开更新弹窗
              if (latestApk.updatePolicy?.updateStrategy === 'force' || apkUpdate.policy?.updateStrategy === 'force') {
                onOpen()
              }
            } else {
              // 如果获取最新APK信息失败，使用原有的更新信息
              setUpdateInfo({
                hasUpdate: true,
                version: apkUpdate.version,
                policy: apkUpdate.policy
              })
              
              if (apkUpdate.policy?.updateStrategy === 'force') {
                onOpen()
              }
            }
          } catch (error) {
            console.error('获取最新APK信息失败:', error)
            // 使用原有的更新信息
            setUpdateInfo({
              hasUpdate: true,
              version: apkUpdate.version,
              policy: apkUpdate.policy
            })
            
            if (apkUpdate.policy?.updateStrategy === 'force') {
              onOpen()
            }
          }
        } else {
          setUpdateInfo({ hasUpdate: false })
        }
      } else {
        setError('检查更新失败')
      }
    } catch (error) {
      console.error('检查更新失败:', error)
      setError('网络连接失败，请稍后重试')
    } finally {
      setIsChecking(false)
    }
  }

  // 开始更新
  const startUpdate = async () => {
    if (isUpdating) return

    setIsUpdating(true)
    setDownloadProgress(0)
    setError('')

    try {
      // 首先请求 /latest-apk/:platform 接口获取最新的APK信息
      const latestApkResponse = await apiClient.get<{
        success: boolean
        data: {
          id: string
          versionName: string
          versionCode: number
          fileUrl: string
          fileSize?: number
          fileHash?: string
          releaseNotes?: string
          minCompatibleVersion?: string
          platform: string
          channel: string
          updatePolicy?: {
            updateStrategy: 'force' | 'optional' | 'silent'
            rolloutPercentage: number
            targetVersionMin?: string
            targetVersionMax?: string
          }
          createdAt: string
          updatedAt: string
        }
      }>(`/api/app-update/latest-apk/${getPlatform()}`)

      if (!latestApkResponse.success || !latestApkResponse.data) {
        throw new Error('获取最新APK信息失败')
      }

      const latestApk = latestApkResponse.data
      console.log('获取到最新APK信息:', latestApk)

      // 检查版本是否需要更新
      const currentVersionCode = parseInt(currentVersion.split('.').join(''))
      if (latestApk.versionCode <= currentVersionCode) {
        setError('当前已是最新版本')
        return
      }

      // 使用实际的APK下载器下载最新版本
      const result = await apkUpdater.downloadApk(
        latestApk.fileUrl,
        `update-${latestApk.versionName}.apk`,
        (progress: ApkDownloadProgress) => {
          setDownloadProgress(progress.percentage)
        }
      )

      if (result.success && result.filePath) {
        // 下载成功，尝试安装
        const installResult = await apkUpdater.installApk(result.filePath)
        
        if (installResult.success) {
          // 安装成功，关闭弹窗
          onClose()
        } else {
          setError(`安装失败: ${installResult.error}`)
        }
      } else {
        setError(`下载失败: ${result.error}`)
      }
    } catch (error) {
      console.error('更新失败:', error)
      setError('更新失败，请稍后重试')
    } finally {
      setIsUpdating(false)
    }
  }

  // 取消下载
  const cancelUpdate = () => {
    apkUpdater.cancelDownload()
    setIsUpdating(false)
    setDownloadProgress(0)
    setError('')
  }

  // 默认触发器
  const defaultTrigger = (
    <div
      className="flex items-center justify-between cursor-pointer hover:bg-white/5 transition-colors py-2 px-2 rounded-lg"
      onClick={onOpen}
      style={{
        WebkitTouchCallout: 'none',
        WebkitUserSelect: 'none',
        userSelect: 'none',
        touchAction: 'manipulation'
      }}
    >
      <div className="flex items-center space-x-4">
        <div className="w-6 h-6 flex items-center justify-center">
          <Icon icon="lucide:refresh-cw" className="w-5 h-5 text-white/80" />
        </div>
        <span className="text-white font-medium text-lg">{t('menu.check_update')}</span>
      </div>
      <div className="flex items-center gap-2">
        <span className="text-white/60 text-sm">{currentVersion}</span>
        {updateInfo?.hasUpdate && (
          <Chip size="sm" color="warning" variant="flat">
            {t('update.available')}
          </Chip>
        )}
        <Icon icon="lucide:chevron-right" className="w-5 h-5 text-white/60" />
      </div>
    </div>
  )

  return (
    <>
      {trigger ? <div onClick={onOpen}>{trigger}</div> : defaultTrigger}

      <Modal
        isOpen={isOpen}
        onClose={onClose}
        placement="bottom"
        backdrop="blur"
        scrollBehavior="inside"
        classNames={{
          base: 'mb-0 sm:mb-0 bg-transparent',
          wrapper: 'items-end sm:items-end z-[1000]',
          body: 'py-0 bg-transparent overflow-hidden',
          header: 'border-b border-divider',
          backdrop: 'z-[999]'
        }}
        style={{
          zIndex: 1000
        }}
      >
        <ModalContent className="bg-transparent rounded-t-3xl overflow-hidden">
          <ModalHeader className="flex flex-col gap-1 px-6 py-4 relative z-10 bg-background">
            <div className="flex items-center gap-3">
              <Icon icon="lucide:refresh-cw" className="w-5 h-5 text-primary" />
              <span className="text-lg font-semibold">{t('update.title')}</span>
            </div>
          </ModalHeader>
          <ModalBody className="px-0 pb-6 bg-background">
            {/* 左上角渐变装饰 */}
            <div className="absolute top-0 left-0 size-full pointer-events-none z-1 opacity-70">
              <img
                src="/images/decorate/decorate-9.svg"
                alt=""
                className="w-full h-full opacity-80"
              />
            </div>

            <div className="space-y-4 relative z-10 px-6">
              {/* 当前版本信息 */}
              <div className="flex items-center justify-between p-4 bg-default-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <Icon icon="lucide:smartphone" className="w-5 h-5 text-primary" />
                  <div>
                    <div className="font-medium">{t('update.current_version')}</div>
                    <div className="text-sm text-default-500">{currentVersion}</div>
                  </div>
                </div>
                {/* <Chip size="sm" color="success" variant="flat">
                  {t('update.latest')}
                </Chip> */}
              </div>

              {/* 检查更新按钮 */}
              <Button
                color="primary"
                variant="flat"
                className="w-full"
                onPress={checkForUpdates}
                isLoading={isChecking}
                startContent={<Icon icon="lucide:refresh-cw" className="w-4 h-4" />}
              >
                {isChecking ? t('update.checking') : t('update.check_now')}
              </Button>

              {/* 错误信息 */}
              {error && (
                <div className="p-3 bg-danger-50 border border-danger-200 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Icon icon="lucide:alert-circle" className="w-4 h-4 text-danger" />
                    <span className="text-sm text-danger">{error}</span>
                  </div>
                </div>
              )}

              {/* 更新信息 */}
              {updateInfo?.hasUpdate && updateInfo.version && (
                <div className="space-y-4">
                  <Divider />
                  
                  <div className="p-4 bg-warning-50 border border-warning-200 rounded-lg">
                    <div className="flex items-center gap-3 mb-3">
                      <Icon icon="lucide:download" className="w-5 h-5 text-warning" />
                      <div>
                        <div className="font-medium text-warning-800">
                          {t('update.new_version_available')}
                        </div>
                        <div className="text-sm text-warning-600">
                          v{updateInfo.version.versionName}
                        </div>
                      </div>
                    </div>

                    {/* 更新说明 */}
                    {updateInfo.version.releaseNotes && (
                      <div className="mb-3">
                        <div className="text-sm font-medium text-warning-800 mb-1">
                          {t('update.release_notes')}:
                        </div>
                        <div className="text-sm text-warning-600 bg-white/50 p-2 rounded">
                          {updateInfo.version.releaseNotes}
                        </div>
                      </div>
                    )}

                    {/* 文件大小 */}
                    {updateInfo.version.fileSize && (
                      <div className="text-sm text-warning-600">
                        {t('update.file_size')}: {(updateInfo.version.fileSize / 1024 / 1024).toFixed(1)} MB
                      </div>
                    )}

                    {/* 更新策略 */}
                    {updateInfo.policy && (
                      <div className="mt-3">
                        <Chip
                          size="sm"
                          color={updateInfo.policy.updateStrategy === 'force' ? 'danger' : 'warning'}
                          variant="flat"
                        >
                          {updateInfo.policy.updateStrategy === 'force' 
                            ? t('update.force_update') 
                            : t('update.optional_update')
                          }
                        </Chip>
                      </div>
                    )}
                  </div>

                  {/* 下载进度 */}
                  {isUpdating && (
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span>{t('update.downloading')}</span>
                        <span>{downloadProgress}%</span>
                      </div>
                      <Progress
                        value={downloadProgress}
                        color="primary"
                        className="w-full"
                      />
                      <Button
                        variant="light"
                        color="danger"
                        size="sm"
                        className="w-full"
                        onPress={cancelUpdate}
                      >
                        取消
                      </Button>
                    </div>
                  )}

                  {/* 更新按钮 */}
                  {!isUpdating && (
                    <Button
                      color="primary"
                      className="w-full"
                      onPress={startUpdate}
                      startContent={<Icon icon="lucide:download" className="w-4 h-4" />}
                    >
                      {updateInfo.policy?.updateStrategy === 'force' 
                        ? t('update.update_now') 
                        : t('update.update_optional')
                      }
                    </Button>
                  )}
                </div>
              )}

              {/* 无更新提示 */}
              {updateInfo && !updateInfo.hasUpdate && !isChecking && (
                <div className="p-4 bg-success-50 border border-success-200 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Icon icon="lucide:check-circle" className="w-5 h-5 text-success" />
                    <span className="text-sm text-success-800">{t('update.no_update')}</span>
                  </div>
                </div>
              )}
            </div>

            {/* 取消按钮 */}
            <div className="px-6 pt-4">
              <Button variant="light" className="w-full" onPress={onClose}>
                取消
              </Button>
            </div>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  )
}

