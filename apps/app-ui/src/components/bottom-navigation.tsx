import React from 'react'
import { useLocation } from 'react-router'
import { useTranslation } from 'react-i18next'
import { cn } from '@/lib/utils'
import { useDeviceSafeArea } from '@/hooks/use-mobile-viewport'
import { useSmartNavigation } from '@/lib/navigation'

interface BottomNavigationProps {
  className?: string
}

// 本地图标资源路径
const iconAssets = {
  // 创建按钮
  createButton: '/images/bottom-nav/create-button.svg',
  // 首页图标
  homeInactive: '/images/bottom-nav/home-inactive.svg',
  homeActive: '/images/bottom-nav/home-active.svg',
  // 控制图标
  controlInactive: '/images/bottom-nav/control-inactive.svg',
  controlActive: '/images/bottom-nav/control-active.svg',
  // 消息图标
  messageInactive: '/images/bottom-nav/message-inactive.svg',
  messageActive: '/images/bottom-nav/message-active.svg',
  // 我的图标
  profileInactive: '/images/bottom-nav/profile-inactive.svg',
  profileActive: '/images/bottom-nav/profile-active.svg'
}

export function BottomNavigation({ className }: BottomNavigationProps) {
  const { t } = useTranslation()
  const location = useLocation()
  const { smartNavigate } = useSmartNavigation()
  const pathname = location.pathname
  // 获取设备安全区域信息
  const safeArea = useDeviceSafeArea()

  const isActive = (path: string): boolean => {
    return pathname === path
  }

  // 处理导航点击，使用智能导航
  const handleTabClick = (path: string, e: React.MouseEvent<HTMLDivElement>) => {
    e.preventDefault() // 阻止默认行为
    if (pathname !== path) {
      // 使用智能导航，自动判断使用 replace 还是 push
      smartNavigate(path)
    }
  }

  const navItems = [
    {
      labelKey: 'navigation.home',
      href: '/discover',
      iconActive: iconAssets.homeActive,
      iconInactive: iconAssets.homeInactive
    },
    {
      labelKey: 'navigation.control',
      href: '/device-connection',
      iconActive: iconAssets.controlActive,
      iconInactive: iconAssets.controlInactive
    },
    {
      href: '/roles/custom',
      isCreateButton: true
    },
    {
      labelKey: 'navigation.script',
      href: '/interactive',
      iconActive: iconAssets.messageActive,
      iconInactive: iconAssets.messageInactive
    },
    {
      labelKey: 'navigation.profile',
      href: '/profile',
      iconActive: iconAssets.profileActive,
      iconInactive: iconAssets.profileInactive
    }
  ]

  // 计算底部安全区域高度，考虑最小值
  const bottomPadding = Math.max(safeArea.bottom, 0)
  console.log('BottomNavigation-安全区域底部:', bottomPadding)

  return (
    <div
      className={cn('fixed bottom-0 left-0 right-0 z-50', className)}
      style={{
        paddingBottom: `calc(${bottomPadding}px + 10px)`
      }}
    >
      {/* 黑色毛玻璃背景效果 */}
      <div
        className="absolute inset-0 backdrop-blur-lg border-t border-white/10"
        style={{
          backgroundColor: 'rgba(18, 21, 33, 0.8)', // #121521 with opacity
          backdropFilter: 'blur(20px)',
          WebkitBackdropFilter: 'blur(20px)'
        }}
      />

      {/* 导航项容器 */}
      <div className="relative flex h-16 items-center justify-around px-2">
        {navItems.map((item, index) => {
          const active = isActive(item.href)

          // 创建按钮特殊处理
          if (item.isCreateButton) {
            return (
              <div
                key={item.href}
                onClick={e => handleTabClick(item.href, e)}
                className="flex flex-col items-center justify-center cursor-pointer px-4"
              >
                <img
                  alt={t('navigation.create')}
                  className="block w-[50px] h-[34px]"
                  src={iconAssets.createButton}
                />
              </div>
            )
          }

          // 普通导航项
          return (
            <div
              key={item.href}
              onClick={e => handleTabClick(item.href, e)}
              className="flex flex-col items-center justify-center gap-0.5 cursor-pointer py-2 min-w-0 flex-1"
            >
              <div className="w-7 h-7 flex items-center justify-center">
                <img
                  alt={item.labelKey ? t(item.labelKey) : ''}
                  className="block w-full h-full"
                  src={active ? item.iconActive : item.iconInactive}
                />
              </div>
              {item.labelKey && (
                <span
                  className={cn(
                    'text-[11px] text-center text-nowrap font-normal leading-none',
                    active ? 'text-white' : 'text-[#999999]'
                  )}
                  style={{ fontFamily: "'PingFang SC', sans-serif" }}
                >
                  {t(item.labelKey)}
                </span>
              )}
            </div>
          )
        })}
      </div>
    </div>
  )
}
