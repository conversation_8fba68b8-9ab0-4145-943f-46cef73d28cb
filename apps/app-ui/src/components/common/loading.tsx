import React from 'react'
import { cn } from '@/lib/utils'

interface LoadingProps {
  /** 组件大小 */
  size?: 'sm' | 'md' | 'lg' | 'xl'
  /** 加载文字 */
  text?: string
  /** 是否显示文字 */
  showText?: boolean
  /** 自定义类名 */
  className?: string
  /** 自定义样式 */
  style?: React.CSSProperties
  /** 颜色主题 */
  variant?: 'primary' | 'secondary' | 'white'
}

/**
 * 脉冲光环 Loading 组件 - 极简高端的加载指示器
 *
 * 特点：
 * - 毛玻璃质感的光环扩散效果
 * - 使用品牌渐变色系
 * - 多种尺寸和样式变体
 * - 性能优化的纯 CSS 动画
 */
export default function Loading({
  size = 'md',
  text = '...',
  showText = false,
  className,
  style,
  variant = 'primary',
  ...props
}: LoadingProps) {
  // 尺寸配置
  const sizeConfig = {
    sm: {
      container: 'w-8 h-8',
      center: 'w-2 h-2',
      ring1: 'w-6 h-6',
      ring2: 'w-8 h-8',
      ring3: 'w-10 h-10',
      text: 'text-xs mt-2'
    },
    md: {
      container: 'w-12 h-12',
      center: 'w-3 h-3',
      ring1: 'w-8 h-8',
      ring2: 'w-12 h-12',
      ring3: 'w-16 h-16',
      text: 'text-sm mt-3'
    },
    lg: {
      container: 'w-16 h-16',
      center: 'w-4 h-4',
      ring1: 'w-10 h-10',
      ring2: 'w-16 h-16',
      ring3: 'w-20 h-20',
      text: 'text-base mt-4'
    },
    xl: {
      container: 'w-20 h-20',
      center: 'w-5 h-5',
      ring1: 'w-12 h-12',
      ring2: 'w-20 h-20',
      ring3: 'w-24 h-24',
      text: 'text-lg mt-4'
    }
  }

  // 颜色配置
  const colorConfig = {
    primary: {
      center: 'bg-gradient-to-br from-[#ff2d97] to-[#8b2fff]',
      ring: 'border-[#ff2d97]/40',
      text: 'text-[#ff2d97]'
    },
    secondary: {
      center: 'bg-gradient-to-br from-[#8b2fff] to-[#ff2d97]',
      ring: 'border-[#8b2fff]/40',
      text: 'text-[#8b2fff]'
    },
    white: {
      center: 'bg-white',
      ring: 'border-white/40',
      text: 'text-white'
    }
  }

  const config = sizeConfig[size]
  const colors = colorConfig[variant]

  return (
    <div
      className={cn('flex flex-col items-center justify-center', className)}
      style={style}
      {...props}
    >
      {/* Loading 动画容器 */}
      <div className={cn('relative flex items-center justify-center', config.container)}>
        {/* 中心圆点 */}
        <div
          className={cn('absolute rounded-full z-10', config.center, colors.center)}
          style={{
            boxShadow: `0 0 20px rgba(255, 45, 151, 0.5)`
          }}
        />

        {/* 脉冲光环 1 */}
        <div
          className={cn(
            'absolute rounded-full border-2 animate-pulse-ring-1',
            config.ring1,
            colors.ring
          )}
          style={{
            backdropFilter: 'blur(4px)',
            WebkitBackdropFilter: 'blur(4px)'
          }}
        />

        {/* 脉冲光环 2 */}
        <div
          className={cn(
            'absolute rounded-full border-2 animate-pulse-ring-2',
            config.ring2,
            colors.ring
          )}
          style={{
            backdropFilter: 'blur(6px)',
            WebkitBackdropFilter: 'blur(6px)',
            animationDelay: '0.4s'
          }}
        />

        {/* 脉冲光环 3 */}
        <div
          className={cn(
            'absolute rounded-full border animate-pulse-ring-3',
            config.ring3,
            colors.ring
          )}
          style={{
            backdropFilter: 'blur(8px)',
            WebkitBackdropFilter: 'blur(8px)',
            animationDelay: '0.8s'
          }}
        />
      </div>

      {/* 加载文字 */}
      {showText && (
        <p
          className={cn('font-medium tracking-wide opacity-80', config.text, colors.text)}
          style={{
            fontFamily: "'PingFang SC', sans-serif"
          }}
        >
          {text}
        </p>
      )}
    </div>
  )
}

// 预设尺寸组件
export const LoadingSM = (props: Omit<LoadingProps, 'size'>) => <Loading {...props} size="sm" />

export const LoadingMD = (props: Omit<LoadingProps, 'size'>) => <Loading {...props} size="md" />

export const LoadingLG = (props: Omit<LoadingProps, 'size'>) => <Loading {...props} size="lg" />

export const LoadingXL = (props: Omit<LoadingProps, 'size'>) => <Loading {...props} size="xl" />

// 带文字的预设组件
export const LoadingWithText = (props: Omit<LoadingProps, 'showText'>) => (
  <Loading {...props} showText />
)

// 全屏遮罩 Loading
export const LoadingOverlay = ({
  show,
  text = '',
  size = 'lg',
  variant = 'primary',
  className,
  ...props
}: LoadingProps & { show: boolean }) => {
  if (!show) return null

  return (
    <div
      className={cn(
        'fixed inset-0 z-[9999] flex items-center justify-center',
        'backdrop-blur-lg',
        className
      )}
      style={{
        backgroundColor: 'rgba(18, 21, 33, 0.8)', // 使用项目主题色
        backdropFilter: 'blur(20px)',
        WebkitBackdropFilter: 'blur(20px)'
      }}
      {...props}
    >
      <Loading size={size} text={text} showText variant={variant} />
    </div>
  )
}

// 内联 Loading（用于按钮等）
export const LoadingInline = ({ className, ...props }: Omit<LoadingProps, 'showText' | 'text'>) => (
  <Loading {...props} size="sm" showText={false} className={cn('inline-flex', className)} />
)
