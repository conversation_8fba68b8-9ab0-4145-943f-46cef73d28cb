import React from 'react'
import { motion } from 'framer-motion'
import { Icon } from '@iconify/react'
import { Button } from '@heroui/react'
import type { MusicItem } from '@/types/music'

interface TopInfoBarProps {
  music: MusicItem
  onBack: () => void
  onMusicDrawerOpen: () => void
}

/**
 * 顶部信息栏组件
 * 包含返回按钮、音乐信息和设置按钮
 */
export const TopInfoBar: React.FC<TopInfoBarProps> = ({ music, onBack, onMusicDrawerOpen }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: -50 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -50 }}
      onClick={e => e.stopPropagation()}
      className="absolute top-0 left-0 right-0 z-30 p-6 pt-12"
    >
      <div className="flex items-center justify-between">
        <Button
          isIconOnly
          variant="light"
          onPress={onBack}
          className="text-white hover:bg-white/10"
        >
          <Icon icon="solar:arrow-left-linear" width={24} />
        </Button>

        <div className="text-center">
          <h2 className="text-white text-lg font-semibold">{music.title}</h2>
          <p className="text-white/60 text-sm">{music.artist}</p>
        </div>

        <Button
          isIconOnly
          variant="light"
          onPress={onMusicDrawerOpen}
          className="text-white hover:bg-white/10"
        >
          <Icon icon="solar:playlist-minimalistic-2-linear" width={24} />
        </Button>
      </div>
    </motion.div>
  )
}
