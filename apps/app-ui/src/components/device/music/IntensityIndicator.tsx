import React from 'react'
import { motion } from 'framer-motion'
import { Slider } from '@heroui/react'

interface IntensityIndicatorProps {
  intensity: number
  isConnected: boolean
  onChange: (value: number | number[]) => void
  onDragStart: () => void
  onDragEnd: () => void
}

/**
 * 强度指示器组件 - 优化版本
 * 左侧竖直强度控制条
 */
export const IntensityIndicator: React.FC<IntensityIndicatorProps> = ({
  intensity,
  isConnected,
  onChange,
  onDragStart,
  onDragEnd
}) => {
  return (
    <div className="flex flex-col items-center gap-3" onClick={e => e.stopPropagation()}>
      {/* 强度数值 */}
      <motion.div
        animate={{
          scale: intensity > 6 ? [1, 1.1, 1] : 1,
          boxShadow:
            intensity > 6
              ? [
                  '0 0 0 rgba(255, 45, 151, 0)',
                  '0 0 20px rgba(255, 45, 151, 0.6)',
                  '0 0 0 rgba(255, 45, 151, 0)'
                ]
              : '0 4px 12px rgba(0, 0, 0, 0.3)'
        }}
        transition={{ duration: 0.5, repeat: intensity > 6 ? Infinity : 0 }}
        className="bg-black/70 backdrop-blur-sm rounded-full px-3 py-2 text-white text-lg font-bold shadow-lg border border-white/20 min-w-[50px] flex items-center justify-center"
      >
        {intensity}
      </motion.div>

      {/* 自定义竖直进度条 */}
      <div className="relative">
        {/* 背景轨道 */}
        <div className="w-5 h-60 bg-gradient-to-t from-gray-800 via-gray-700 to-gray-600 rounded-full border border-white/20 overflow-hidden shadow-inner relative">
          {/* 渐变填充 - 平滑过渡 */}
          <motion.div
            className="w-full bg-gradient-to-t from-[#00d4ff] via-[#892fff] to-[#ff2d97] rounded-full shadow-lg"
            animate={{
              height: `${(intensity / 9) * 100}%`
            }}
            transition={{
              duration: 0.3,
              ease: [0.4, 0, 0.2, 1] // 自定义缓动函数，更平滑
            }}
            style={{
              position: 'absolute',
              bottom: 0,
              left: 0,
              right: 0
            }}
          />

          {/* 高光效果 - 增强视觉层次 */}
          {intensity > 0 && (
            <motion.div
              className="absolute inset-x-0 bg-white/20 rounded-full"
              animate={{
                height: `${(intensity / 9) * 100}%`,
                opacity: intensity > 6 ? [0.3, 0.6, 0.3] : 0.3
              }}
              transition={{
                duration: 0.3,
                ease: [0.4, 0, 0.2, 1],
                opacity: { duration: 1, repeat: intensity > 6 ? Infinity : 0 }
              }}
              style={{
                bottom: 0,
                width: '2px',
                left: '1px'
              }}
            />
          )}

          {/* 发光效果 - 高强度时显示 */}
          {intensity > 7 && (
            <motion.div
              className="absolute inset-0 bg-gradient-to-t from-[#00d4ff]/30 via-[#892fff]/30 to-[#ff2d97]/30 rounded-full blur-sm"
              animate={{
                opacity: [0.4, 0.8, 0.4],
                scale: [1, 1.05, 1]
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                ease: 'easeInOut'
              }}
              style={{
                height: `${(intensity / 9) * 100}%`,
                bottom: 0
              }}
            />
          )}
        </div>

        {/* 可交互的滑块覆盖层 */}
        <div className="absolute inset-0">
          <Slider
            orientation="vertical"
            size="lg"
            step={1}
            minValue={0}
            maxValue={9}
            value={intensity}
            onChange={onChange}
            className="h-full opacity-0" // 隐藏默认样式，保留交互功能
            aria-label="设备强度控制"
            onMouseDown={onDragStart}
            onMouseUp={onDragEnd}
            onTouchStart={onDragStart}
            onTouchEnd={onDragEnd}
          />
        </div>
      </div>

      {/* 强度标签 */}
      <div className="text-white/60 text-xs font-medium">强度</div>

      {/* 连接状态指示灯 */}
      <motion.div
        className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'}`}
        animate={{
          scale: isConnected ? [1, 1.2, 1] : 1,
          opacity: isConnected ? [1, 0.6, 1] : [1, 0.4, 1]
        }}
        transition={{
          duration: isConnected ? 2 : 1,
          repeat: Infinity,
          ease: 'easeInOut'
        }}
      />
    </div>
  )
}
