import React, { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Icon } from '@iconify/react'
import type { MusicItem } from '@/types/music'
import type { StoredMusicItem } from '@/services/music-storage'
import { MusicStorageService } from '@/services/music-storage'
import { musicService } from '@/services/music'

interface MusicDrawerProps {
  isOpen: boolean
  onClose: () => void
  currentMusic: MusicItem | null
  onMusicSelect: (music: StoredMusicItem) => void
}

/**
 * 抽屉式音乐选择组件
 * 集成在线音乐和导入音乐的选择功能
 */
export const MusicDrawer: React.FC<MusicDrawerProps> = ({
  isOpen,
  onClose,
  currentMusic,
  onMusicSelect
}) => {
  const [activeTab, setActiveTab] = useState<'online' | 'import'>('online')
  const [onlineMusic, setOnlineMusic] = useState<StoredMusicItem[]>([])
  const [importedMusic, setImportedMusic] = useState<StoredMusicItem[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isImporting, setIsImporting] = useState(false)
  const [importProgress, setImportProgress] = useState(0)
  const [deletingMusicId, setDeletingMusicId] = useState<string | null>(null)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 加载音乐数据
  useEffect(() => {
    const loadMusicData = async () => {
      if (!isOpen) return

      setIsLoading(true)
      try {
        // 加载在线音乐
        const online = await musicService.getOnlineMusic()
        setOnlineMusic(online)

        // 加载导入音乐
        const imported = await musicService.getImportedMusic()
        setImportedMusic(imported)
      } catch (error) {
        console.error('加载音乐数据失败:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadMusicData()
  }, [isOpen])

  // 显示删除确认对话框
  const handleDeleteClick = (musicId: string, e: React.MouseEvent) => {
    e.stopPropagation() // 防止触发音乐选择
    setShowDeleteConfirm(musicId)
  }

  // 确认删除导入的音乐
  const handleConfirmDelete = async (musicId: string) => {
    if (deletingMusicId) return // 防止重复删除

    try {
      setDeletingMusicId(musicId)
      setShowDeleteConfirm(null)
      console.log('🗑️ 开始删除音乐:', musicId)

      await musicService.deleteImportedMusic(musicId)

      // 刷新导入音乐列表
      const updatedImportedMusic = await musicService.getImportedMusic()
      setImportedMusic(updatedImportedMusic)

      console.log('✅ 音乐删除成功')
    } catch (error) {
      console.error('❌ 删除音乐失败:', error)
      alert('删除音乐失败，请重试')
    } finally {
      setDeletingMusicId(null)
    }
  }

  // 取消删除
  const handleCancelDelete = () => {
    setShowDeleteConfirm(null)
  }

  // 处理音乐选择
  const handleMusicSelect = (music: StoredMusicItem) => {
    onMusicSelect(music)
    onClose()
  }

  // 监听音乐时长更新
  useEffect(() => {
    const handleMusicUpdate = (updatedMusic: StoredMusicItem) => {
      console.log('🔄 收到音乐更新通知:', updatedMusic.title, updatedMusic.duration)
      // 更新导入音乐列表中的对应项
      setImportedMusic(prev =>
        prev.map(music => (music.id === updatedMusic.id ? updatedMusic : music))
      )
    }

    MusicStorageService.addUpdateListener(handleMusicUpdate)

    return () => {
      MusicStorageService.removeUpdateListener(handleMusicUpdate)
    }
  }, [])

  // 格式化时长
  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  // 处理文件导入
  const handleFileImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // 检查文件类型
    if (!file.type.startsWith('audio/')) {
      alert('请选择音频文件')
      return
    }

    setIsImporting(true)
    setImportProgress(0)
    try {
      const importedMusic = await musicService.importMusic(file, progress => {
        setImportProgress(progress)
      })

      // 刷新导入音乐列表
      const updatedImportedMusic = await musicService.getImportedMusic()
      setImportedMusic(updatedImportedMusic)

      // 切换到导入标签页并选择新导入的音乐
      setActiveTab('import')
      onMusicSelect(importedMusic)
      onClose()
    } catch (error) {
      console.error('导入音乐失败:', error)
      alert('导入音乐失败，请重试')
    } finally {
      setIsImporting(false)
      setImportProgress(0)
      // 清空文件输入
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* 背景遮罩 */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
            onClick={onClose}
          />

          {/* 抽屉内容 */}
          <motion.div
            initial={{ y: '100%' }}
            animate={{ y: 0 }}
            exit={{ y: '100%' }}
            transition={{ type: 'spring', damping: 25, stiffness: 200 }}
            onClick={e => e.stopPropagation()}
            className="fixed bottom-0 left-0 right-0 bg-[#1a1f2e] rounded-t-3xl z-50 max-h-[70vh] overflow-hidden"
          >
            {/* 拖拽指示器 */}
            <div className="flex justify-center py-3">
              <div className="w-12 h-1 bg-white/20 rounded-full" />
            </div>

            {/* 标题栏 */}
            <div className="flex items-center justify-between px-6 pb-4">
              <h2 className="text-white text-xl font-semibold">选择音乐</h2>
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={onClose}
                className="w-8 h-8 flex items-center justify-center text-white/60 hover:text-white rounded-full hover:bg-white/10 transition-colors"
              >
                <Icon icon="solar:close-linear" width={20} />
              </motion.button>
            </div>

            {/* 标签切换 */}
            <div className="flex mx-6 mb-4 bg-white/5 rounded-full p-1">
              <motion.button
                whileTap={{ scale: 0.95 }}
                onClick={() => setActiveTab('online')}
                className={`flex-1 py-2 px-4 rounded-full text-sm font-medium transition-colors relative ${
                  activeTab === 'online' ? 'text-white' : 'text-white/60 hover:text-white/80'
                }`}
              >
                {activeTab === 'online' && (
                  <motion.div
                    layoutId="activeTab"
                    className="absolute inset-0 bg-[#892fff] rounded-full"
                    transition={{ type: 'spring', damping: 15, stiffness: 200 }}
                  />
                )}
                <span className="relative z-10 flex items-center gap-2">
                  <Icon icon="solar:music-note-3-bold" width={16} />
                  在线音乐 ({onlineMusic.length})
                </span>
              </motion.button>

              <motion.button
                whileTap={{ scale: 0.95 }}
                onClick={() => setActiveTab('import')}
                className={`flex-1 py-2 px-4 rounded-full text-sm font-medium transition-colors relative ${
                  activeTab === 'import' ? 'text-white' : 'text-white/60 hover:text-white/80'
                }`}
              >
                {activeTab === 'import' && (
                  <motion.div
                    layoutId="activeTab"
                    className="absolute inset-0 bg-[#892fff] rounded-full"
                    transition={{ type: 'spring', damping: 15, stiffness: 200 }}
                  />
                )}
                <span className="relative z-10 flex items-center gap-2">
                  <Icon icon="solar:folder-music-bold" width={16} />
                  我的音乐 ({importedMusic.length})
                </span>
              </motion.button>
            </div>

            {/* 音乐列表 */}
            <div className="flex-1 overflow-y-auto px-6 pb-6">
              {isLoading ? (
                <div className="flex items-center justify-center py-12">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                    className="w-8 h-8 border-2 border-[#892fff] border-t-transparent rounded-full"
                  />
                </div>
              ) : (
                <div className="space-y-2">
                  {activeTab === 'online' ? (
                    onlineMusic.length > 0 ? (
                      onlineMusic.map(music => (
                        <motion.div
                          key={music.id}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => handleMusicSelect(music)}
                          className={`p-4 rounded-xl cursor-pointer transition-colors ${
                            currentMusic?.id === music.id
                              ? 'bg-[#892fff]/20 border border-[#892fff]/30'
                              : 'bg-white/5 hover:bg-white/10 border border-transparent'
                          }`}
                        >
                          <div className="flex items-center gap-4">
                            <div className="w-12 h-12 bg-gradient-to-br from-[#892fff] to-[#ff2d97] rounded-xl flex items-center justify-center">
                              <Icon
                                icon="solar:music-note-3-bold"
                                width={24}
                                className="text-white"
                              />
                            </div>
                            <div className="flex-1 min-w-0">
                              <h3 className="text-white font-medium truncate">{music.title}</h3>
                              <p className="text-white/60 text-sm">
                                {formatDuration(music.duration || 0)}
                              </p>
                            </div>
                            {currentMusic?.id === music.id && (
                              <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                className="w-6 h-6 bg-[#892fff] rounded-full flex items-center justify-center"
                              >
                                <Icon icon="solar:play-bold" width={12} className="text-white" />
                              </motion.div>
                            )}
                          </div>
                        </motion.div>
                      ))
                    ) : (
                      <div className="text-center py-12 text-white/60">
                        <Icon
                          icon="solar:music-note-3-linear"
                          width={48}
                          className="mx-auto mb-4 opacity-40"
                        />
                        <p>暂无在线音乐</p>
                      </div>
                    )
                  ) : (
                    <>
                      {/* 导入按钮 */}
                      <motion.label
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        className="block p-4 rounded-xl bg-white/5 hover:bg-white/10 border-2 border-dashed border-white/20 hover:border-[#892fff]/50 cursor-pointer transition-colors"
                      >
                        <div className="flex items-center gap-4">
                          <div className="w-12 h-12 bg-[#892fff]/20 rounded-xl flex items-center justify-center">
                            <Icon
                              icon="solar:add-circle-bold"
                              width={24}
                              className="text-[#892fff]"
                            />
                          </div>
                          <div>
                            <h3 className="text-white font-medium">导入音乐文件</h3>
                            <p className="text-white/60 text-sm">支持 MP3, WAV, M4A 格式</p>
                          </div>
                        </div>
                        <input
                          ref={fileInputRef}
                          type="file"
                          accept="audio/*"
                          onChange={handleFileImport}
                          className="hidden"
                        />
                        {isImporting && (
                          <div className="absolute inset-0 bg-black/50 rounded-xl flex items-center justify-center">
                            <div className="text-white text-sm flex flex-col items-center gap-3">
                              <motion.div
                                animate={{ rotate: 360 }}
                                transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                                className="w-6 h-6 border-2 border-white/40 border-t-white rounded-full"
                              />
                              <div className="text-center">
                                <div className="text-white font-medium">导入中...</div>
                                <div className="text-white/60 text-xs mt-1">{importProgress}%</div>
                              </div>
                              {/* 进度条 */}
                              <div className="w-32 h-1 bg-white/20 rounded-full overflow-hidden">
                                <motion.div
                                  className="h-full bg-[#892fff]"
                                  initial={{ width: 0 }}
                                  animate={{ width: `${importProgress}%` }}
                                  transition={{ duration: 0.3 }}
                                />
                              </div>
                            </div>
                          </div>
                        )}
                      </motion.label>

                      {/* 导入的音乐列表 */}
                      {importedMusic.length > 0 ? (
                        importedMusic.map(music => (
                          <motion.div
                            key={music.id}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            onClick={() => handleMusicSelect(music)}
                            className={`p-4 rounded-xl cursor-pointer transition-colors ${
                              currentMusic?.id === music.id
                                ? 'bg-[#892fff]/20 border border-[#892fff]/30'
                                : 'bg-white/5 hover:bg-white/10 border border-transparent'
                            }`}
                          >
                            <div className="flex items-center gap-4">
                              <div className="w-12 h-12 bg-gradient-to-br from-[#00d4ff] to-[#892fff] rounded-xl flex items-center justify-center">
                                <Icon
                                  icon="solar:folder-music-bold"
                                  width={24}
                                  className="text-white"
                                />
                              </div>
                              <div className="flex-1 min-w-0">
                                <h3 className="text-white font-medium truncate">{music.title}</h3>
                                <p className="text-white/60 text-sm">
                                  {formatDuration(music.duration || 0)}
                                </p>
                              </div>
                              <div className="flex items-center gap-2">
                                {/* 删除按钮 */}
                                <motion.button
                                  whileHover={{ scale: 1.1 }}
                                  whileTap={{ scale: 0.9 }}
                                  onClick={e => handleDeleteClick(music.id, e)}
                                  disabled={deletingMusicId === music.id}
                                  className="w-8 h-8 rounded-full bg-red-500/20 hover:bg-red-500/30 flex items-center justify-center text-red-400 hover:text-red-300 transition-colors disabled:opacity-50"
                                  title="删除音乐"
                                >
                                  {deletingMusicId === music.id ? (
                                    <motion.div
                                      animate={{ rotate: 360 }}
                                      transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                                      className="w-4 h-4 border border-red-400 border-t-transparent rounded-full"
                                    />
                                  ) : (
                                    <Icon icon="solar:trash-bin-minimalistic-bold" width={16} />
                                  )}
                                </motion.button>

                                {/* 播放状态指示器 */}
                                {currentMusic?.id === music.id && (
                                  <motion.div
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1 }}
                                    className="w-6 h-6 bg-[#892fff] rounded-full flex items-center justify-center"
                                  >
                                    <Icon
                                      icon="solar:play-bold"
                                      width={12}
                                      className="text-white"
                                    />
                                  </motion.div>
                                )}
                              </div>
                            </div>
                          </motion.div>
                        ))
                      ) : (
                        <div className="text-center py-8 text-white/60">
                          <Icon
                            icon="solar:folder-music-linear"
                            width={48}
                            className="mx-auto mb-4 opacity-40"
                          />
                          <p>还没有导入音乐</p>
                          <p className="text-sm mt-2">点击上方按钮导入音乐文件</p>
                        </div>
                      )}
                    </>
                  )}
                </div>
              )}
            </div>
          </motion.div>

          {/* 删除确认对话框 */}
          {showDeleteConfirm && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/80 flex items-center justify-center z-50"
              onClick={handleCancelDelete}
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                onClick={e => e.stopPropagation()}
                className="bg-[#1a1d29] rounded-2xl p-6 mx-4 max-w-sm w-full"
              >
                <div className="text-center">
                  <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Icon
                      icon="solar:trash-bin-minimalistic-bold"
                      width={32}
                      className="text-red-400"
                    />
                  </div>
                  <h3 className="text-white text-lg font-semibold mb-2">删除音乐</h3>
                  <p className="text-white/60 text-sm mb-6">
                    确定要删除这个音乐吗？删除后无法恢复。
                  </p>
                  <div className="flex gap-3">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={handleCancelDelete}
                      className="flex-1 py-3 px-4 bg-white/10 hover:bg-white/20 text-white rounded-xl transition-colors"
                    >
                      取消
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => handleConfirmDelete(showDeleteConfirm)}
                      disabled={deletingMusicId === showDeleteConfirm}
                      className="flex-1 py-3 px-4 bg-red-500 hover:bg-red-600 text-white rounded-xl transition-colors disabled:opacity-50"
                    >
                      {deletingMusicId === showDeleteConfirm ? (
                        <div className="flex items-center justify-center gap-2">
                          <motion.div
                            animate={{ rotate: 360 }}
                            transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                            className="w-4 h-4 border border-white border-t-transparent rounded-full"
                          />
                          删除中...
                        </div>
                      ) : (
                        '确认删除'
                      )}
                    </motion.button>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </>
      )}
    </AnimatePresence>
  )
}
