import React from 'react'
import { motion } from 'framer-motion'
import type { MusicItem, PlayMode } from '@/types/music'
import { PlaybackControls } from './PlaybackControls'
import { MusicWaveformProgress } from './MusicWaveformProgress'

interface BottomControlPanelProps {
  music: MusicItem
  playbackState: any
  playMode: PlayMode
  audioElement: HTMLAudioElement | null
  onSeek: (value: number) => void
  onPlayPause: () => void
  onPrevious?: () => void
  onNext?: () => void

  onPlayModeToggle?: () => void
  formatTime: (seconds: number) => string
  onDragStart: () => void
  onDragEnd: () => void
}

/**
 * 底部控制面板组件 - 重构版
 * 包含音乐信息卡片和播放控制面板
 */
export const BottomControlPanel: React.FC<BottomControlPanelProps> = ({
  music,
  playbackState,
  playMode,
  audioElement,
  onSeek,
  onPlayPause,
  onPrevious,
  onNext,
  onPlayModeToggle
}) => {
  return (
    <div className="space-y-4">
      {/* 播放控制面板 - 使用精美卡片样式 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        whileHover={{ scale: 1.01 }}
        onClick={e => e.stopPropagation()}
        className="
          relative overflow-hidden rounded-2xl p-6
          bg-black/20 backdrop-blur-sm border border-white/20
          hover:border-[#892fff]/50 hover:bg-black/30 transition-all duration-300
        "
      >
        {/* 背景波形装饰 */}
        <div className="absolute inset-0 overflow-hidden">
          <motion.div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-[#892fff]/20 to-[#ff2d97]/20 rounded-full blur-xl" />
          <motion.div className="absolute -bottom-10 -left-10 w-24 h-24 bg-gradient-to-tr from-[#00d4ff]/20 to-[#892fff]/20 rounded-full blur-xl" />
        </div>

        {/* 内容区域 */}
        <div className="relative z-10 space-y-4">
          {/* 波形进度条 */}
          <div>
            <MusicWaveformProgress
              audioElement={audioElement}
              currentTime={playbackState.currentTime}
              duration={playbackState.duration || 0}
              onSeek={onSeek}
              height={50}
            />
          </div>

          {/* 播放控制按钮 */}
          <div>
            <PlaybackControls
              playbackState={playbackState}
              playMode={playMode}
              onPlayPause={onPlayPause}
              onPrevious={onPrevious}
              onNext={onNext}
              onPlayModeToggle={onPlayModeToggle}
            />
          </div>
        </div>

        {/* 悬浮效果 */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-[#892fff]/10 to-[#ff2d97]/10 opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none"
          whileHover={{ opacity: 1 }}
        />
      </motion.div>
    </div>
  )
}
