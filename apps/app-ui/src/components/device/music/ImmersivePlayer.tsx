import React, { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import type { MusicItem, AudioVisualizationData, PlayMode } from '@/types/music'
import { useMusicPlayer } from '@/stores/music-player'
import { useWaveformData } from '@/hooks/useWaveformData'
import { audioAnalysisService } from '@/services/audio-analysis'
import { MusicDrawer } from './MusicDrawer'
import { AudioVisualization } from './AudioVisualization'
import { IntensityIndicator } from './IntensityIndicator'
import { BottomControlPanel } from './BottomControlPanel'
import { TopInfoBar } from './TopInfoBar'

interface ImmersivePlayerProps {
  music: MusicItem
  onBack: () => void
}

/**
 * 沉浸式音乐播放器组件
 * 全屏可视化 + 悬浮控制面板
 */
export const ImmersivePlayer: React.FC<ImmersivePlayerProps> = ({ music, onBack }) => {
  const [showControls, setShowControls] = useState(true)
  const [isDragging, setIsDragging] = useState(false)
  const [showMusicDrawer, setShowMusicDrawer] = useState(false)
  const audioRef = useRef<HTMLAudioElement>(null)
  const hideControlsTimeoutRef = useRef<NodeJS.Timeout>()

  const {
    playbackState,
    deviceIntensity,
    isDeviceConnected,
    visualizationData,
    playlist,
    playMode,
    setDeviceIntensity,
    updatePlaybackState,
    updateVisualizationData,
    setAudioElement,
    setCurrentMusic,
    setPlayMode
  } = useMusicPlayer()

  // 蓝牙广播数据管理
  const bluetoothWaveform = useWaveformData({
    mode: 'music',
    maxPoints: 500
  })

  // 移除组件层重复的频率控制，统一由策略层管理

  // 自动隐藏控制面板 - 暂停时保持显示
  useEffect(() => {
    const resetHideTimer = () => {
      if (hideControlsTimeoutRef.current) {
        clearTimeout(hideControlsTimeoutRef.current)
      }

      setShowControls(true)

      // 如果正在播放且没有拖拽，才设置自动隐藏
      if (playbackState.isPlaying && !isDragging) {
        hideControlsTimeoutRef.current = setTimeout(() => {
          setShowControls(false)
        }, 5000)
      }
    }

    resetHideTimer()

    return () => {
      if (hideControlsTimeoutRef.current) {
        clearTimeout(hideControlsTimeoutRef.current)
      }
    }
  }, [isDragging, playbackState.isPlaying])

  // 播放状态改变时显示控制面板
  useEffect(() => {
    // 当暂停时，立即显示控制面板
    if (!playbackState.isPlaying) {
      setShowControls(true)
    }
  }, [playbackState.isPlaying])

  // 初始化音频元素
  useEffect(() => {
    if (!audioRef.current) return

    const audio = audioRef.current

    // 暂停当前播放并重置
    audio.pause()
    audio.currentTime = 0

    // 断开之前的音频分析器连接
    audioAnalysisService.disconnect()

    // 设置新的音频源
    audio.src = music.url

    // 确保音量设置正确
    audio.volume = 1.0
    audio.muted = false

    // 更新加载状态
    updatePlaybackState({
      isLoading: true,
      isPlaying: false,
      currentTime: 0,
      duration: 0
    })

    // 注意：不在这里连接音频分析器，而是在用户首次播放时连接
    // 这样可以避免在没有用户交互的情况下创建AudioContext导致的问题

    // 音频事件监听
    const handleLoadedMetadata = () => {
      updatePlaybackState({
        duration: audio.duration,
        isLoading: false
      })
    }

    const handleTimeUpdate = () => {
      updatePlaybackState({
        currentTime: audio.currentTime
      })
    }

    const handlePlay = () => {
      updatePlaybackState({ isPlaying: true })
    }

    const handlePause = () => {
      updatePlaybackState({ isPlaying: false })
    }

    const handleEnded = () => {
      updatePlaybackState({
        isPlaying: false,
        currentTime: 0
      })

      // 根据播放模式处理歌曲结束
      switch (playMode) {
        case 'single':
          // 单曲循环：重新播放当前歌曲
          if (audioRef.current) {
            audioRef.current.currentTime = 0
            audioRef.current.play().catch(console.error)
          }
          break
        case 'repeat':
          // 列表循环：播放下一首，到最后一首时回到第一首
          if (playlist.length > 1) {
            const currentIndex = playlist.findIndex(item => item.id === music.id)
            const nextIndex = (currentIndex + 1) % playlist.length
            const nextMusic = playlist[nextIndex]
            if (nextMusic) {
              setCurrentMusic(nextMusic)
            }
          } else {
            // 只有一首歌时也循环播放
            if (audioRef.current) {
              audioRef.current.currentTime = 0
              audioRef.current.play().catch(console.error)
            }
          }
          break
        case 'shuffle':
          // 随机播放：随机选择下一首
          if (playlist.length > 1) {
            let nextIndex
            do {
              nextIndex = Math.floor(Math.random() * playlist.length)
            } while (
              nextIndex === playlist.findIndex(item => item.id === music.id) &&
              playlist.length > 1
            )

            const nextMusic = playlist[nextIndex]
            if (nextMusic) {
              setCurrentMusic(nextMusic)
            }
          } else {
            // 只有一首歌时重新播放
            if (audioRef.current) {
              audioRef.current.currentTime = 0
              audioRef.current.play().catch(console.error)
            }
          }
          break
        default:
          // 默认行为：停止播放
          break
      }
    }

    const handleError = (e: Event) => {
      const errorTarget = e.target as HTMLAudioElement
      updatePlaybackState({
        isLoading: false,
        isPlaying: false,
        error: `音频加载失败: ${errorTarget.error?.message || '未知错误'}`
      })
    }

    // 添加事件监听
    audio.addEventListener('loadedmetadata', handleLoadedMetadata)
    audio.addEventListener('timeupdate', handleTimeUpdate)
    audio.addEventListener('play', handlePlay)
    audio.addEventListener('pause', handlePause)
    audio.addEventListener('ended', handleEnded)
    audio.addEventListener('error', handleError)

    // 设置到 store 中
    setAudioElement(audio)

    return () => {
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata)
      audio.removeEventListener('timeupdate', handleTimeUpdate)
      audio.removeEventListener('play', handlePlay)
      audio.removeEventListener('pause', handlePause)
      audio.removeEventListener('ended', handleEnded)
      audio.removeEventListener('error', handleError)

      // 清理蓝牙广播数据（立即发送，不受频率限制）
      bluetoothWaveform.addDataPoint(0)

      // 断开音频分析器
      audioAnalysisService.disconnect()
    }
  }, [
    music.url,
    music.id,
    updatePlaybackState,
    setAudioElement,
    playMode,
    playlist,
    setCurrentMusic,
    bluetoothWaveform
  ])

  // 移除不必要的平滑强度状态以提升性能
  // const [smoothIntensity, setSmoothIntensity] = useState(deviceIntensity)

  // 真实音频分析和强度同步 - 优化性能
  useEffect(() => {
    if (!playbackState.isPlaying) {
      // 播放停止时，立即发送强度为0的数据点
      bluetoothWaveform.addDataPoint(0)
      return
    }

    const interval = setInterval(() => {
      // 获取真实的音频分析数据
      const audioData = audioAnalysisService.getVisualizationData()
      const realIntensity = audioAnalysisService.calculateIntensity()

      if (audioData && audioAnalysisService.connected) {
        // 更新可视化数据
        updateVisualizationData(audioData)

        // 简化强度计算，直接使用真实强度
        const finalIntensity = Math.round(realIntensity)
        setDeviceIntensity(finalIntensity)

        // 直接发送蓝牙广播，频率控制交给策略层
        bluetoothWaveform.addDataPoint(finalIntensity)
      } else {
        // 如果音频分析不可用，使用基础强度
        const fallbackIntensity = Math.max(1, Math.round(Math.random() * 3 + 2))
        setDeviceIntensity(fallbackIntensity)
        bluetoothWaveform.addDataPoint(fallbackIntensity)
      }
    }, 200) // 从50ms优化到200ms，减少CPU负载

    return () => clearInterval(interval)
  }, [playbackState.isPlaying, updateVisualizationData, setDeviceIntensity, bluetoothWaveform])

  // 处理播放/暂停
  const handlePlayPause = async () => {
    if (!audioRef.current) return

    try {
      if (playbackState.isPlaying) {
        audioRef.current.pause()
      } else {
        // 先播放音频
        await audioRef.current.play()

        // 播放成功后连接音频分析器
        if (!audioAnalysisService.connected) {
          try {
            await audioAnalysisService.connectAudioElement(audioRef.current)
          } catch (analysisError) {
            console.warn('音频分析器连接失败，但不影响播放:', analysisError)
          }
        }
      }
    } catch (error) {
      console.error('播放/暂停失败:', error)
      updatePlaybackState({
        error: '音频播放失败，请检查音频文件'
      })
    }
  }

  // 处理进度拖拽
  const handleSeek = (time: number) => {
    if (audioRef.current) {
      audioRef.current.currentTime = time
    }
  }

  // 处理强度调整
  const handleIntensityChange = (value: number | number[]) => {
    const intensity = Array.isArray(value) ? value[0] : value
    setDeviceIntensity(intensity)
  }

  // 处理上一首
  const handlePrevious = () => {
    if (playlist.length === 0) return

    const currentIndex = playlist.findIndex(item => item.id === music.id)
    let prevIndex: number

    switch (playMode) {
      case 'shuffle':
        // 随机模式：随机选择一首（排除当前歌曲）
        if (playlist.length === 1) {
          prevIndex = 0
        } else {
          do {
            prevIndex = Math.floor(Math.random() * playlist.length)
          } while (prevIndex === currentIndex)
        }
        break
      case 'repeat':
      case 'single':
      default:
        // 列表循环和单曲循环：按顺序播放上一首
        prevIndex = currentIndex > 0 ? currentIndex - 1 : playlist.length - 1
        break
    }

    const prevMusic = playlist[prevIndex]
    if (prevMusic) {
      setCurrentMusic(prevMusic)
    }
  }

  // 处理下一首
  const handleNext = () => {
    if (playlist.length === 0) return

    const currentIndex = playlist.findIndex(item => item.id === music.id)
    let nextIndex: number

    switch (playMode) {
      case 'shuffle':
        // 随机模式：随机选择一首（排除当前歌曲）
        if (playlist.length === 1) {
          nextIndex = 0
        } else {
          do {
            nextIndex = Math.floor(Math.random() * playlist.length)
          } while (nextIndex === currentIndex)
        }
        break
      case 'repeat':
      case 'single':
      default:
        // 列表循环和单曲循环：按顺序播放下一首
        nextIndex = (currentIndex + 1) % playlist.length
        break
    }

    const nextMusic = playlist[nextIndex]
    if (nextMusic) {
      setCurrentMusic(nextMusic)
    }
  }

  // 处理播放模式切换
  const handlePlayModeToggle = () => {
    const modes: PlayMode[] = ['single', 'repeat', 'shuffle']
    const currentIndex = modes.indexOf(playMode)
    const nextIndex = (currentIndex + 1) % modes.length
    setPlayMode(modes[nextIndex])
  }

  // 格式化时间
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  // 处理屏幕点击
  const handleScreenTap = () => {
    setShowControls(!showControls)
  }

  // 组件卸载时清理蓝牙广播和音频分析器
  useEffect(() => {
    return () => {
      // 组件卸载时立即发送强度为0，停止设备（不受频率限制）
      bluetoothWaveform.addDataPoint(0)
      // 断开音频分析器
      audioAnalysisService.disconnect()
    }
  }, [bluetoothWaveform])

  // 蓝牙状态监控和重连
  useEffect(() => {
    const checkBluetoothStatus = async () => {
      if (!bluetoothWaveform.isBluetoothReady()) {
        console.warn('音乐模式：蓝牙未准备就绪，尝试重新初始化...')
        try {
          await bluetoothWaveform.reinitializeBluetooth()
          console.log('音乐模式：蓝牙重新初始化成功')
        } catch (error) {
          console.error('音乐模式：蓝牙重新初始化失败:', error)
        }
      }
    }

    // 延迟检查蓝牙状态，给蓝牙初始化时间
    const timer = setTimeout(checkBluetoothStatus, 1000)

    return () => clearTimeout(timer)
  }, [bluetoothWaveform])

  // 定期输出蓝牙广播性能统计（仅在开发环境）
  // useEffect(() => {
  //   if (process.env.NODE_ENV !== 'development') return

  //   const interval = setInterval(() => {
  //     const stats = bluetoothWaveform.getPerformanceStats()
  //     if (stats) {
  //       console.log('音乐模式蓝牙广播统计:', stats)
  //     }
  //   }, 10000) // 每10秒输出一次

  //   return () => clearInterval(interval)
  // }, [bluetoothWaveform])

  return (
    <div
      className="relative w-full h-full min-h-screen bg-[#121521] overflow-hidden"
      onClick={handleScreenTap}
    >
      {/* 隐藏的音频元素 */}
      <audio
        ref={audioRef}
        style={{ display: 'none' }}
        controls={false}
        preload="auto"
        playsInline
        muted={false}
        crossOrigin="anonymous"
      />

      {/* 全屏音频可视化背景 */}
      <div className="absolute inset-0 z-1">
        <AudioVisualization
          data={visualizationData}
          isPlaying={playbackState.isPlaying}
          intensity={deviceIntensity}
          smoothIntensity={deviceIntensity}
          onPlayPause={handlePlayPause}
          playbackState={playbackState}
        />
      </div>

      {/* 背景渐变遮罩 */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-black/30" />

      {/* 左侧强度指示器 - 始终显示 */}
      {/* <motion.div
        initial={{ opacity: 0, x: -50 }}
        animate={{ opacity: 1, x: 0 }}
        className="absolute left-4 top-1/2 transform -translate-y-1/2 z-20"
      >
        <IntensityIndicator
          intensity={deviceIntensity}
          isConnected={isDeviceConnected}
          onChange={handleIntensityChange}
          onDragStart={() => setIsDragging(true)}
          onDragEnd={() => setIsDragging(false)}
        />
      </motion.div> */}

      {/* 顶部信息栏 */}
      <AnimatePresence>
        {showControls && (
          <TopInfoBar
            music={music}
            onBack={onBack}
            onMusicDrawerOpen={() => setShowMusicDrawer(true)}
          />
        )}
      </AnimatePresence>

      {/* 底部控制面板 */}
      <AnimatePresence>
        {showControls && (
          <motion.div
            initial={{ opacity: 0, y: 100 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 100 }}
            className="absolute bottom-0 left-0 right-0 z-30 p-2 pb-8"
          >
            <BottomControlPanel
              music={music}
              playbackState={playbackState}
              playMode={playMode}
              audioElement={audioRef.current}
              onSeek={handleSeek}
              onPlayPause={handlePlayPause}
              onPrevious={playlist.length > 0 ? handlePrevious : undefined}
              onNext={playlist.length > 0 ? handleNext : undefined}
              onPlayModeToggle={handlePlayModeToggle}
              formatTime={formatTime}
              onDragStart={() => setIsDragging(true)}
              onDragEnd={() => setIsDragging(false)}
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* 提示文字 */}
      <AnimatePresence>
        {!showControls && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20"
          >
            <p className="text-white/40 text-xs text-center">点击屏幕显示控制面板</p>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 音乐选择抽屉 */}
      <MusicDrawer
        isOpen={showMusicDrawer}
        onClose={() => setShowMusicDrawer(false)}
        currentMusic={music}
        onMusicSelect={setCurrentMusic}
      />
    </div>
  )
}
