import React from 'react'
import { motion } from 'framer-motion'
import type { AudioVisualizationData, PlaybackState } from '@/types/music'
import { VinylPlayer } from './VinylPlayer'

interface AudioVisualizationProps {
  data: AudioVisualizationData | null
  isPlaying: boolean
  intensity: number
  smoothIntensity: number
  onPlayPause: () => void
  playbackState: PlaybackState
}

export const AudioVisualization: React.FC<AudioVisualizationProps> = ({
  data,
  isPlaying,
  intensity,
  smoothIntensity,
  onPlayPause,
  playbackState
}) => {
  return (
    <div className="relative w-full h-full flex items-center justify-center">
      {/* 背景音频可视化效果 */}
      {data && (
        <motion.div
          className="absolute inset-0 opacity-20"
          animate={{
            scale: 1 + smoothIntensity * 0.1,
            opacity: 0.1 + smoothIntensity * 0.3
          }}
          transition={{ duration: 0.3, ease: 'easeOut' }}
        >
          {/* 可以在这里添加更多的可视化效果 */}
          <div
            className="absolute inset-0 bg-gradient-radial from-purple-500/20 via-blue-500/10 to-transparent"
            style={{
              transform: `scale(${1 + intensity * 0.2})`,
              opacity: intensity * 0.5
            }}
          />
        </motion.div>
      )}

      {/* 中央黑胶唱片播放器 */}
      <div className="relative z-10">
        <VinylPlayer isPlaying={isPlaying} onPlayPause={onPlayPause} intensity={intensity} />
      </div>
    </div>
  )
}
