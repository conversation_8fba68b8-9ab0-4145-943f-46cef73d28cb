import React, { useRef, useEffect } from 'react'

interface FlowingWaveVisualizerProps {
  isPlaying: boolean
  intensity: number
  size?: number
}

interface WavePoint {
  x: number
  y: number
  z: number
  originalAngle: number
  waveOffset: number
}

/**
 * 3D流动波浪可视化组件
 * 使用Canvas实现类似图片中的流动波浪效果
 */
export const FlowingWaveVisualizer: React.FC<FlowingWaveVisualizerProps> = ({
  isPlaying,
  intensity,
  size = 320
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationRef = useRef<number>()
  const timeRef = useRef(0)
  const smoothIntensityRef = useRef(intensity)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    const centerX = size / 2
    const centerY = size / 2
    const baseRadius = 120 // 匹配240px唱片的半径

    // 创建波浪点数组
    const createWavePoints = (radius: number, count: number, waveIndex: number): WavePoint[] => {
      const points: WavePoint[] = []
      for (let i = 0; i < count; i++) {
        const angle = (i / count) * Math.PI * 2
        points.push({
          x: Math.cos(angle) * radius,
          y: Math.sin(angle) * radius,
          z: 0,
          originalAngle: angle,
          waveOffset: waveIndex * Math.PI * 0.3
        })
      }
      return points
    }

    // 创建渐变色
    const createGradient = (x1: number, y1: number, x2: number, y2: number) => {
      const gradient = ctx.createLinearGradient(x1, y1, x2, y2)
      gradient.addColorStop(0, 'rgba(0, 212, 255, 0.8)') // 蓝色
      gradient.addColorStop(0.3, 'rgba(255, 255, 255, 0.9)') // 白色
      gradient.addColorStop(0.7, 'rgba(255, 255, 255, 0.9)') // 白色
      gradient.addColorStop(1, 'rgba(255, 165, 0, 0.8)') // 橙色
      return gradient
    }

    // 更新波浪点位置 - 优化平滑度
    const updateWavePoints = (points: WavePoint[], time: number, smoothIntensity: number) => {
      return points.map((point, index) => {
        const angle = point.originalAngle
        const wavePhase = time * 0.008 + point.waveOffset + index * 0.05 // 降低频率

        // 平滑的强度响应
        const intensityFactor = Math.max(0.2, smoothIntensity / 9) // 确保最小波动

        // 主波浪：更温和的响应
        const primaryWave = Math.sin(wavePhase) * (8 + smoothIntensity * 3) * intensityFactor

        // 次波浪：减少幅度
        const secondaryWave =
          Math.sin(wavePhase * 1.2 + angle * 1.5) * (4 + smoothIntensity * 1.5) * intensityFactor

        // 细节波浪：更细腻的变化
        const detailWave =
          Math.cos(wavePhase * 1.8 + angle * 2.5) * (2 + smoothIntensity * 1) * intensityFactor

        // Z轴偏移：更温和的3D效果
        const zOffset =
          Math.sin(wavePhase * 0.5 + angle * 1.2) * (3 + smoothIntensity * 1.5) * intensityFactor

        const dynamicRadius = baseRadius + primaryWave + secondaryWave + detailWave

        return {
          ...point,
          x: Math.cos(angle) * dynamicRadius,
          y: Math.sin(angle) * dynamicRadius,
          z: zOffset
        }
      })
    }

    // 绘制波浪路径
    const drawWavePath = (points: WavePoint[], opacity: number) => {
      if (points.length < 3) return

      ctx.save()

      // 根据Z值和强度调整透明度和大小（模拟3D深度）
      const avgZ = points.reduce((sum, p) => sum + p.z, 0) / points.length
      const intensityBoost = 1 + (smoothIntensityRef.current / 9) * 0.3 // 减少强度增强效果
      const depthScale = 1 + avgZ * 0.005 * intensityBoost
      const depthOpacity = opacity * (0.7 + Math.abs(avgZ) * 0.01 * intensityBoost)

      ctx.translate(centerX, centerY)
      ctx.scale(depthScale, depthScale)

      // 创建超平滑的样条曲线路径
      ctx.beginPath()

      // 使用Cardinal样条曲线算法
      const tension = 0.3 // 张力系数，控制曲线的弯曲程度
      const numPoints = points.length

      // 扩展点数组以处理闭合曲线
      const extendedPoints = [
        points[numPoints - 1], // 前一个点
        ...points,
        points[0], // 后一个点
        points[1] // 后后一个点
      ]

      ctx.moveTo(points[0].x, points[0].y)

      for (let i = 1; i < numPoints; i++) {
        const p0 = extendedPoints[i - 1 + 1] // 前一个点
        const p1 = extendedPoints[i + 1] // 当前点
        const p2 = extendedPoints[i + 1 + 1] // 下一个点
        const p3 = extendedPoints[i + 2 + 1] // 下下个点

        // Cardinal样条曲线的控制点计算
        const cp1x = p1.x + (p2.x - p0.x) * tension
        const cp1y = p1.y + (p2.y - p0.y) * tension
        const cp2x = p2.x - (p3.x - p1.x) * tension
        const cp2y = p2.y - (p3.y - p1.y) * tension

        ctx.bezierCurveTo(cp1x, cp1y, cp2x, cp2y, p2.x, p2.y)
      }

      ctx.closePath()

      // 设置样式和绘制 - 线条宽度温和响应强度
      const gradient = createGradient(-baseRadius, -baseRadius, baseRadius, baseRadius)
      const lineWidth = 2 + (smoothIntensityRef.current / 9) * 2 // 减少线条变化幅度

      ctx.strokeStyle = gradient
      ctx.lineWidth = lineWidth
      ctx.globalAlpha = depthOpacity
      ctx.stroke()

      // 添加内部发光效果 - 温和的发光强度
      ctx.globalAlpha = depthOpacity * (0.15 + smoothIntensityRef.current * 0.02)
      ctx.lineWidth = lineWidth * 2
      ctx.stroke()

      ctx.restore()
    }

    // 动画循环 - 优化平滑度
    const animate = () => {
      timeRef.current += 0.5 // 降低时间增量，让动画更慢

      // 平滑强度过渡
      const targetIntensity = intensity
      const currentIntensity = smoothIntensityRef.current
      const smoothingFactor = 0.1 // 平滑系数，值越小越平滑
      smoothIntensityRef.current =
        currentIntensity + (targetIntensity - currentIntensity) * smoothingFactor

      // 清空画布
      ctx.clearRect(0, 0, size, size)

      if (!isPlaying) {
        // 暂停时显示静态效果
        const staticPoints = createWavePoints(baseRadius, 60, 0)
        drawWavePath(staticPoints, 0.3)
        animationRef.current = requestAnimationFrame(animate)
        return
      }

      const smoothIntensity = smoothIntensityRef.current
      const time = timeRef.current

      // 创建多层波浪 - 使用平滑强度
      const layers = [
        { count: 80, radiusOffset: 0, opacity: 0.6 + smoothIntensity * 0.02, speed: 1 },
        {
          count: 70,
          radiusOffset: -15,
          opacity: 0.4 + smoothIntensity * 0.015,
          speed: 1.05 + smoothIntensity * 0.01
        },
        {
          count: 90,
          radiusOffset: 10,
          opacity: 0.5 + smoothIntensity * 0.015,
          speed: 0.95 - smoothIntensity * 0.005
        },
        {
          count: 60,
          radiusOffset: -25,
          opacity: 0.25 + smoothIntensity * 0.01,
          speed: 1.15 + smoothIntensity * 0.015
        }
      ]

      layers.forEach((layer, index) => {
        const layerRadius = baseRadius + layer.radiusOffset
        const points = createWavePoints(layerRadius, layer.count, index)
        const updatedPoints = updateWavePoints(points, time * layer.speed, smoothIntensity)
        drawWavePath(updatedPoints, layer.opacity)
      })

      animationRef.current = requestAnimationFrame(animate)
    }

    // 开始动画
    animate()

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [isPlaying, intensity, size])

  return (
    <canvas
      ref={canvasRef}
      width={size}
      height={size}
      style={{
        width: size,
        height: size,
        filter: 'blur(0.5px)', // 轻微模糊增加柔和感
        display: 'block' // 确保没有额外的行内间距
      }}
    />
  )
}
