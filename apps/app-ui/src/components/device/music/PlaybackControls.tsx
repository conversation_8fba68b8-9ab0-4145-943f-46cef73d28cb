import React from 'react'
import { motion } from 'framer-motion'
import { Icon } from '@iconify/react'
import type { PlaybackState, PlayMode } from '@/types/music'

interface PlaybackControlsProps {
  playbackState: PlaybackState
  playMode?: PlayMode
  onPlayPause: () => void
  onPrevious?: () => void
  onNext?: () => void
  onPlayModeToggle?: () => void
}

// 播放模式辅助函数
const getPlayModeIcon = (mode: PlayMode) => {
  switch (mode) {
    case 'repeat':
      return 'solar:repeat-bold'
    case 'shuffle':
      return 'solar:shuffle-bold'
    default:
      return 'solar:repeat-one-bold'
  }
}

const getPlayModeLabel = (mode: PlayMode) => {
  switch (mode) {
    case 'repeat':
      return '列表循环'
    case 'shuffle':
      return '随机播放'
    default:
      return '单曲循环'
  }
}

/**
 * 播放控制组件
 * 包含播放/暂停、上一首、下一首、播放模式切换等控制
 */
export const PlaybackControls: React.FC<PlaybackControlsProps> = ({
  playbackState,
  playMode = 'single',
  onPlayPause,
  onPrevious,
  onNext,
  onPlayModeToggle
}) => {
  return (
    <div className="flex items-center justify-between">
      {/* 播放模式按钮 - 左边 */}
      <motion.button
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        onClick={e => {
          e.stopPropagation()
          onPlayModeToggle?.()
        }}
        disabled={!onPlayModeToggle}
        className={`w-10 h-10 flex items-center justify-center rounded-full transition-colors ${
          onPlayModeToggle
            ? playMode !== 'single'
              ? 'text-[#892fff] bg-[#892fff]/20'
              : 'text-white/60 hover:text-white hover:bg-white/10'
            : 'text-white/30 cursor-not-allowed'
        }`}
        aria-label={getPlayModeLabel(playMode)}
        title={getPlayModeLabel(playMode)}
      >
        <Icon icon={getPlayModeIcon(playMode)} width={20} />
      </motion.button>

      {/* 主要播放控制 - 中间 */}
      <div className="flex items-center gap-6">
        <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={e => {
            e.stopPropagation()
            onPrevious?.()
          }}
          disabled={!onPrevious}
          className={`w-12 h-12 flex items-center justify-center rounded-full transition-colors ${
            onPrevious
              ? 'text-white/60 hover:text-white hover:bg-white/10'
              : 'text-white/30 cursor-not-allowed'
          }`}
          aria-label="上一首"
        >
          <Icon icon="solar:skip-previous-bold" width={24} />
        </motion.button>

        <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={e => {
            e.stopPropagation()
            onPlayPause()
          }}
          className="w-16 h-16 flex items-center justify-center text-white bg-gradient-to-r from-[#892fff] to-[#ff2d97] rounded-full shadow-lg"
          aria-label={playbackState.isPlaying ? '暂停' : '播放'}
        >
          <Icon
            icon={playbackState.isPlaying ? 'solar:pause-bold' : 'solar:play-bold'}
            width={24}
          />
        </motion.button>

        <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={e => {
            e.stopPropagation()
            onNext?.()
          }}
          disabled={!onNext}
          className={`w-12 h-12 flex items-center justify-center rounded-full transition-colors ${
            onNext
              ? 'text-white/60 hover:text-white hover:bg-white/10'
              : 'text-white/30 cursor-not-allowed'
          }`}
          aria-label="下一首"
        >
          <Icon icon="solar:skip-next-bold" width={24} />
        </motion.button>
      </div>

      {/* 占位元素 - 右边，保持布局平衡 */}
      <div className="w-10 h-10"></div>
    </div>
  )
}
