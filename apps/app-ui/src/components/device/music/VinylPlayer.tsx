import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Icon } from '@iconify/react'
import { Button } from '@heroui/react'
import { FlowingWaveVisualizer } from './FlowingWaveVisualizer'

interface VinylPlayerProps {
  isPlaying: boolean
  intensity: number
  onPlayPause: () => void
  isLoading?: boolean
}

/**
 * 仿真黑胶唱片播放器组件
 * 包含SVG唱片、唱针交互、播放控制和发光效果
 */
export const VinylPlayer: React.FC<VinylPlayerProps> = ({
  isPlaying,
  intensity,
  onPlayPause,
  isLoading = false
}) => {
  return (
    <div className="relative flex flex-col items-center">
      {/* 唱片播放区域 */}
      <div className="relative">
        {/* 发光效果 - 播放时显示 */}
        <AnimatePresence>
          {isPlaying && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{
                opacity: [0.4, 0.8, 0.4],
                scale: [1, 1.1, 1],
                rotate: [0, 360]
              }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{
                opacity: { duration: 2, repeat: Infinity },
                scale: { duration: 3, repeat: Infinity },
                rotate: { duration: 20, repeat: Infinity, ease: 'linear' }
              }}
              className="absolute inset-0 rounded-full bg-gradient-to-r from-[#892fff]/30 via-[#ff2d97]/30 to-[#00d4ff]/30 blur-xl"
              style={{ width: '280px', height: '280px', left: '-20px', top: '-20px' }}
            />
          )}
        </AnimatePresence>

        {/* 黑胶唱片 */}
        <motion.div
          className="relative z-10"
          animate={{
            rotate: isPlaying ? [0, 360] : 0
          }}
          transition={{
            duration: isPlaying ? 8 : 0,
            repeat: isPlaying ? Infinity : 0,
            ease: 'linear'
          }}
          style={{ width: '240px', height: '240px' }}
        >
          <img src="/images/device/vinyl-record.svg" alt="黑胶唱片" className="w-full h-full" />
        </motion.div>

        {/* 唱针 */}
        <motion.div
          className="absolute -top-2 -right-2 z-20"
          animate={{
            rotate: isPlaying ? -15 : 20,
            x: isPlaying ? 8 : -15,
            y: isPlaying ? 5 : 15
          }}
          transition={{
            duration: 0.8,
            ease: 'easeInOut'
          }}
          style={{ transformOrigin: 'top right' }}
        >
          {/* 唱针臂 */}
          <div className="relative">
            <motion.div
              className="w-24 h-2 bg-gradient-to-r from-gray-400 to-gray-600 rounded-full shadow-lg"
              style={{ transformOrigin: 'right center' }}
            />

            {/* 唱针头 */}
            <motion.div
              className="absolute -left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-full shadow-md border border-yellow-300"
              animate={{
                boxShadow: isPlaying
                  ? '0 0 8px rgba(251, 191, 36, 0.6)'
                  : '0 2px 4px rgba(0, 0, 0, 0.3)'
              }}
            />

            {/* 唱针支架 */}
            <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-3 h-6 bg-gradient-to-b from-gray-500 to-gray-700 rounded-sm" />
          </div>
        </motion.div>

        {/* 中央播放按钮 - 初始状态时显示在唱片中心 */}
        <AnimatePresence>
          {!isPlaying && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="absolute inset-0 flex items-center justify-center z-30"
            >
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={e => {
                  e.stopPropagation()
                  onPlayPause()
                }}
                disabled={isLoading}
                className="w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-white/30 hover:bg-white/30 transition-all shadow-lg"
                aria-label="播放音乐"
              >
                {isLoading ? (
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                  >
                    <Icon icon="solar:refresh-bold" width={32} className="text-white" />
                  </motion.div>
                ) : (
                  <Icon icon="solar:play-bold" width={32} className="text-white" />
                )}
              </motion.button>
            </motion.div>
          )}
        </AnimatePresence>

        {/* 播放时的点击区域 - 点击唱片暂停 */}
        {isPlaying && (
          <motion.div
            className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 size-20 z-30 cursor-pointer"
            onClick={e => {
              e.stopPropagation()
              onPlayPause()
            }}
            aria-label="点击暂停音乐"
          />
        )}

        {/* 3D流动波浪可视化 */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
          <FlowingWaveVisualizer isPlaying={isPlaying} intensity={intensity} size={320} />
        </div>
      </div>
    </div>
  )
}
