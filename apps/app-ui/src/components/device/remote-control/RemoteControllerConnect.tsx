import React, { useState } from 'react'
import { Icon } from '@iconify/react'
import { motion } from 'framer-motion'
import { useRemoteControlStore } from '../../../stores/remote-control-store'

export const RemoteControllerConnect: React.FC = () => {
  const {
    controller,
    actions: { connectToDevice }
  } = useRemoteControlStore()
  const [controlCode, setControlCode] = useState('')

  // 处理连接设备
  const handleConnect = async () => {
    if (!controlCode.trim()) return

    const success = await connectToDevice(controlCode.trim())
    if (success) {
      setControlCode('')
    }
  }

  // 处理粘贴
  const handlePaste = async () => {
    try {
      const text = await navigator.clipboard.readText()
      const cleanText = text.trim().toUpperCase().slice(0, 6)
      setControlCode(cleanText)
    } catch (error) {
      console.error('粘贴失败:', error)
    }
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh] px-6">
      <div className="w-20 h-20 bg-[#1d2135] rounded-full flex items-center justify-center mb-6">
        <Icon icon="solar:gameboy-bold" width={40} className="text-[#00ff88]" />
      </div>
      <h2 className="text-white text-2xl font-semibold mb-2">连接远程设备</h2>
      <p className="text-[#7c85b6] text-base text-center mb-8">输入控制码来连接设备</p>

      <div className="w-full max-w-sm space-y-4">
        <div className="relative">
          <input
            type="text"
            value={controlCode}
            onChange={e => setControlCode(e.target.value.toUpperCase())}
            placeholder="输入6位控制码"
            className="w-full bg-black/20 backdrop-blur-sm text-white text-center py-4 px-6 pr-14 rounded-2xl text-lg font-mono tracking-wider placeholder-[#7c85b6] border border-white/20 focus:border-[#00ff88] focus:bg-black/30 outline-none transition-all"
            maxLength={6}
          />
          <motion.button
            onClick={handlePaste}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#7c85b6] hover:text-[#00ff88] p-2 rounded-lg hover:bg-white/10 transition-colors"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <Icon icon="solar:clipboard-bold" width={20} />
          </motion.button>
        </div>

        <motion.button
          onClick={handleConnect}
          disabled={controller.isConnecting || !controlCode.trim()}
          className="w-full bg-[#00ff88] hover:bg-[#00e67a] text-black font-semibold py-4 rounded-3xl transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          whileHover={!controller.isConnecting && controlCode.trim() ? { scale: 1.02 } : {}}
          whileTap={!controller.isConnecting && controlCode.trim() ? { scale: 0.98 } : {}}
        >
          {controller.isConnecting ? '连接中...' : '连接设备'}
        </motion.button>
      </div>

      <p className="text-[#7c85b6] text-sm mt-8 text-center">请向设备拥有者获取控制码</p>
    </div>
  )
}
