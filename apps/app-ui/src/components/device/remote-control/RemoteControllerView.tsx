import React from 'react'
import { useNavigate } from 'react-router'
import { Icon } from '@iconify/react'
import { motion } from 'framer-motion'
import { useRemoteControlStore } from '@/stores/remote-control-store'
import { RemoteDeviceControlProvider } from '../../../contexts/remote-device-control-provider'
import { EmojiToast } from '../EmojiToast'
import { RemoteControllerConnect } from './RemoteControllerConnect'
import { RemoteControllerControl } from './RemoteControllerControl'

export const RemoteControllerView: React.FC = () => {
  const navigate = useNavigate()
  const {
    controller,
    actions: { disconnectFromDevice }
  } = useRemoteControlStore()

  const handleDisconnect = async () => {
    await disconnectFromDevice()
  }

  return (
    <div className="min-h-screen bg-[#121521] relative overflow-hidden flex flex-col">
      {/* 背景装饰元素 */}
      <div className="absolute top-0 left-0 w-screen pointer-events-none">
        <img src="/images/device/bg.svg" className="w-full h-full" />
      </div>

      {/* 顶部标题栏 */}
      <div className="relative z-10 pt-12 pb-8">
        <div className="flex items-center justify-between px-6">
          <motion.button
            onClick={() => navigate(-1)}
            className="w-10 h-10 flex items-center justify-center text-white hover:bg-white/20 transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Icon icon="solar:arrow-left-linear" width={20} />
          </motion.button>
          <h1 className="text-white text-xl font-semibold absolute left-1/2 -translate-x-1/2">
            远程控制
          </h1>
          {controller.isConnected && (
            <div className="flex items-center gap-2">
              <motion.button
                onClick={handleDisconnect}
                className="px-3 py-1.5 bg-red-500/20 hover:bg-red-500/30 text-red-400 text-sm rounded-2xl transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                断开
              </motion.button>
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-green-400 text-sm">在线</span>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 内容区域 */}
      <div className="relative z-10 flex-1">
        {!controller.isConnected ? (
          <RemoteControllerConnect />
        ) : (
          <RemoteDeviceControlProvider>
            <RemoteControllerControl />
          </RemoteDeviceControlProvider>
        )}

        {/* 错误提示 */}
        {controller.error && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-4 p-4 bg-red-500/20 border border-red-500/30 rounded-2xl backdrop-blur-sm"
          >
            <div className="flex items-center gap-2">
              <Icon icon="solar:danger-triangle-bold" width={16} className="text-red-400" />
              <p className="text-red-400 text-sm">{controller.error}</p>
            </div>
          </motion.div>
        )}
      </div>

      {/* Emoji Toast */}
      <EmojiToast />
    </div>
  )
}
