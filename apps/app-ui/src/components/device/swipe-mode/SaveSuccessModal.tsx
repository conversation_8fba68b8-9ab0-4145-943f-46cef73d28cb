import React from 'react'
import { But<PERSON> } from '@heroui/react'
import { useSmartNavigation } from '@/lib/navigation'
import { Icon } from '@iconify/react'
import GradientModal from '@/components/common/gradient-modal'
import type { WaveformLibraryItem } from '@/types/swipe-mode'

interface SaveSuccessModalProps {
  /** 是否显示弹窗 */
  isOpen: boolean
  /** 关闭弹窗回调 */
  onClose: () => void
  /** 保存的波形数据 */
  waveform?: WaveformLibraryItem
  /** 继续划屏回调 */
  onContinue: () => void
}

export default function SaveSuccessModal({
  isOpen,
  onClose,
  waveform,
  onContinue
}: SaveSuccessModalProps) {
  const { smartNavigate, goBack } = useSmartNavigation()

  // 返回首页
  const handleGoHome = () => {
    onClose()
    goBack()
  }

  // 继续划屏
  const handleContinue = () => {
    onClose()
    onContinue()
  }

  return (
    <GradientModal isOpen={isOpen} onClose={onClose} title="保存成功" showFooter={false}>
      <div className="text-center space-y-6">
        {/* 成功图标 */}
        <div className="flex justify-center">
          <div className="w-16 h-16 rounded-full bg-primary-500/20 flex items-center justify-center">
            <Icon icon="mdi:check-circle" className="w-8 h-8 text-primary-500" />
          </div>
        </div>

        {/* 成功信息 */}
        <div className="space-y-2">
          <p className="text-lg font-medium text-white">轨迹已保存到波形库</p>
          {waveform && (
            <div className="space-y-1">
              <p className="text-sm text-white/70">波形名称：{waveform.name}</p>
              <p className="text-xs text-white/50">
                时长：{Math.round((waveform.duration / 1000) * 10) / 10}秒
              </p>
            </div>
          )}
        </div>

        {/* 操作按钮 */}
        <div className="flex gap-3">
          <Button
            variant="bordered"
            onPress={handleGoHome}
            className="flex-1 border-white/20 text-white hover:bg-white/10 transition-colors h-12 rounded-2xl"
          >
            返回首页
          </Button>
          <Button
            onPress={handleContinue}
            className="flex-1 text-white font-medium transition-all bg-button-primary h-12 rounded-2xl"
          >
            继续划屏
          </Button>
        </div>

        {/* 提示文字 */}
        <p className="text-xs text-white/40 mt-4">你可以在波形库中查看和管理你的所有波形</p>
      </div>
    </GradientModal>
  )
}
