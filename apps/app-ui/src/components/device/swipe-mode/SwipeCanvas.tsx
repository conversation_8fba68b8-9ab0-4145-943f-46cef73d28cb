import React, { useRef, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react'
import { motion } from 'framer-motion'
import type { TrackPoint } from '@/types/swipe-mode'
import { CanvasManager } from '@/utils/swipe-mode/canvas-manager'
import { TrackCalculator } from '@/utils/swipe-mode/track-calculator'

interface SwipeCanvasProps {
  className?: string
  onTrackStart?: () => void
  onTrackUpdate?: (points: TrackPoint[], currentIntensity: number) => void
  onTrackEnd?: (points: TrackPoint[]) => void
  completedTracks?: TrackPoint[][]
  disabled?: boolean
  hasStartedRecording?: boolean // 新增：是否已经开始录制
  isPlaybackMode?: boolean // 新增：是否为回放模式
  // 回放相关
  playbackPosition?: { x: number; y: number } | null // 当前回放位置
  playbackIntensity?: number // 当前回放强度
}

export interface SwipeCanvasRef {
  clear: () => void
  getImageData: () => string
}

export const SwipeCanvas = forwardRef<SwipeCanvasRef, SwipeCanvasProps>(
  (
    {
      className = '',
      onTrackStart,
      onTrackUpdate,
      onTrackEnd,
      completedTracks = [],
      disabled = false,
      hasStartedRecording = false,
      isPlaybackMode = false,
      playbackPosition = null,
      playbackIntensity = 0
    },
    ref
  ) => {
    const canvasRef = useRef<HTMLCanvasElement>(null)
    const canvasManagerRef = useRef<CanvasManager | null>(null)
    const currentTrackRef = useRef<TrackPoint[]>([])
    const isDrawingRef = useRef(false)
    const lastUpdateTimeRef = useRef(0)
    const touchTimeoutRef = useRef<NodeJS.Timeout | null>(null)
    const completedTracksRef = useRef<TrackPoint[][]>(completedTracks)

    // 暴露给父组件的方法
    useImperativeHandle(ref, () => ({
      clear: () => {
        if (canvasManagerRef.current) {
          canvasManagerRef.current.clear()
          currentTrackRef.current = []
        }
      },
      getImageData: () => {
        return canvasManagerRef.current?.toDataURL() || ''
      }
    }))

    // 初始化Canvas管理器
    useEffect(() => {
      if (!canvasRef.current) return

      // 延迟初始化，确保DOM完全渲染
      const initCanvas = () => {
        if (!canvasRef.current) return

        canvasManagerRef.current = new CanvasManager(canvasRef.current, {
          showGrid: false // 简洁模式，不显示网格
        })

        // 强制重新调整尺寸，确保完全填充父容器
        canvasManagerRef.current.forceResize()

        // 开始动画循环（只在绘制时运行，用于高亮效果）
        canvasManagerRef.current.startAnimation(() => {
          if (canvasManagerRef.current && isDrawingRef.current) {
            canvasManagerRef.current.drawMultipleTracks(
              completedTracksRef.current,
              currentTrackRef.current,
              isDrawingRef.current // 传递绘制状态
            )
          }
        })
      }

      // 使用setTimeout确保DOM渲染完成
      const timeoutId = setTimeout(initCanvas, 500)

      return () => {
        clearTimeout(timeoutId)
        if (touchTimeoutRef.current) {
          clearTimeout(touchTimeoutRef.current)
        }
        canvasManagerRef.current?.destroy()
      }
    }, [])

    // 更新已完成的轨迹
    useEffect(() => {
      completedTracksRef.current = completedTracks
      if (canvasManagerRef.current) {
        canvasManagerRef.current.drawMultipleTracks(
          completedTracks,
          currentTrackRef.current,
          isDrawingRef.current
        )
      }
    }, [completedTracks])

    // 处理尺寸变化
    useEffect(() => {
      if (!canvasRef.current) return

      const handleResize = () => {
        if (canvasManagerRef.current) {
          // 延迟执行resize，确保DOM更新完成
          setTimeout(() => {
            if (canvasManagerRef.current) {
              canvasManagerRef.current.forceResize()
              // forceResize已经包含了drawBackground，再次绘制轨迹
              setTimeout(() => {
                if (canvasManagerRef.current) {
                  canvasManagerRef.current.drawMultipleTracks(
                    completedTracksRef.current,
                    currentTrackRef.current,
                    isDrawingRef.current
                  )
                }
              }, 50)
            }
          }, 100)
        }
      }

      // 使用ResizeObserver监听Canvas容器尺寸变化
      const resizeObserver = new ResizeObserver(handleResize)
      resizeObserver.observe(canvasRef.current)

      // 备用方案：监听window事件
      window.addEventListener('resize', handleResize)
      window.addEventListener('orientationchange', handleResize)

      return () => {
        resizeObserver.disconnect()
        window.removeEventListener('resize', handleResize)
        window.removeEventListener('orientationchange', handleResize)
      }
    }, []) // 移除completedTracks依赖，避免不必要的重新创建

    // 处理回放绘制
    useEffect(() => {
      if (!isPlaybackMode || !canvasManagerRef.current) return

      if (playbackIntensity === 0) {
        // 强度为0表示手指抬起，结束当前轨迹
        if (currentTrackRef.current.length > 0) {
          completedTracksRef.current.push([...currentTrackRef.current])
          currentTrackRef.current = []
        }
      } else if (playbackPosition) {
        // 有位置且强度>0，添加到当前轨迹
        const trackPoint: TrackPoint = {
          x: playbackPosition.x,
          y: playbackPosition.y,
          pressure: playbackIntensity || 1,
          timestamp: Date.now()
        }

        currentTrackRef.current.push(trackPoint)
      }

      // 重新绘制画布
      canvasManagerRef.current.drawMultipleTracks(
        completedTracksRef.current,
        currentTrackRef.current,
        playbackIntensity > 0 // 只有在绘制时才显示当前轨迹高亮
      )
    }, [isPlaybackMode, playbackPosition, playbackIntensity])

    // 获取触摸点坐标
    const getTouchPoint = useCallback((touch: React.Touch): TrackPoint => {
      if (!canvasManagerRef.current) {
        throw new Error('Canvas manager not initialized')
      }

      const canvasPoint = canvasManagerRef.current.screenToCanvas(touch.clientX, touch.clientY)

      return {
        x: canvasPoint.x,
        y: canvasPoint.y,
        timestamp: Date.now(),
        pressure: (touch as any).force || 1 // 压感支持
      }
    }, [])

    // 开始绘制
    const handleTouchStart = useCallback(
      (e: React.TouchEvent) => {
        if (disabled) return

        // 移除 preventDefault，避免被动事件监听器警告
        e.stopPropagation()

        const touch = e.touches[0]
        if (!touch) return

        const point = getTouchPoint(touch)
        currentTrackRef.current = [point]
        isDrawingRef.current = true
        lastUpdateTimeRef.current = Date.now()

        onTrackStart?.()

        // 立即触发一次更新，使用最低强度（强度1）
        onTrackUpdate?.(currentTrackRef.current, 1)

        // 重绘Canvas
        if (canvasManagerRef.current) {
          canvasManagerRef.current.drawMultipleTracks(
            completedTracks,
            currentTrackRef.current,
            isDrawingRef.current
          )
        }

        // 设置超时，如果用户只是点击不移动，2秒后自动结束
        touchTimeoutRef.current = setTimeout(() => {
          if (isDrawingRef.current && currentTrackRef.current.length === 1) {
            // 模拟触摸结束
            isDrawingRef.current = false

            // 触发强度为0的更新，重置进度条
            onTrackUpdate?.(currentTrackRef.current, 0)

            // 触发结束回调
            if (currentTrackRef.current.length > 0) {
              onTrackEnd?.(currentTrackRef.current)
            }

            // 手动触发一次重绘，清除高亮点
            if (canvasManagerRef.current) {
              canvasManagerRef.current.drawMultipleTracks(
                completedTracks,
                currentTrackRef.current,
                false
              )
            }

            currentTrackRef.current = []
          }
        }, 2000) // 2秒超时
      },
      [disabled, getTouchPoint, onTrackStart, onTrackUpdate, onTrackEnd, completedTracks]
    )

    // 绘制过程中
    const handleTouchMove = useCallback(
      (e: React.TouchEvent) => {
        if (disabled || !isDrawingRef.current) return

        e.stopPropagation()

        // 清除超时，因为用户开始移动了
        if (touchTimeoutRef.current) {
          clearTimeout(touchTimeoutRef.current)
          touchTimeoutRef.current = null
        }

        const touch = e.touches[0]
        if (!touch) return

        const now = Date.now()
        // 限制更新频率，避免过多的点
        if (now - lastUpdateTimeRef.current < 16) return // ~60fps

        const point = getTouchPoint(touch)
        currentTrackRef.current.push(point)
        lastUpdateTimeRef.current = now

        // 计算当前强度
        const currentIntensity =
          currentTrackRef.current.length > 1
            ? TrackCalculator.calculateIntensity(
                TrackCalculator.calculateSpeed(
                  currentTrackRef.current,
                  currentTrackRef.current.length - 1
                )
              )
            : 1

        onTrackUpdate?.(currentTrackRef.current, currentIntensity)

        // 重绘Canvas
        if (canvasManagerRef.current) {
          canvasManagerRef.current.drawMultipleTracks(
            completedTracks,
            currentTrackRef.current,
            isDrawingRef.current
          )
        }
      },
      [disabled, getTouchPoint, onTrackUpdate, completedTracks]
    )

    // 结束绘制
    const handleTouchEnd = useCallback(
      (e: React.TouchEvent) => {
        if (disabled || !isDrawingRef.current) return

        e.stopPropagation()

        // 清除超时
        if (touchTimeoutRef.current) {
          clearTimeout(touchTimeoutRef.current)
          touchTimeoutRef.current = null
        }

        isDrawingRef.current = false

        // 触发强度为0的更新，重置进度条
        onTrackUpdate?.(currentTrackRef.current, 0)

        if (currentTrackRef.current.length > 1) {
          // 平滑轨迹
          const smoothedTrack = TrackCalculator.smoothTrack(currentTrackRef.current)
          onTrackEnd?.(smoothedTrack)
        } else if (currentTrackRef.current.length === 1) {
          // 单点触摸也要触发结束回调
          onTrackEnd?.(currentTrackRef.current)
        }

        // 手动触发一次重绘，清除高亮点
        if (canvasManagerRef.current) {
          canvasManagerRef.current.drawMultipleTracks(
            completedTracks,
            currentTrackRef.current,
            false // 绘制状态为false，不显示高亮
          )
        }

        // 延迟清除轨迹显示（保持之前的行为）
        setTimeout(() => {
          if (canvasManagerRef.current) {
            canvasManagerRef.current.clear() // clear方法已经包含了drawBackground
          }
        }, 1500) // 1.5秒后自动清除轨迹

        currentTrackRef.current = []
      },
      [disabled, onTrackUpdate, onTrackEnd, completedTracks]
    )

    // 处理鼠标事件（开发时使用）
    const handleMouseDown = useCallback(
      (e: React.MouseEvent) => {
        if (disabled) return

        e.preventDefault()

        const point: TrackPoint = {
          x: e.nativeEvent.offsetX,
          y: e.nativeEvent.offsetY,
          timestamp: Date.now(),
          pressure: 1
        }

        currentTrackRef.current = [point]
        isDrawingRef.current = true

        onTrackStart?.()

        // 立即触发一次更新，使用最低强度（强度1）
        onTrackUpdate?.(currentTrackRef.current, 1)

        // 重绘Canvas
        if (canvasManagerRef.current) {
          canvasManagerRef.current.drawMultipleTracks(
            completedTracks,
            currentTrackRef.current,
            isDrawingRef.current
          )
        }

        // 设置超时，如果用户只是点击不移动，2秒后自动结束
        touchTimeoutRef.current = setTimeout(() => {
          if (isDrawingRef.current && currentTrackRef.current.length === 1) {
            // 模拟鼠标结束
            isDrawingRef.current = false

            // 触发强度为0的更新，重置进度条
            onTrackUpdate?.(currentTrackRef.current, 0)

            // 触发结束回调
            if (currentTrackRef.current.length > 0) {
              onTrackEnd?.(currentTrackRef.current)
            }

            // 手动触发一次重绘，清除高亮点
            if (canvasManagerRef.current) {
              canvasManagerRef.current.drawMultipleTracks(
                completedTracks,
                currentTrackRef.current,
                false
              )
            }

            currentTrackRef.current = []
          }
        }, 2000) // 2秒超时
      },
      [disabled, onTrackStart, onTrackUpdate, onTrackEnd, completedTracks]
    )

    const handleMouseMove = useCallback(
      (e: React.MouseEvent) => {
        if (disabled || !isDrawingRef.current) return

        e.preventDefault()

        // 清除超时，因为用户开始移动了
        if (touchTimeoutRef.current) {
          clearTimeout(touchTimeoutRef.current)
          touchTimeoutRef.current = null
        }

        const point: TrackPoint = {
          x: e.nativeEvent.offsetX,
          y: e.nativeEvent.offsetY,
          timestamp: Date.now(),
          pressure: 1
        }

        currentTrackRef.current.push(point)

        const currentIntensity =
          currentTrackRef.current.length > 1
            ? TrackCalculator.calculateIntensity(
                TrackCalculator.calculateSpeed(
                  currentTrackRef.current,
                  currentTrackRef.current.length - 1
                )
              )
            : 1

        onTrackUpdate?.(currentTrackRef.current, currentIntensity)

        if (canvasManagerRef.current) {
          canvasManagerRef.current.drawMultipleTracks(
            completedTracks,
            currentTrackRef.current,
            isDrawingRef.current
          )
        }
      },
      [disabled, onTrackUpdate, completedTracks]
    )

    const handleMouseUp = useCallback(
      (e: React.MouseEvent) => {
        if (disabled || !isDrawingRef.current) return

        e.preventDefault()

        // 清除超时
        if (touchTimeoutRef.current) {
          clearTimeout(touchTimeoutRef.current)
          touchTimeoutRef.current = null
        }

        isDrawingRef.current = false

        // 触发强度为0的更新，重置进度条
        onTrackUpdate?.(currentTrackRef.current, 0)

        if (currentTrackRef.current.length > 1) {
          const smoothedTrack = TrackCalculator.smoothTrack(currentTrackRef.current)
          onTrackEnd?.(smoothedTrack)
        } else if (currentTrackRef.current.length === 1) {
          // 单点触摸也要触发结束回调
          onTrackEnd?.(currentTrackRef.current)
        }

        // 手动触发一次重绘，清除高亮点
        if (canvasManagerRef.current) {
          canvasManagerRef.current.drawMultipleTracks(
            completedTracks,
            currentTrackRef.current,
            false // 绘制状态为false，不显示高亮
          )
        }

        // 延迟清除轨迹显示（保持之前的行为）
        setTimeout(() => {
          if (canvasManagerRef.current) {
            canvasManagerRef.current.clear() // clear方法已经包含了drawBackground
          }
        }, 1500) // 1.5秒后自动清除轨迹

        currentTrackRef.current = []
      },
      [disabled, onTrackUpdate, onTrackEnd, completedTracks]
    )

    return (
      <motion.div
        className={`relative overflow-hidden w-screen h-[75vh] touch-none select-none ${className}`}
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
      >
        {/* 触控提示 */}
        {!disabled && !hasStartedRecording && !isPlaybackMode && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 flex items-center justify-center pointer-events-none z-10"
          >
            <div className="text-center">
              <motion.div
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4"
              >
                <svg
                  width="32"
                  height="32"
                  viewBox="0 0 24 24"
                  fill="none"
                  className="text-white/60"
                >
                  <path
                    d="M12 2C13.1 2 14 2.9 14 4V12L15.5 10.5C15.9 10.1 16.4 10 16.9 10.1C17.4 10.2 17.8 10.6 17.9 11.1C18 11.6 17.9 12.1 17.5 12.5L12.5 17.5C12.1 17.9 11.5 17.9 11.1 17.5L6.1 12.5C5.7 12.1 5.6 11.6 5.7 11.1C5.8 10.6 6.2 10.2 6.7 10.1C7.2 10 7.7 10.1 8.1 10.5L10 12V4C10 2.9 10.9 2 12 2Z"
                    fill="currentColor"
                  />
                </svg>
              </motion.div>
              <p className="text-white/60 text-sm font-medium">用手指在屏幕上滑动</p>
              <p className="text-white/40 text-xs mt-1">绘制你的专属轨迹</p>
            </div>
          </motion.div>
        )}

        {/* 禁用状态遮罩 */}
        {disabled && (
          <div className="absolute inset-0 bg-black/50 flex items-center justify-center z-20">
            <div className="text-center">
              <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-2">
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  className="text-white/60"
                >
                  <path
                    d="M12 2C17.5 2 22 6.5 22 12S17.5 22 12 22 2 17.5 2 12 6.5 2 12 2ZM12 4C7.6 4 4 7.6 4 12S7.6 20 12 20 20 16.4 20 12 16.4 4 12 4ZM12 6C13.1 6 14 6.9 14 8V12C14 13.1 13.1 14 12 14S10 13.1 10 12V8C10 6.9 10.9 6 12 6ZM12 16C13.1 16 14 16.9 14 18S13.1 20 12 20 10 19.1 10 18 10.9 16 12 16Z"
                    fill="currentColor"
                  />
                </svg>
              </div>
              <p className="text-white/60 text-sm">绘制已暂停</p>
            </div>
          </div>
        )}

        {/* Canvas 画布 */}
        <canvas
          ref={canvasRef}
          className="w-screen h-[75vh] touch-none cursor-crosshair absolute inset-0 select-none"
          style={{ touchAction: 'none' }}
          onTouchStart={isPlaybackMode ? undefined : handleTouchStart}
          onTouchMove={isPlaybackMode ? undefined : handleTouchMove}
          onTouchEnd={isPlaybackMode ? undefined : handleTouchEnd}
          onTouchCancel={handleTouchEnd}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
        />
      </motion.div>
    )
  }
)

SwipeCanvas.displayName = 'SwipeCanvas'
