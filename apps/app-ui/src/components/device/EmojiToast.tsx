import React, { useEffect, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useRemoteControlStore } from '../../stores/remote-control-store'

interface EmojiToastProps {
  className?: string
}

export const EmojiToast: React.FC<EmojiToastProps> = ({ className = '' }) => {
  const { lastReaction } = useRemoteControlStore()
  const [showToast, setShowToast] = useState(false)

  useEffect(() => {
    if (lastReaction) {
      setShowToast(true)

      // 3秒后自动隐藏
      const timer = setTimeout(() => {
        setShowToast(false)
      }, 3000)

      return () => clearTimeout(timer)
    }
  }, [lastReaction])

  return (
    <AnimatePresence>
      {showToast && lastReaction && (
        <motion.div
          initial={{ opacity: 0, scale: 0.5, y: -50 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.5, y: -50 }}
          className={`fixed top-20 left-1/2 transform -translate-x-1/2 z-50 ${className}`}
        >
          <div className="bg-black/80 backdrop-blur-sm rounded-2xl px-6 py-4 flex items-center gap-3 shadow-2xl border border-white/10">
            <motion.span
              className="text-4xl"
              animate={{
                scale: [1, 1.2, 1],
                rotate: [0, -10, 10, 0]
              }}
              transition={{
                duration: 0.5,
                ease: 'easeInOut'
              }}
            >
              {lastReaction.emoji}
            </motion.span>
            <div>
              <p className="text-white font-semibold">收到回应</p>
              <p className="text-white/60 text-sm">
                {lastReaction.from === 'host' ? '来自设备拥有者' : '来自控制者'}
              </p>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

export default EmojiToast
