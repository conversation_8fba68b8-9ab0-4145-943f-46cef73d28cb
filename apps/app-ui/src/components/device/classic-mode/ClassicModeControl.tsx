import React, { useState } from 'react'
import { Icon } from '@iconify/react'
import { motion } from 'framer-motion'
import { useDeviceControl } from '../../../contexts/device-control-context'
import { modes } from '@/utils/deviceModes'

interface ClassicModeControlProps {
  onBack?: () => void
  showBackButton?: boolean
  className?: string
}

export const ClassicModeControl: React.FC<ClassicModeControlProps> = ({
  onBack,
  showBackButton = true,
  className = ''
}) => {
  const { sendClassicCommand, isRemoteMode } = useDeviceControl()
  const [selectedClassicMode, setSelectedClassicMode] = useState<number | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  // 处理经典模式选择
  const handleClassicModeSelect = async (modeId: number) => {
    if (isLoading) return

    try {
      setIsLoading(true)

      if (selectedClassicMode === modeId) {
        setSelectedClassicMode(null)
      } else {
        setSelectedClassicMode(modeId)

        // 发送控制指令（本地广播或远程Socket）
        await sendClassicCommand(modeId)

        console.log(`${isRemoteMode ? '🌐 [远程]' : '📡 [本地]'} 经典模式指令已发送:`, modeId)
      }
    } catch (error) {
      console.error('发送经典模式指令失败:', error)
      // 发送失败时取消选中状态
      setSelectedClassicMode(null)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 50 }}
      transition={{ duration: 0.4, delay: 0.1 }}
      className={`relative z-10 ${className || 'bg-[#1d2135] rounded-t-[32px] flex-1 h-full'}`}
    >
      <div className={`${className === 'bg-transparent' ? 'px-5 pt-2 pb-8' : 'px-5 pt-10 h-full'}`}>
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            {showBackButton && onBack && (
              <motion.button
                onClick={onBack}
                className="w-10 h-10 flex items-center justify-center text-white bg-white/10 rounded-2xl hover:bg-white/20 transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Icon icon="solar:arrow-left-linear" width={20} />
              </motion.button>
            )}
            <h3 className="text-white text-lg font-semibold">经典模式</h3>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 rounded-full bg-[#ff2d97]"></div>
            <span className="text-[#7c85b6] text-xs">
              {selectedClassicMode ? `模式 ${selectedClassicMode}` : '请选择模式'}
            </span>
          </div>
        </div>

        {/* 经典模式网格 */}
        <div className="grid grid-cols-3 gap-3 mb-6">
          {modes.map((mode, index) => (
            <motion.div
              key={mode.id}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3, delay: 0.2 + index * 0.05 }}
              whileHover={{ scale: isLoading ? 1 : 1.05 }}
              whileTap={{ scale: isLoading ? 1 : 0.95 }}
              onClick={() => handleClassicModeSelect(mode.id)}
              className={`
                relative overflow-hidden rounded-2xl h-28 cursor-pointer transition-all duration-300
                bg-black/20 backdrop-blur-sm border
                ${
                  selectedClassicMode === mode.id
                    ? 'border-[#ff2d97] bg-[#ff2d97]/10'
                    : 'border-white/20 hover:border-white/40 hover:bg-black/30'
                }
                ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}
              `}
            >
              {/* 背景装饰 */}
              <div className="absolute inset-0 overflow-hidden">
                <div className="absolute -top-4 -right-4 w-12 h-12 bg-gradient-to-br from-[#ff2d97]/20 to-transparent rounded-full blur-lg" />
              </div>

              {/* 内容区域 */}
              <div className="relative z-10 h-full flex flex-col items-center justify-center">
                <motion.div className="w-10 h-10 rounded-lg mb-2 flex items-center justify-center">
                  <img
                    src={`/images/device/modes/${mode.id}.svg`}
                    alt={mode.name}
                    className="w-10 h-10"
                  />
                </motion.div>
                <span
                  className={`text-sm font-medium transition-colors ${
                    selectedClassicMode === mode.id ? 'text-white' : 'text-white/70'
                  }`}
                >
                  {mode.name}
                </span>
              </div>

              {/* 选中效果 */}
              {selectedClassicMode === mode.id && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="absolute inset-0 bg-gradient-to-br from-[#ff2d97]/10 to-transparent pointer-events-none"
                />
              )}

              {/* 悬浮效果 */}
              <motion.div
                className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none"
                whileHover={{ opacity: 1 }}
              />

              {/* 加载指示器 */}
              {isLoading && selectedClassicMode === mode.id && (
                <div className="absolute inset-0 flex items-center justify-center bg-black/30">
                  <div className="w-6 h-6 border-2 border-[#ff2d97]/30 border-t-[#ff2d97] rounded-full animate-spin" />
                </div>
              )}
            </motion.div>
          ))}
        </div>
      </div>
    </motion.div>
  )
}
