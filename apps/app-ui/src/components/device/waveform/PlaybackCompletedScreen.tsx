import React from 'react'
import { Icon } from '@iconify/react'
import { motion } from 'framer-motion'
import type { WaveformRecording } from '@/types/recording'

interface PlaybackCompletedScreenProps {
  recording: WaveformRecording
  onRestart: () => void
  onBack: () => void
}

export default function PlaybackCompletedScreen({
  recording,
  onRestart,
  onBack
}: PlaybackCompletedScreenProps) {
  return (
    <div className="absolute inset-0 z-20 bg-black/40 backdrop-blur-xl border border-white/20 flex items-center justify-center">
      <div className="text-center space-y-6 px-6">
        {/* 完成图标 */}
        <motion.div
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.6, type: 'spring', stiffness: 200 }}
          className="flex justify-center"
        >
          <div className="w-16 h-16 rounded-full bg-gradient-to-br from-emerald-400 via-green-500 to-teal-600 flex items-center justify-center shadow-lg shadow-emerald-400/40 backdrop-blur-sm border border-white/20">
            <Icon icon="solar:check-circle-bold" width={28} className="text-white drop-shadow-lg" />
          </div>
        </motion.div>

        {/* 录制信息 */}
        <motion.div
          className="space-y-2"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.5 }}
        >
          <h2 className="text-xl font-bold text-white drop-shadow-lg">播放完成</h2>
          <p className="text-white/70 text-sm font-medium">「{recording.name}」播放完毕</p>
        </motion.div>

        {/* 操作按钮 */}
        <div className="flex items-center justify-center gap-3">
          <motion.button
            onClick={onRestart}
            className="flex items-center gap-2 px-5 py-2.5 bg-gradient-to-r from-[#ff2d97] to-[#892fff] hover:from-[#ff2d97]/90 hover:to-[#892fff]/90 text-white rounded-full transition-all duration-300 shadow-md shadow-[#ff2d97]/30 backdrop-blur-sm border border-white/20 font-medium text-sm"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.4 }}
          >
            <Icon icon="solar:restart-bold" width={16} />
            <span>重新播放</span>
          </motion.button>

          <motion.button
            onClick={onBack}
            className="flex items-center gap-2 px-5 py-2.5 bg-white/15 hover:bg-white/25 text-white rounded-full transition-all duration-300 backdrop-blur-sm border border-white/30 font-medium text-sm"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7, duration: 0.4 }}
          >
            <Icon icon="solar:arrow-left-linear" width={16} />
            <span>返回</span>
          </motion.button>
        </div>
      </div>
    </div>
  )
}
