import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Icon } from '@iconify/react'
import type { WaveformLibraryItem } from '@/types/swipe-mode'
import { WaveformPreview } from './WaveformPreview'
import DeleteWaveformModal from './DeleteWaveformModal'

export interface WaveformCardProps {
  waveform: WaveformLibraryItem
  onPlay: (waveform: WaveformLibraryItem) => void
  onLike?: (waveformId: string) => Promise<void>
  onFavorite?: (waveformId: string) => Promise<void>
  onEdit?: (waveform: WaveformLibraryItem) => void
  onDelete?: (waveformId: string) => Promise<void>
  className?: string
  showOwnerActions?: boolean
}

/**
 * 波形卡片组件
 * 用于在波形库中展示单个波形项目
 */
export const WaveformCard: React.FC<WaveformCardProps> = ({
  waveform,
  onPlay,
  onLike,
  onFavorite,
  onEdit,
  onDelete,
  className = '',
  showOwnerActions = false
}) => {
  const [isLiking, setIsLiking] = useState(false)
  const [isFavoriting, setIsFavoriting] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const [editName, setEditName] = useState(waveform.name || '')
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)

  // 处理点赞
  const handleLike = async (e: React.MouseEvent) => {
    e.stopPropagation()
    if (isLiking || !onLike) return

    setIsLiking(true)
    try {
      await onLike(waveform.id)
    } finally {
      setIsLiking(false)
    }
  }

  // 处理收藏
  const handleFavorite = async (e: React.MouseEvent) => {
    e.stopPropagation()
    if (isFavoriting || !onFavorite) return

    setIsFavoriting(true)
    try {
      await onFavorite(waveform.id)
    } finally {
      setIsFavoriting(false)
    }
  }

  // 处理播放
  const handlePlay = () => {
    onPlay(waveform)
  }

  // 处理编辑
  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation()
    setIsEditing(true)
  }

  // 保存编辑
  const handleSaveEdit = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (editName.trim() && editName !== waveform.name) {
      onEdit?.({ ...waveform, name: editName.trim() })
    }
    setIsEditing(false)
  }

  // 取消编辑
  const handleCancelEdit = (e: React.MouseEvent) => {
    e.stopPropagation()
    setEditName(waveform.name || '')
    setIsEditing(false)
  }

  // 处理输入
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEditName(e.target.value)
  }

  // 处理回车保存
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSaveEdit(e as any)
    } else if (e.key === 'Escape') {
      handleCancelEdit(e as any)
    }
  }

  // 处理删除 - 显示确认弹窗
  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation()
    setShowDeleteModal(true)
  }

  // 确认删除
  const handleConfirmDelete = async () => {
    if (!onDelete) return

    setIsDeleting(true)
    try {
      await onDelete(waveform.id)
      setShowDeleteModal(false)
    } catch (error) {
      console.error('删除失败:', error)
    } finally {
      setIsDeleting(false)
    }
  }

  // 格式化时长
  const formatDuration = (duration: number) => {
    const seconds = Math.floor(duration / 1000)
    return `${seconds.toFixed(1)}s`
  }

  return (
    <motion.div
      whileTap={{ scale: 0.98 }}
      onClick={handlePlay}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      style={{
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
      }}
      className="
          relative overflow-hidden rounded-2xl
          bg-black/20 backdrop-blur-sm border border-white/20
          hover:border-[#892fff]/50 hover:bg-black/30 transition-all duration-300
        "
    >
      {/* 背景波形装饰 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <motion.div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-[#892fff]/20 to-[#ff2d97]/20 rounded-full blur-xl" />
        <motion.div className="absolute -bottom-10 -left-10 w-24 h-24 bg-gradient-to-tr from-[#00d4ff]/20 to-[#892fff]/20 rounded-full blur-xl" />
      </div>

      <div className="p-4 pb-0 space-y-3">
        {/* 作者信息 */}
        <div className="flex items-center gap-2">
          <div className="w-5 h-5 bg-gradient-to-br from-[#ff2d97] to-[#892fff] rounded-full flex items-center justify-center">
            {waveform.authorAvatar ? (
              <img
                src={waveform.authorAvatar}
                alt={waveform.authorName}
                className="w-full h-full rounded-full object-cover"
              />
            ) : (
              <Icon icon="solar:user-bold" width={11} className="text-white" />
            )}
          </div>
          <span className="text-white/70 text-xs">{waveform.authorName}</span>
          {waveform.isOwner && !showOwnerActions && (
            <div className="px-2 py-0.5 bg-[#ff2d97]/20 rounded-lg">
              <span className="text-[#ff2d97] text-xs font-medium">我的</span>
            </div>
          )}
        </div>

        {/* 标题和作者 */}
        {/* 标题编辑 */}
        <div>
          {isEditing ? (
            <div className="flex items-center gap-2 mb-2">
              <input
                type="text"
                value={editName}
                onChange={handleNameChange}
                onKeyDown={handleKeyPress}
                onClick={e => e.stopPropagation()}
                className="flex-1 bg-white/10 border border-white/20 rounded-xl px-3 py-2 text-white text-lg font-semibold focus:outline-none focus:ring-2 focus:ring-[#ff2d97]/50"
                autoFocus
                maxLength={50}
              />
              <motion.button
                onClick={handleSaveEdit}
                className="w-8 h-8 rounded-xl bg-[#ff2d97]/20 text-[#ff2d97] flex items-center justify-center shrink-0"
                whileTap={{ scale: 0.9 }}
              >
                <Icon icon="material-symbols:check-small" width={16} />
              </motion.button>
              <motion.button
                onClick={handleCancelEdit}
                className="w-8 h-8 rounded-xl bg-white/10 text-white/60 flex items-center justify-center shrink-0"
                whileTap={{ scale: 0.9 }}
              >
                <Icon icon="material-symbols:close-small" width={16} />
              </motion.button>
            </div>
          ) : (
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-white font-semibold text-md line-clamp-1 flex-1">
                {waveform.name || '未命名波形'}
              </h3>
            </div>
          )}
        </div>
      </div>

      {/* 波形预览区域 */}
      <div className="relative h-22 p-2 pt-0">
        <WaveformPreview
          waveform={waveform}
          width={325}
          height={70}
          showAnimation={true}
          className="w-full h-full rounded-2xl border border-white/10"
        />

        {/* 时长标签 */}
        <div className="absolute top-2 right-4">
          <div className="px-3 py-1.5 rounded-xl text-xs font-medium text-white bg-black/40 backdrop-blur-sm border border-white/10">
            {formatDuration(waveform.duration)}
          </div>
        </div>
      </div>

      {/* 信息区域 */}
      <div className="p-4 pt-2">
        {/* 统计信息和操作按钮 */}
        <div className="flex items-center justify-between">
          {/* 核心统计信息 - 纯展示 */}
          <div className="flex items-center gap-4 flex-1">
            <div className="flex items-center gap-2">
              <Icon
                icon={waveform.isLiked ? 'solar:heart-bold' : 'solar:heart-linear'}
                width={18}
                className="text-[#ff2d97]"
              />
              <span className="text-white/80 text-sm font-medium">{waveform.likeCount}</span>
            </div>
            <div className="flex items-center gap-2">
              <Icon
                icon={waveform.isFavorited ? 'solar:bookmark-bold' : 'solar:bookmark-linear'}
                width={16}
                className="text-[#892fff]"
              />
              <span className="text-white/70 text-sm">{waveform.favoriteCount}</span>
            </div>
            <div className="flex items-center gap-2">
              <Icon icon="solar:play-circle-linear" width={16} className="text-white/60" />
              <span className="text-white/60 text-sm">{waveform.playCount || 0}</span>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center gap-2 ml-4">
            {/* 点赞按钮 */}
            {onLike && (
              <motion.button
                onClick={async e => {
                  e.preventDefault()
                  e.stopPropagation()

                  if (isLiking || !onLike) return

                  setIsLiking(true)
                  try {
                    await onLike(waveform.id)
                  } finally {
                    setIsLiking(false)
                  }
                }}
                disabled={isLiking}
                className={`w-8 h-8 rounded-xl flex items-center justify-center transition-all duration-200 ${
                  waveform.isLiked
                    ? 'bg-[#ff2d97]/20 text-[#ff2d97] border border-[#ff2d97]/40'
                    : 'text-white/70 active:bg-white/15 border border-white/20'
                }`}
                whileTap={{ scale: 0.95 }}
              >
                <motion.div
                  animate={waveform.isLiked ? { scale: [1, 1.2, 1] } : {}}
                  transition={{ duration: 0.3 }}
                >
                  <Icon
                    icon={waveform.isLiked ? 'solar:heart-bold' : 'solar:heart-linear'}
                    width={15}
                  />
                </motion.div>
              </motion.button>
            )}

            {/* 收藏按钮 - 只在有onFavorite回调时显示 */}
            {onFavorite && !waveform.isOwner && (
              <motion.button
                onClick={async e => {
                  e.preventDefault()
                  e.stopPropagation()

                  if (isFavoriting || !onFavorite) return

                  setIsFavoriting(true)
                  try {
                    await onFavorite(waveform.id)
                  } finally {
                    setIsFavoriting(false)
                  }
                }}
                disabled={isFavoriting}
                className={`w-8 h-8 rounded-xl flex items-center justify-center transition-all duration-200 ${
                  waveform.isFavorited
                    ? 'bg-[#892fff]/20 text-[#892fff] border border-[#892fff]/40'
                    : ' text-white/70 active:bg-white/15 border border-white/20'
                }`}
                whileTap={{ scale: 0.95 }}
              >
                <motion.div
                  animate={waveform.isFavorited ? { scale: [1, 1.2, 1] } : {}}
                  transition={{ duration: 0.3 }}
                >
                  <Icon
                    icon={waveform.isFavorited ? 'solar:bookmark-bold' : 'solar:bookmark-linear'}
                    width={15}
                  />
                </motion.div>
              </motion.button>
            )}

            {showOwnerActions && waveform.isOwner && onEdit && (
              <motion.button
                onClick={e => {
                  e.stopPropagation()
                  handleEdit(e)
                }}
                className="w-8 h-8 rounded-xl  text-white/60 flex items-center justify-center border border-white/20"
                whileTap={{ scale: 0.9 }}
              >
                <Icon icon="solar:pen-linear" width={16} />
              </motion.button>
            )}

            {/* 删除按钮 - 只在拥有者模式下显示 */}
            {showOwnerActions && waveform.isOwner && onDelete && (
              <motion.button
                onClick={e => {
                  e.stopPropagation()
                  handleDelete(e)
                }}
                className="w-8 h-8 rounded-xl  text-white/60 active:bg-red-500/20 active:text-red-400 flex items-center justify-center transition-all duration-200 border border-white/20"
                whileTap={{ scale: 0.95 }}
              >
                <Icon icon="solar:trash-bin-minimalistic-bold" width={15} />
              </motion.button>
            )}
          </div>
        </div>

        {/* 标签 */}
        {waveform.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-3">
            {waveform.tags.slice(0, 3).map((tag, index) => (
              <div key={index} className="px-2 py-1 bg-white/10 rounded-full text-white/70 text-xs">
                #{tag}
              </div>
            ))}
            {waveform.tags.length > 3 && (
              <div className="px-2 py-1 bg-white/10 rounded-full text-white/70 text-xs">
                +{waveform.tags.length - 3}
              </div>
            )}
          </div>
        )}
      </div>
      {/* 删除确认弹窗 */}
      <DeleteWaveformModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        waveform={waveform}
        onConfirm={handleConfirmDelete}
        isDeleting={isDeleting}
      />
    </motion.div>
  )
}

/**
 * 波形卡片骨架屏组件
 */
export const WaveformCardSkeleton: React.FC<{ className?: string }> = ({ className = '' }) => {
  return (
    <div
      className={`relative overflow-hidden rounded-2xl bg-black/20 backdrop-blur-sm border border-white/20 ${className}`}
    >
      {/* 背景装饰 */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-[#892fff]/10 to-[#ff2d97]/10 rounded-full blur-xl" />
        <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-gradient-to-tr from-[#00d4ff]/10 to-[#892fff]/10 rounded-full blur-xl" />
      </div>

      {/* 作者信息区域骨架 */}
      <div className="p-4 pb-0 space-y-4">
        <div className="flex items-center gap-3">
          <div className="w-5 h-5 bg-white/10 rounded-full animate-pulse" />
          <div className="h-3 bg-white/10 rounded w-20 animate-pulse" />
        </div>

        {/* 标题骨架 */}
        <div className="h-5 bg-white/10 rounded w-3/4 animate-pulse" />
      </div>

      {/* 波形预览区域骨架 */}
      <div className="relative h-22 p-3">
        <div className="w-full h-full bg-white/10 rounded-2xl animate-pulse" />
      </div>

      {/* 信息区域骨架 */}
      <div className="p-4 pt-3">
        {/* 统计信息和操作按钮骨架 */}
        <div className="flex items-center justify-between">
          {/* 统计信息骨架 */}
          <div className="flex items-center gap-6 flex-1">
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-white/10 rounded animate-pulse" />
              <div className="h-4 bg-white/10 rounded w-8 animate-pulse" />
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-white/10 rounded animate-pulse" />
              <div className="h-4 bg-white/10 rounded w-8 animate-pulse" />
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-white/10 rounded animate-pulse" />
              <div className="h-4 bg-white/10 rounded w-8 animate-pulse" />
            </div>
          </div>

          {/* 操作按钮骨架 */}
          <div className="flex items-center gap-3 ml-6">
            <div className="w-8 h-8 bg-white/10 rounded-xl animate-pulse" />
            <div className="w-8 h-8 bg-white/10 rounded-xl animate-pulse" />
          </div>
        </div>
      </div>
    </div>
  )
}
