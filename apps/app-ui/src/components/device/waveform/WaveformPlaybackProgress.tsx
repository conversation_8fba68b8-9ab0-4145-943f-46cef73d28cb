import React, { useRef, useEffect, useCallback } from 'react'
import { motion } from 'framer-motion'
import type { WaveformRecording } from '@/types/recording'

export interface WaveformPlaybackProgressProps {
  recording: WaveformRecording | null
  currentTime: number
  className?: string
  height?: number
  onSeek?: (time: number) => void
}

/**
 * 波形回放进度组件
 * 显示累积式的波形进度，已播放部分为彩色，未播放部分为灰色
 */
export const WaveformPlaybackProgress: React.FC<WaveformPlaybackProgressProps> = ({
  recording,
  currentTime,
  className = '',
  height = 64,
  onSeek
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)

  // 根据强度获取颜色（更现代化的配色）
  const getIntensityColor = useCallback((intensity: number): string => {
    if (intensity >= 8) return '#ff0080' // 极高强度 - 鲜艳粉色
    if (intensity >= 6) return '#8b5cf6' // 高强度 - 紫色
    if (intensity >= 4) return '#06b6d4' // 中高强度 - 青色
    if (intensity >= 2) return '#10b981' // 中强度 - 绿色
    return '#6366f1' // 低强度 - 靛蓝色
  }, [])

  // 计算波形数据点的强度
  const calculateIntensityData = useCallback((points: any[]) => {
    if (!points || points.length < 2) return []

    const intensityData = []

    for (let i = 0; i < points.length; i++) {
      const [, intensity] = points[i]
      intensityData.push(intensity || 1)
    }

    return intensityData
  }, [])

  // 绘制波形进度
  const drawWaveform = useCallback(() => {
    const canvas = canvasRef.current
    if (!canvas || !recording) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    const dpr = window.devicePixelRatio || 1

    // 获取父容器的尺寸，确保Canvas撑满
    const parentElement = canvas.parentElement
    if (!parentElement) return

    const parentRect = parentElement.getBoundingClientRect()
    const width = parentRect.width
    const canvasHeight = height

    // 设置Canvas的实际尺寸（考虑高DPI）
    canvas.width = width * dpr
    canvas.height = canvasHeight * dpr

    // 设置Canvas的显示尺寸，确保撑满父容器
    canvas.style.width = '100%'
    canvas.style.height = '100%'

    // 缩放绘制上下文以适应高DPI
    ctx.scale(dpr, dpr)

    // 清空画布
    ctx.clearRect(0, 0, width, canvasHeight)

    // 绘制更美观的背景渐变
    const backgroundGradient = ctx.createLinearGradient(0, 0, 0, canvasHeight)
    backgroundGradient.addColorStop(0, 'rgba(20, 24, 38, 0.95)')
    backgroundGradient.addColorStop(0.3, 'rgba(25, 30, 48, 0.8)')
    backgroundGradient.addColorStop(0.7, 'rgba(30, 36, 58, 0.6)')
    backgroundGradient.addColorStop(1, 'rgba(35, 42, 68, 0.4)')
    ctx.fillStyle = backgroundGradient
    ctx.fillRect(0, 0, width, canvasHeight)

    // 添加微妙的边框效果
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.08)'
    ctx.lineWidth = 1
    ctx.strokeRect(0, 0, width, canvasHeight)

    // 计算强度数据
    const intensityData = calculateIntensityData(recording.points)
    if (intensityData.length < 2) return

    // 计算当前播放进度
    const progressRatio = recording.duration > 0 ? Math.min(1, currentTime / recording.duration) : 0
    const progressIndex = Math.min(
      intensityData.length - 1,
      Math.floor(progressRatio * intensityData.length)
    )
    const progressX = progressRatio * width

    // 绘制基础波形轮廓（未播放部分的背景）
    ctx.globalAlpha = 0.15
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)'
    ctx.lineWidth = 1.5
    ctx.lineCap = 'round'
    ctx.lineJoin = 'round'

    ctx.beginPath()
    let firstPoint = true
    for (let i = 0; i < intensityData.length; i++) {
      const intensity = intensityData[i]
      const x = (i / (intensityData.length - 1)) * width
      const y = canvasHeight * 0.9 - (intensity / 9) * canvasHeight * 0.7

      if (firstPoint) {
        ctx.moveTo(x, y)
        firstPoint = false
      } else {
        ctx.lineTo(x, y)
      }
    }
    ctx.stroke()

    // 绘制已播放部分（美观的彩色波形）
    if (progressIndex > 0) {
      ctx.globalAlpha = 1
      ctx.lineCap = 'round'
      ctx.lineJoin = 'round'

      // 减少绘制点数以提高性能和美观度，但确保包含进度点
      const step = Math.max(1, Math.floor(intensityData.length / 300))

      // 创建要绘制的点的索引数组，确保包含关键点
      const pointsToRender = new Set<number>()

      // 添加步进点
      for (let i = 0; i <= progressIndex; i += step) {
        pointsToRender.add(i)
      }

      // 确保包含关键点
      pointsToRender.add(0) // 第一个点
      pointsToRender.add(progressIndex) // 当前进度点

      // 如果进度接近100%，确保包含最后几个点
      if (progressRatio > 0.95) {
        for (let i = Math.max(0, intensityData.length - 5); i < intensityData.length; i++) {
          pointsToRender.add(i)
        }
      }

      // 转换为排序数组
      const sortedPoints = Array.from(pointsToRender).sort((a, b) => a - b)

      for (let idx = 1; idx < sortedPoints.length; idx++) {
        const prevIndex = sortedPoints[idx - 1]
        const currentIndex = sortedPoints[idx]

        const prevIntensity = intensityData[prevIndex]
        const currentIntensity = intensityData[currentIndex]

        const prevX = (prevIndex / (intensityData.length - 1)) * width
        const prevY = canvasHeight * 0.9 - (prevIntensity / 9) * canvasHeight * 0.7
        const currentX = (currentIndex / (intensityData.length - 1)) * width
        const currentY = canvasHeight * 0.9 - (currentIntensity / 9) * canvasHeight * 0.7

        // 主波形线条
        ctx.globalAlpha = 0.9
        ctx.strokeStyle = getIntensityColor(currentIntensity)
        ctx.lineWidth = 2.5

        ctx.beginPath()
        ctx.moveTo(prevX, prevY)
        ctx.lineTo(currentX, currentY)
        ctx.stroke()

        // 为高强度添加内发光效果
        if (currentIntensity > 4) {
          ctx.globalAlpha = 0.4
          ctx.strokeStyle = getIntensityColor(currentIntensity)
          ctx.lineWidth = 4
          ctx.filter = 'blur(1px)'

          ctx.beginPath()
          ctx.moveTo(prevX, prevY)
          ctx.lineTo(currentX, currentY)
          ctx.stroke()

          ctx.filter = 'none'
        }

        // 为极高强度添加外发光
        if (currentIntensity > 7) {
          ctx.globalAlpha = 0.2
          ctx.strokeStyle = getIntensityColor(currentIntensity)
          ctx.lineWidth = 8
          ctx.filter = 'blur(3px)'

          ctx.beginPath()
          ctx.moveTo(prevX, prevY)
          ctx.lineTo(currentX, currentY)
          ctx.stroke()

          ctx.filter = 'none'
        }
      }
    }

    // 绘制进度指示器（更精致的设计）
    if (progressX > 0 && progressX <= width) {
      // 进度线的发光效果
      ctx.globalAlpha = 0.3
      ctx.strokeStyle = '#ffffff'
      ctx.lineWidth = 8
      ctx.filter = 'blur(4px)'

      ctx.beginPath()
      ctx.moveTo(progressX, 0)
      ctx.lineTo(progressX, canvasHeight)
      ctx.stroke()

      ctx.filter = 'none'

      // 主进度线
      ctx.globalAlpha = 0.9
      ctx.strokeStyle = '#ffffff'
      ctx.lineWidth = 2
      ctx.setLineDash([])

      ctx.beginPath()
      ctx.moveTo(progressX, 0)
      ctx.lineTo(progressX, canvasHeight)
      ctx.stroke()

      // 绘制进度点
      if (progressIndex < intensityData.length) {
        const currentIntensity = intensityData[progressIndex]
        const y = canvasHeight * 0.9 - (currentIntensity / 9) * canvasHeight * 0.7

        // 外圈发光
        ctx.globalAlpha = 0.4
        ctx.fillStyle = getIntensityColor(currentIntensity)
        ctx.beginPath()
        ctx.arc(progressX, y, 12, 0, Math.PI * 2)
        ctx.fill()

        // 中圈
        ctx.globalAlpha = 0.8
        ctx.fillStyle = getIntensityColor(currentIntensity)
        ctx.beginPath()
        ctx.arc(progressX, y, 6, 0, Math.PI * 2)
        ctx.fill()

        // 内圈（白色核心）
        ctx.globalAlpha = 1
        ctx.fillStyle = '#ffffff'
        ctx.beginPath()
        ctx.arc(progressX, y, 3, 0, Math.PI * 2)
        ctx.fill()
      }
    }

    ctx.globalAlpha = 1
  }, [recording, currentTime, height, getIntensityColor, calculateIntensityData])

  // 处理点击事件（跳转到指定时间）
  const handleClick = useCallback(
    (e: React.MouseEvent) => {
      if (!recording || !onSeek) return

      const canvas = canvasRef.current
      if (!canvas) return

      // 使用父容器的尺寸来计算点击位置，确保与绘制逻辑一致
      const parentElement = canvas.parentElement
      if (!parentElement) return

      const rect = parentElement.getBoundingClientRect()
      const x = e.clientX - rect.left
      const ratio = x / rect.width
      const newTime = ratio * recording.duration

      onSeek(Math.max(0, Math.min(recording.duration, newTime)))
    },
    [recording, onSeek]
  )

  // 数据变化时重绘
  useEffect(() => {
    drawWaveform()
  }, [drawWaveform])

  // 监听容器尺寸变化
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const resizeObserver = new ResizeObserver(() => {
      // 延迟一帧执行，确保DOM更新完成
      requestAnimationFrame(() => {
        drawWaveform()
      })
    })

    // 同时监听Canvas和其父容器的尺寸变化
    const parentElement = canvas.parentElement
    if (parentElement) {
      resizeObserver.observe(parentElement)
    }
    resizeObserver.observe(canvas)

    // 初始绘制
    requestAnimationFrame(() => {
      drawWaveform()
    })

    return () => {
      resizeObserver.disconnect()
    }
  }, [drawWaveform])

  if (!recording) {
    return (
      <motion.div
        className={`relative overflow-hidden rounded-xl bg-gradient-to-r from-gray-800/30 to-gray-700/20 flex items-center justify-center border border-white/5 ${className}`}
        style={{ height: `${height}px` }}
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
      >
        <div className="flex items-center gap-2 text-white/40">
          <div className="w-2 h-2 bg-white/20 rounded-full animate-pulse" />
          <p className="text-sm font-medium">暂无录制数据</p>
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      className={`relative overflow-hidden rounded-xl cursor-pointer shadow-lg backdrop-blur-sm border border-white/10 ${className}`}
      style={{
        height: `${height}px`,
        background:
          'linear-gradient(135deg, rgba(30, 35, 55, 0.8) 0%, rgba(25, 30, 48, 0.6) 50%, rgba(20, 25, 40, 0.4) 100%)'
      }}
      initial={{ opacity: 0, y: 10, scale: 0.98 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ duration: 0.4, ease: 'easeOut' }}
      whileHover={{
        scale: 1.02,
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)'
      }}
      onClick={handleClick}
    >
      <canvas ref={canvasRef} className="w-full h-full rounded-xl" />

      {/* 内发光边框效果 */}
      <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-pink-500/5 pointer-events-none" />

      {/* 顶部高光效果 */}
      <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none" />
    </motion.div>
  )
}
