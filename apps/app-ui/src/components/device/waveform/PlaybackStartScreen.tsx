import React from 'react'
import { Icon } from '@iconify/react'
import { motion } from 'framer-motion'
import type { WaveformRecording } from '@/types/recording'

interface PlaybackStartScreenProps {
  recording: WaveformRecording
  onStart: () => void
  onCancel: () => void
  isLoading?: boolean
}

export default function PlaybackStartScreen({
  recording,
  onStart,
  onCancel,
  isLoading = false
}: PlaybackStartScreenProps) {
  return (
    <div className="absolute inset-0 z-20 bg-black/40 backdrop-blur-xl border border-white/20 flex items-center justify-center">
      <div className="text-center space-y-6 px-6 flex flex-col items-center justify-center">
        {/* 录制信息 */}
        <motion.div
          className="space-y-3"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="text-xl font-bold text-white drop-shadow-lg">{recording.name}</h2>
          <div className="flex items-center justify-center">
            <div className="flex items-center gap-2 px-3 py-1.5 bg-white/10 backdrop-blur-sm rounded-full border border-white/20">
              <Icon icon="solar:clock-circle-linear" width={14} className="text-white/80" />
              <span className="text-white/90 font-medium text-sm">
                {Math.round((recording.duration / 1000) * 10) / 10}秒
              </span>
            </div>
          </div>
        </motion.div>

        {/* 开始按钮 */}
        <motion.button
          onClick={onStart}
          disabled={isLoading}
          className={`
            w-20 h-20 rounded-full flex items-center justify-center
            bg-gradient-to-br from-[#ff2d97] via-[#892fff] to-[#4c1d95]
            shadow-xl shadow-[#ff2d97]/40
            backdrop-blur-sm border border-white/20
            text-white transition-all duration-300
            ${
              isLoading
                ? 'opacity-50 cursor-not-allowed'
                : 'hover:shadow-[#ff2d97]/60 hover:scale-105'
            }
          `}
          whileHover={isLoading ? {} : { scale: 1.08 }}
          whileTap={isLoading ? {} : { scale: 0.95 }}
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.6, type: 'spring', stiffness: 200 }}
        >
          {isLoading ? (
            <div className="w-6 h-6 border-2 border-white/30 border-t-white rounded-full animate-spin" />
          ) : (
            <Icon icon="solar:play-bold" width={24} />
          )}
        </motion.button>

        {/* 提示文字 */}
        <motion.p
          className="text-white/60 text-sm font-medium"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6, duration: 0.4 }}
        >
          开始播放录制内容
        </motion.p>

        {/* 取消按钮 */}
        <motion.button
          onClick={onCancel}
          disabled={isLoading}
          className="flex items-center gap-1.5 px-4 py-2 bg-white/15 hover:bg-white/25 text-white/80 hover:text-white rounded-full transition-all duration-300 backdrop-blur-sm border border-white/30 font-medium text-sm"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.4 }}
        >
          <span>取消</span>
        </motion.button>
      </div>
    </div>
  )
}
