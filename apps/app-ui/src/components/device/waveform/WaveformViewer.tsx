import React, { useRef, useEffect, useCallback, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

export interface WaveformDataPoint {
  intensity: number
  timestamp: number
}

export interface WaveformViewerProps {
  data: WaveformDataPoint[]
  currentIntensity?: number
  className?: string
  height?: number
  showCurrentIndicator?: boolean
  showControls?: boolean
  emptyMessage?: string
  onViewportChange?: (startIndex: number, endIndex: number) => void
}

export const WaveformViewer: React.FC<WaveformViewerProps> = ({
  data,
  currentIntensity = 0,
  className = '',
  height = 64,
  showCurrentIndicator = true,
  showControls = true,
  emptyMessage = '实时波形',
  onViewportChange
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [viewportStart, setViewportStart] = useState(0)
  const [isDragging, setIsDragging] = useState(false)
  const [dragStartX, setDragStartX] = useState(0)
  const [dragStartViewport, setDragStartViewport] = useState(0)
  const [userScrolled, setUserScrolled] = useState(false)
  const [animationProgress, setAnimationProgress] = useState(0)
  const animationRef = useRef<number>()

  // 每屏显示的数据点数量
  const pointsPerView = 200

  // 获取当前视窗的数据
  const getViewportData = useCallback(() => {
    if (data.length === 0) return []

    const endIndex = Math.min(viewportStart + pointsPerView, data.length)
    const startIndex = Math.max(0, endIndex - pointsPerView)

    return data.slice(startIndex, endIndex)
  }, [data, viewportStart, pointsPerView])

  // 根据强度获取颜色
  const getIntensityColor = useCallback((intensity: number): string => {
    if (intensity >= 7) return '#ff2d97' // 高强度 - 粉色
    if (intensity >= 4) return '#892fff' // 中强度 - 紫色
    if (intensity >= 2) return '#00d4ff' // 中低强度 - 蓝色
    return '#4ade80' // 低强度 - 绿色
  }, [])

  // 绘制波形
  const drawWaveform = useCallback(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    const dpr = window.devicePixelRatio || 1
    let targetWidth: number
    let targetHeight: number

    // 获取父容器的尺寸
    const parentElement = canvas.parentElement
    if (parentElement) {
      const parentRect = parentElement.getBoundingClientRect()
      targetWidth = parentRect.width
      targetHeight = parentRect.height

      // 如果父容器尺寸为0，使用props的height作为备用
      if (targetWidth === 0 || targetHeight === 0) {
        const canvasRect = canvas.getBoundingClientRect()
        targetWidth = canvasRect.width || parentRect.width || 300
        targetHeight = height // 使用props传入的height
      }
    } else {
      // 备用方案：使用props的height
      targetWidth = 300
      targetHeight = height // 使用props传入的height
    }

    // 设置Canvas的实际尺寸（考虑高DPI）
    canvas.width = targetWidth * dpr
    canvas.height = targetHeight * dpr

    // 设置Canvas的显示尺寸
    canvas.style.width = targetWidth + 'px'
    canvas.style.height = targetHeight + 'px'

    // 缩放绘制上下文以适应高DPI
    ctx.scale(dpr, dpr)

    const canvasWidth = targetWidth
    const canvasHeight = targetHeight

    // 清空画布
    ctx.clearRect(0, 0, canvasWidth, canvasHeight)

    // 绘制背景渐变
    const backgroundGradient = ctx.createLinearGradient(0, 0, 0, canvasHeight)
    backgroundGradient.addColorStop(0, 'rgba(29, 33, 53, 0.8)')
    backgroundGradient.addColorStop(0.5, 'rgba(29, 33, 53, 0.5)')
    backgroundGradient.addColorStop(1, 'rgba(29, 33, 53, 0.3)')
    ctx.fillStyle = backgroundGradient
    ctx.fillRect(0, 0, canvasWidth, canvasHeight)

    const viewportData = getViewportData()
    if (viewportData.length < 2) return

    // 绘制波形线段
    ctx.lineWidth = 3
    ctx.lineCap = 'round'
    ctx.lineJoin = 'round'

    const time = Date.now() * 0.002 // 动画时间

    // 绘制波形线段 - 带动画效果
    ctx.lineWidth = 3
    ctx.lineCap = 'round'
    ctx.lineJoin = 'round'

    for (let i = 1; i < viewportData.length; i++) {
      const prevPoint = viewportData[i - 1]
      const currentPoint = viewportData[i]

      const color = getIntensityColor(currentPoint.intensity)

      const prevX = ((i - 1) / (viewportData.length - 1)) * canvasWidth
      const prevY = canvasHeight - (prevPoint.intensity / 9) * canvasHeight
      const currentX = (i / (viewportData.length - 1)) * canvasWidth
      const currentY = canvasHeight - (currentPoint.intensity / 9) * canvasHeight

      // 线条渐变
      const lineGradient = ctx.createLinearGradient(prevX, prevY, currentX, currentY)
      lineGradient.addColorStop(0, getIntensityColor(prevPoint.intensity))
      lineGradient.addColorStop(1, color)

      // 绘制主线条
      ctx.globalAlpha = 0.9
      ctx.strokeStyle = lineGradient
      ctx.lineWidth = 3 + Math.sin(time * 2 + i * 0.2) * 1 // 更明显的粗细变化

      ctx.beginPath()
      ctx.moveTo(prevX, prevY)
      ctx.lineTo(currentX, currentY)
      ctx.stroke()

      // 绘制发光效果（高强度点）
      if (currentPoint.intensity > 5) {
        ctx.globalAlpha = 0.3
        ctx.strokeStyle = color
        ctx.lineWidth = 6
        ctx.filter = 'blur(2px)'

        ctx.beginPath()
        ctx.moveTo(prevX, prevY)
        ctx.lineTo(currentX, currentY)
        ctx.stroke()

        ctx.filter = 'none'
      }

      // 绘制数据点脉冲效果（降低门槛，更容易看到）
      if (currentPoint.intensity > 4 && i % 6 === 0) {
        const pulseRadius = 3 + Math.sin(time * 4 + i * 0.8) * 2
        const pulseOpacity = 0.6 + Math.sin(time * 3 + i * 0.5) * 0.4

        ctx.globalAlpha = pulseOpacity
        ctx.fillStyle = color

        ctx.beginPath()
        ctx.arc(currentX, currentY, pulseRadius, 0, Math.PI * 2)
        ctx.fill()

        // 外圈光晕
        ctx.globalAlpha = 0.15
        ctx.beginPath()
        ctx.arc(currentX, currentY, pulseRadius * 2, 0, Math.PI * 2)
        ctx.fill()
      }
    }

    // 绘制当前强度指示线 - 带呼吸效果
    if (showCurrentIndicator && currentIntensity > 0) {
      const y = canvasHeight - (currentIntensity / 9) * canvasHeight
      const breathe = 0.5 + Math.sin(time * 3) * 0.3 // 更明显的呼吸效果

      // 主指示线
      ctx.strokeStyle = '#ffffff'
      ctx.lineWidth = 2
      ctx.setLineDash([4, 4])
      ctx.globalAlpha = breathe

      ctx.beginPath()
      ctx.moveTo(0, y)
      ctx.lineTo(canvasWidth, y)
      ctx.stroke()

      // 发光效果
      ctx.strokeStyle = getIntensityColor(currentIntensity)
      ctx.lineWidth = 3
      ctx.globalAlpha = breathe * 0.4
      ctx.filter = 'blur(2px)'

      ctx.beginPath()
      ctx.moveTo(0, y)
      ctx.lineTo(canvasWidth, y)
      ctx.stroke()

      ctx.filter = 'none'
      ctx.setLineDash([])
    }

    ctx.globalAlpha = 1
  }, [
    data,
    viewportStart,
    currentIntensity,
    showCurrentIndicator,
    getIntensityColor,
    getViewportData,
    height,
    animationProgress
  ])

  // 处理拖拽开始
  const handleDragStart = useCallback(
    (clientX: number) => {
      setIsDragging(true)
      setDragStartX(clientX)
      setDragStartViewport(viewportStart)
    },
    [viewportStart]
  )

  // 处理拖拽移动
  const handleDragMove = useCallback(
    (clientX: number) => {
      if (!isDragging || !containerRef.current) return

      const deltaX = clientX - dragStartX
      const containerWidth = containerRef.current.getBoundingClientRect().width
      const deltaPoints = Math.round((deltaX / containerWidth) * pointsPerView)

      const newViewportStart = Math.max(
        0,
        Math.min(data.length - pointsPerView, dragStartViewport - deltaPoints)
      )

      setViewportStart(newViewportStart)
      setUserScrolled(true) // 标记用户已手动滚动
    },
    [isDragging, dragStartX, dragStartViewport, data.length, pointsPerView]
  )

  // 处理拖拽结束
  const handleDragEnd = useCallback(() => {
    setIsDragging(false)
  }, [])

  // 鼠标事件
  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault()
      handleDragStart(e.clientX)
    },
    [handleDragStart]
  )

  const handleMouseMove = useCallback(
    (e: React.MouseEvent) => {
      handleDragMove(e.clientX)
    },
    [handleDragMove]
  )

  const handleMouseUp = useCallback(() => {
    handleDragEnd()
  }, [handleDragEnd])

  // 触摸事件
  const handleTouchStart = useCallback(
    (e: React.TouchEvent) => {
      e.preventDefault()
      const touch = e.touches[0]
      if (touch) {
        handleDragStart(touch.clientX)
      }
    },
    [handleDragStart]
  )

  const handleTouchMove = useCallback(
    (e: React.TouchEvent) => {
      e.preventDefault()
      const touch = e.touches[0]
      if (touch) {
        handleDragMove(touch.clientX)
      }
    },
    [handleDragMove]
  )

  const handleTouchEnd = useCallback(
    (e: React.TouchEvent) => {
      e.preventDefault()
      handleDragEnd()
    },
    [handleDragEnd]
  )

  // 自动滚动到最新数据
  const scrollToLatest = useCallback(() => {
    if (data.length > pointsPerView) {
      setViewportStart(data.length - pointsPerView)
    } else {
      setViewportStart(0)
    }
    setUserScrolled(false) // 重置手动滚动标记，恢复自动滚动
  }, [data.length, pointsPerView])

  // 滚动到开始
  const scrollToStart = useCallback(() => {
    setViewportStart(0)
    setUserScrolled(true) // 标记为手动滚动，停止自动滚动
  }, [])

  // 动画进度更新时重绘
  useEffect(() => {
    drawWaveform()
  }, [animationProgress, drawWaveform])

  // 启动动画循环
  useEffect(() => {
    let isActive = true

    const animate = () => {
      if (!isActive) return

      setAnimationProgress(prev => (prev + 0.01) % 1)
      animationRef.current = requestAnimationFrame(animate)
    }

    // 启动动画
    animationRef.current = requestAnimationFrame(animate)

    return () => {
      isActive = false
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [])

  // 数据变化时重绘
  useEffect(() => {
    drawWaveform()
  }, [drawWaveform])

  // 监听容器尺寸变化
  useEffect(() => {
    const canvas = canvasRef.current
    const container = containerRef.current
    if (!canvas || !container) return

    // 初始化
    const initCanvas = () => {
      drawWaveform()
    }

    // 延迟初始化，确保容器尺寸已确定
    const timer = setTimeout(initCanvas, 100)

    // ResizeObserver监听容器尺寸变化
    const resizeObserver = new ResizeObserver(() => {
      drawWaveform()
    })

    resizeObserver.observe(container)

    // 监听屏幕方向变化（移动端）
    const handleResize = () => {
      setTimeout(() => {
        drawWaveform()
      }, 100)
    }

    window.addEventListener('resize', handleResize)
    window.addEventListener('orientationchange', handleResize)

    return () => {
      clearTimeout(timer)
      resizeObserver.disconnect()
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('orientationchange', handleResize)
    }
  }, [drawWaveform])

  // 数据长度变化时自动滚动到最新（如果用户没有手动滚动）
  useEffect(() => {
    if (data.length > pointsPerView && !isDragging && !userScrolled) {
      const maxViewportStart = data.length - pointsPerView
      setViewportStart(maxViewportStart)
    }
  }, [data.length, pointsPerView, isDragging, userScrolled])

  // 视窗变化时通知父组件
  useEffect(() => {
    if (onViewportChange) {
      const endIndex = Math.min(viewportStart + pointsPerView, data.length)
      const startIndex = Math.max(0, endIndex - pointsPerView)
      onViewportChange(startIndex, endIndex)
    }
  }, [viewportStart, data.length, pointsPerView, onViewportChange])

  // 窗口大小变化时重绘
  useEffect(() => {
    const handleResize = () => {
      setTimeout(drawWaveform, 100)
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [drawWaveform])

  const hasData = data.length > 0
  const canScrollLeft = viewportStart > 0
  const canScrollRight = viewportStart < data.length - pointsPerView
  const isAtLatest = viewportStart >= data.length - pointsPerView

  return (
    <motion.div
      className={`relative ${className}`}
      style={{ height: `${height}px` }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: 'easeOut' }}
    >
      <motion.div
        ref={containerRef}
        className="relative w-full h-full overflow-hidden rounded-lg cursor-grab active:cursor-grabbing backdrop-blur-sm border border-white/10"
        initial={{ scale: 0.95 }}
        animate={{ scale: 1 }}
        transition={{ duration: 0.3, delay: 0.2 }}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onTouchCancel={handleTouchEnd}
      >
        <canvas ref={canvasRef} className="w-full h-full" />

        {/* 无数据提示 */}
        <AnimatePresence>
          {!hasData && (
            <motion.div
              className="absolute inset-0 flex items-center justify-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <motion.div
                className="text-center"
                initial={{ scale: 0.8, y: 10 }}
                animate={{ scale: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.1 }}
              >
                <motion.div
                  className="w-6 h-6 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-2 relative"
                  animate={{
                    scale: [1, 1.1, 1],
                    opacity: [0.5, 0.8, 0.5]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: 'easeInOut'
                  }}
                >
                  <motion.div
                    className="w-2 h-2 bg-white/40 rounded-full"
                    animate={{
                      scale: [0.8, 1.2, 0.8],
                      opacity: [0.4, 1, 0.4]
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      ease: 'easeInOut',
                      delay: 0.2
                    }}
                  />
                  {/* 波纹效果 */}
                  <motion.div
                    className="absolute inset-0 border border-white/20 rounded-full"
                    animate={{
                      scale: [1, 2],
                      opacity: [0.5, 0]
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: 'easeOut'
                    }}
                  />
                </motion.div>
                <motion.p
                  className="text-white/40 text-xs"
                  animate={{ opacity: [0.6, 1, 0.6] }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: 'easeInOut'
                  }}
                >
                  {emptyMessage}
                </motion.p>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* 控制按钮 */}
      {showControls && hasData && data.length > pointsPerView && (
        <div className="absolute bottom-2 left-2 flex gap-1">
          <AnimatePresence>
            {canScrollLeft && (
              <motion.button
                key="scroll-start"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                whileHover={{
                  scale: 1.1,
                  backgroundColor: 'rgba(0, 0, 0, 0.8)',
                  boxShadow: '0 0 12px rgba(255, 255, 255, 0.15)'
                }}
                whileTap={{ scale: 0.9 }}
                transition={{ duration: 0.2 }}
                className="w-6 h-6 bg-black/50 rounded-full flex items-center justify-center text-white/60 backdrop-blur-sm border border-white/10"
                onClick={scrollToStart}
              >
                <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6 1.41-1.41z" />
                  <path d="M6 6h2v12H6z" />
                </svg>
              </motion.button>
            )}

            {!isAtLatest && (
              <motion.button
                key="scroll-latest"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                whileHover={{
                  scale: 1.1,
                  backgroundColor: 'rgba(0, 0, 0, 0.8)',
                  boxShadow: '0 0 12px rgba(255, 255, 255, 0.15)'
                }}
                whileTap={{ scale: 0.9 }}
                transition={{ duration: 0.2 }}
                className="w-6 h-6 bg-black/50 rounded-full flex items-center justify-center text-white/60 backdrop-blur-sm border border-white/10"
                onClick={scrollToLatest}
              >
                <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6-1.41 1.41z" />
                  <path d="M16 6h2v12h-2z" />
                </svg>
              </motion.button>
            )}
          </AnimatePresence>
        </div>
      )}
    </motion.div>
  )
}
