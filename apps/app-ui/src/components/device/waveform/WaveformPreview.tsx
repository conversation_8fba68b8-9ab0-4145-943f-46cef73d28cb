import React, { useRef, useEffect, useCallback } from 'react'
import type { Track, WaveformLibraryItem } from '@/types/swipe-mode'
import { TrackCalculator } from '@/utils/swipe-mode/track-calculator'
import { WaveformRenderer, type WaveformPoint } from '@/utils/waveform-renderer'

export interface WaveformPreviewProps {
  waveform: WaveformLibraryItem | Track
  width?: number
  height?: number
  className?: string
  showAnimation?: boolean
}

/**
 * 波形预览组件
 * 用于在波形库卡片中显示静态的波形预览图
 */
export const WaveformPreview: React.FC<WaveformPreviewProps> = ({
  waveform,
  width = 300,
  height = 60,
  className = '',
  showAnimation = true
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationRef = useRef<number>()

  // 智能压缩轨迹数据，保持关键特征
  const compressTrackData = useCallback((points: any[], targetPoints: number = 120) => {
    if (!points || points.length === 0) return []
    if (points.length <= targetPoints) return points

    const compressed = []
    const step = points.length / targetPoints

    // 确保包含首尾点
    compressed.push(points[0])

    // 采样中间点，但保持数据的变化特征
    for (let i = 1; i < targetPoints - 1; i++) {
      const index = Math.floor(i * step)
      if (index < points.length) {
        compressed.push(points[index])
      }
    }

    // 确保包含最后一个点
    if (points.length > 1) {
      compressed.push(points[points.length - 1])
    }

    return compressed
  }, [])

  // 计算强度数据，确保数据差异化
  const calculateIntensityData = useCallback((points: any[]): WaveformPoint[] => {
    if (!points || points.length < 2) return []

    const waveformPoints: WaveformPoint[] = []

    for (let i = 0; i < points.length; i++) {
      let intensity = 1

      if (i > 0) {
        // 计算距离和时间差
        const prevPoint = points[i - 1]
        const currentPoint = points[i]

        const dx = currentPoint.x - prevPoint.x
        const dy = currentPoint.y - prevPoint.y
        const distance = Math.sqrt(dx * dx + dy * dy)
        const timeDiff = currentPoint.timestamp - prevPoint.timestamp

        // 计算速度 (像素/毫秒)
        const speed = timeDiff > 0 ? distance / timeDiff : 0

        // 将速度映射到强度 (1-9)
        if (speed > 1.5) intensity = 9
        else if (speed > 1.0) intensity = 8
        else if (speed > 0.8) intensity = 7
        else if (speed > 0.6) intensity = 6
        else if (speed > 0.4) intensity = 5
        else if (speed > 0.3) intensity = 4
        else if (speed > 0.2) intensity = 3
        else if (speed > 0.1) intensity = 2
        else intensity = 1
      }

      waveformPoints.push({
        intensity,
        timestamp: points[i].timestamp
      })
    }

    return waveformPoints
  }, [])

  // 绘制波形预览
  const drawWaveform = useCallback(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    const dpr = window.devicePixelRatio || 1

    // 设置Canvas的实际尺寸（考虑高DPI）
    canvas.width = width * dpr
    canvas.height = height * dpr

    // 设置Canvas的显示尺寸
    canvas.style.width = width + 'px'
    canvas.style.height = height + 'px'

    // 缩放绘制上下文以适应高DPI
    ctx.scale(dpr, dpr)

    // 清空画布
    ctx.clearRect(0, 0, width, height)

    // 绘制背景渐变（与WaveformViewer保持一致）
    const backgroundGradient = ctx.createLinearGradient(0, 0, 0, height)
    backgroundGradient.addColorStop(0, 'rgba(29, 33, 53, 0.8)')
    backgroundGradient.addColorStop(0.5, 'rgba(29, 33, 53, 0.5)')
    backgroundGradient.addColorStop(1, 'rgba(29, 33, 53, 0.3)')
    ctx.fillStyle = backgroundGradient
    ctx.fillRect(0, 0, width, height)

    // 检查数据有效性
    if (!waveform.points || waveform.points.length < 2) {
      // 如果没有有效数据，绘制一个基于ID的独特占位波形
      ctx.strokeStyle = '#4ade80'
      ctx.lineWidth = 2
      ctx.globalAlpha = 0.3

      ctx.beginPath()
      const seed = waveform.id
        ? waveform.id.charCodeAt(0) + waveform.id.charCodeAt(waveform.id.length - 1)
        : Math.random() * 100
      for (let i = 0; i < width; i += 6) {
        const y =
          height / 2 +
          Math.sin((i + seed) * 0.08) * (height * 0.25) +
          Math.sin((i + seed * 1.5) * 0.03) * (height * 0.15) +
          Math.sin((i + seed * 0.7) * 0.15) * (height * 0.1)
        if (i === 0) {
          ctx.moveTo(i, y)
        } else {
          ctx.lineTo(i, y)
        }
      }
      ctx.stroke()
      ctx.globalAlpha = 1
      return
    }

    // 压缩轨迹数据
    const compressedPoints = compressTrackData(waveform.points, Math.floor(width / 4))

    // 计算强度数据
    const intensityData = calculateIntensityData(compressedPoints)

    if (intensityData.length < 2) return

    // 使用共享的波形渲染引擎
    WaveformRenderer.render(canvas, {
      width,
      height,
      data: intensityData,
      showAnimation,
      lineWidth: 3,
      pulseThreshold: 3,
      glowThreshold: 5
    })
  }, [
    waveform.points,
    waveform.id,
    width,
    height,
    compressTrackData,
    calculateIntensityData,
    showAnimation
  ])

  // 初始绘制和数据变化时重绘
  useEffect(() => {
    drawWaveform()
  }, [drawWaveform])

  // 如果启用动画，设置动画循环
  useEffect(() => {
    if (!showAnimation) {
      // 静态模式下只绘制一次
      drawWaveform()
      return
    }

    let isActive = true
    let animationId: number

    const animate = () => {
      if (!isActive) return
      drawWaveform()
      animationId = requestAnimationFrame(animate)
    }

    animationId = requestAnimationFrame(animate)

    return () => {
      isActive = false
      if (animationId) {
        cancelAnimationFrame(animationId)
      }
    }
  }, [showAnimation, drawWaveform])

  return (
    <div className={`relative overflow-hidden ${className}`}>
      <canvas
        ref={canvasRef}
        className="w-full h-full"
        style={{ width: `${width}px`, height: `${height}px` }}
      />

      {/* 覆盖渐变效果，增强视觉层次 */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-transparent to-black/10 pointer-events-none" />
    </div>
  )
}

/**
 * 生成波形缩略图的工具函数
 * 用于生成base64格式的预览图，可存储到数据库
 */
export const generateWaveformThumbnail = async (
  track: Track,
  width: number = 200,
  height: number = 40
): Promise<string> => {
  if (!track.points || track.points.length < 2) {
    return ''
  }

  // 压缩数据并转换为WaveformPoint格式
  const targetPoints = Math.floor(width / 4)
  const step = track.points.length / targetPoints
  const compressedPoints = []

  for (let i = 0; i < targetPoints; i++) {
    const index = Math.floor(i * step)
    if (index < track.points.length) {
      compressedPoints.push(track.points[index])
    }
  }

  // 计算强度数据
  const waveformPoints: WaveformPoint[] = []
  for (let i = 0; i < compressedPoints.length; i++) {
    let intensity = 1
    if (i > 0) {
      const prevPoint = compressedPoints[i - 1]
      const currentPoint = compressedPoints[i]
      const dx = currentPoint.x - prevPoint.x
      const dy = currentPoint.y - prevPoint.y
      const distance = Math.sqrt(dx * dx + dy * dy)
      const timeDiff = currentPoint.timestamp - prevPoint.timestamp
      const speed = timeDiff > 0 ? distance / timeDiff : 0

      if (speed > 1.5) intensity = 9
      else if (speed > 1.0) intensity = 8
      else if (speed > 0.8) intensity = 7
      else if (speed > 0.6) intensity = 6
      else if (speed > 0.4) intensity = 5
      else if (speed > 0.3) intensity = 4
      else if (speed > 0.2) intensity = 3
      else if (speed > 0.1) intensity = 2
      else intensity = 1
    }
    waveformPoints.push({ intensity })
  }

  // 使用共享的缩略图生成方法
  return WaveformRenderer.generateThumbnail(waveformPoints, width, height)
}
