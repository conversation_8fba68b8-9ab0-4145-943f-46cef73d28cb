import React from 'react'
import { Icon } from '@iconify/react'
import GradientModal from '@/components/common/gradient-modal'
import type { WaveformLibraryItem } from '@/types/swipe-mode'

interface DeleteWaveformModalProps {
  isOpen: boolean
  onClose: () => void
  waveform?: WaveformLibraryItem
  onConfirm: () => void
  isDeleting?: boolean
}

export default function DeleteWaveformModal({
  isOpen,
  onClose,
  waveform,
  onConfirm,
  isDeleting = false
}: DeleteWaveformModalProps) {
  const handleConfirm = () => {
    onConfirm()
  }

  return (
    <GradientModal
      isOpen={isOpen}
      onClose={onClose}
      title="删除波形"
      cancelText="取消"
      confirmText={isDeleting ? '删除中...' : '确认删除'}
      onCancel={onClose}
      onConfirm={handleConfirm}
      confirmLoading={isDeleting}
      confirmDisabled={isDeleting}
    >
      <div className="text-center space-y-6">
        <div className="flex justify-center">
          <div className="w-16 h-16 rounded-full bg-red-500/20 flex items-center justify-center">
            <Icon icon="solar:trash-bin-minimalistic-bold" className="w-8 h-8 text-red-400" />
          </div>
        </div>

        <div className="space-y-3">
          <p className="text-lg font-medium text-white">确定要删除这个波形吗？</p>
          {waveform && (
            <div className="space-y-2">
              <p className="text-sm text-white/70">波形名称：{waveform.name}</p>
              <p className="text-xs text-white/50">
                创建时间：{new Date(waveform.createdAt).toLocaleDateString()}
              </p>
            </div>
          )}
          <p className="text-sm text-white/60">删除后将无法恢复，请谨慎操作</p>
        </div>
      </div>
    </GradientModal>
  )
}
