import React, { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Icon } from '@iconify/react'
import type { VideoItem } from '@/types/video'
import type { StoredVideoItem } from '@/services/video-storage'
import { VideoStorageService } from '@/services/video-storage'
import { videoService } from '@/services/video'

interface VideoDrawerProps {
  isOpen: boolean
  onClose: () => void
  currentVideo: VideoItem | null
  onVideoSelect: (video: StoredVideoItem) => void
}

/**
 * 抽屉式视频选择组件
 * 集成在线视频和导入视频的选择功能
 */
export const VideoDrawer: React.FC<VideoDrawerProps> = ({
  isOpen,
  onClose,
  currentVideo,
  onVideoSelect
}) => {
  const [activeTab, setActiveTab] = useState<'online' | 'import'>('online')
  const [onlineVideos, setOnlineVideos] = useState<StoredVideoItem[]>([])
  const [importedVideos, setImportedVideos] = useState<StoredVideoItem[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isImporting, setIsImporting] = useState(false)
  const [importProgress, setImportProgress] = useState(0)
  const [deletingVideoId, setDeletingVideoId] = useState<string | null>(null)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 加载视频数据
  useEffect(() => {
    const loadVideoData = async () => {
      if (!isOpen) return

      setIsLoading(true)
      try {
        // 加载在线视频
        const online = await videoService.getOnlineVideos()
        setOnlineVideos(online)

        // 加载导入视频
        const imported = await videoService.getImportedVideos()
        setImportedVideos(imported)
      } catch (error) {
        console.error('加载视频数据失败:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadVideoData()
  }, [isOpen])

  // 监听视频时长更新
  useEffect(() => {
    const handleVideoUpdate = (updatedVideo: StoredVideoItem) => {
      console.log('🔄 收到视频更新通知:', updatedVideo.title, updatedVideo.duration)
      // 更新导入视频列表中的对应项
      setImportedVideos(prev =>
        prev.map(video => (video.id === updatedVideo.id ? updatedVideo : video))
      )
    }

    VideoStorageService.addUpdateListener(handleVideoUpdate)

    return () => {
      VideoStorageService.removeUpdateListener(handleVideoUpdate)
    }
  }, [])

  // 处理视频选择
  const handleVideoSelect = (video: StoredVideoItem) => {
    onVideoSelect(video)
    onClose()
  }

  // 处理文件导入
  const handleFileImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // 检查文件类型
    if (!file.type.startsWith('video/')) {
      alert('请选择视频文件')
      return
    }

    setIsImporting(true)
    setImportProgress(0)
    try {
      const importedVideo = await videoService.importVideo(file, progress => {
        setImportProgress(progress)
      })

      // 刷新导入视频列表
      const updatedImportedVideos = await videoService.getImportedVideos()
      setImportedVideos(updatedImportedVideos)

      // 切换到导入标签页并选择新导入的视频
      setActiveTab('import')
      onVideoSelect(importedVideo)
      onClose()
    } catch (error) {
      console.error('导入视频失败:', error)
      alert('导入视频失败，请重试')
    } finally {
      setIsImporting(false)
      setImportProgress(0)
      // 清空文件输入
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  // 触发文件选择
  const triggerFileImport = () => {
    fileInputRef.current?.click()
  }

  // 显示删除确认对话框
  const handleDeleteClick = (videoId: string, e: React.MouseEvent) => {
    e.stopPropagation() // 防止触发视频选择
    setShowDeleteConfirm(videoId)
  }

  // 确认删除导入的视频
  const handleConfirmDelete = async (videoId: string) => {
    if (deletingVideoId) return // 防止重复删除

    try {
      setDeletingVideoId(videoId)
      setShowDeleteConfirm(null)
      console.log('🗑️ 开始删除视频:', videoId)

      await videoService.deleteImportedVideo(videoId)

      // 刷新导入视频列表
      const updatedImportedVideos = await videoService.getImportedVideos()
      setImportedVideos(updatedImportedVideos)

      console.log('✅ 视频删除成功')
    } catch (error) {
      console.error('❌ 删除视频失败:', error)
      // 这里可以添加错误提示
    } finally {
      setDeletingVideoId(null)
    }
  }

  // 取消删除
  const handleCancelDelete = () => {
    setShowDeleteConfirm(null)
  }

  // 格式化时长
  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* 背景遮罩 */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
            className="fixed inset-0 bg-black/50 z-40"
          />

          {/* 抽屉内容 */}
          <motion.div
            initial={{ y: '100%' }}
            animate={{ y: 0 }}
            exit={{ y: '100%' }}
            transition={{ type: 'spring', damping: 25, stiffness: 200 }}
            className="fixed bottom-0 left-0 right-0 bg-[#1a1d2e] rounded-t-3xl z-50 max-h-[80vh] overflow-hidden"
          >
            {/* 拖拽指示器 */}
            <div className="flex justify-center py-3">
              <div className="w-12 h-1 bg-white/20 rounded-full" />
            </div>

            {/* 标题栏 */}
            <div className="flex items-center justify-between px-6 py-4 border-b border-white/10">
              <h2 className="text-white text-xl font-semibold">选择视频</h2>
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={onClose}
                className="w-8 h-8 flex items-center justify-center text-white/60 hover:text-white"
              >
                <Icon icon="solar:close-circle-linear" width={24} />
              </motion.button>
            </div>

            {/* 标签切换 */}
            <div className="flex px-6 py-4">
              <div className="flex w-full bg-white/5 rounded-xl p-1">
                <motion.button
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setActiveTab('online')}
                  className={`flex-1 py-2 rounded-lg text-sm font-medium transition-colors ${
                    activeTab === 'online'
                      ? 'bg-[#892fff] text-white'
                      : 'text-white/60 hover:text-white'
                  }`}
                >
                  在线视频
                </motion.button>
                <motion.button
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setActiveTab('import')}
                  className={`flex-1 py-2 rounded-lg text-sm font-medium transition-colors ${
                    activeTab === 'import'
                      ? 'bg-[#892fff] text-white'
                      : 'text-white/60 hover:text-white'
                  }`}
                >
                  导入视频
                </motion.button>
              </div>
            </div>

            {/* 内容区域 */}
            <div className="flex-1 overflow-y-auto px-6 pb-6">
              {isLoading ? (
                <div className="flex items-center justify-center py-12">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                    className="w-8 h-8 border-2 border-[#892fff] border-t-transparent rounded-full"
                  />
                </div>
              ) : (
                <div className="space-y-3">
                  {activeTab === 'online' ? (
                    // 在线视频列表
                    onlineVideos.map(video => (
                      <motion.div
                        key={video.id}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => handleVideoSelect(video)}
                        className={`p-4 rounded-xl cursor-pointer transition-colors ${
                          currentVideo?.id === video.id
                            ? 'bg-[#892fff]/20 border border-[#892fff]/50'
                            : 'bg-white/5 hover:bg-white/10'
                        }`}
                      >
                        <div className="flex items-center gap-4">
                          <div className="w-16 h-12 bg-white/10 rounded-lg flex items-center justify-center">
                            <Icon
                              icon="solar:videocamera-bold"
                              width={24}
                              className="text-[#892fff]"
                            />
                          </div>
                          <div className="flex-1">
                            <h3 className="text-white font-medium truncate">{video.title}</h3>
                            <p className="text-white/60 text-sm">
                              {formatDuration(video.duration)}
                            </p>
                          </div>
                          {currentVideo?.id === video.id && (
                            <Icon
                              icon="solar:play-circle-bold"
                              width={24}
                              className="text-[#892fff]"
                            />
                          )}
                        </div>
                      </motion.div>
                    ))
                  ) : (
                    <>
                      {/* 导入的视频列表 */}
                      {importedVideos.map(video => (
                        <motion.div
                          key={video.id}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => handleVideoSelect(video)}
                          className={`relative p-4 rounded-xl cursor-pointer transition-colors ${
                            currentVideo?.id === video.id
                              ? 'bg-[#892fff]/20 border border-[#892fff]/50'
                              : 'bg-white/5 hover:bg-white/10'
                          }`}
                        >
                          <div className="flex items-center gap-4">
                            <div className="w-16 h-12 bg-white/10 rounded-lg flex items-center justify-center">
                              <Icon
                                icon="solar:videocamera-bold"
                                width={24}
                                className="text-[#892fff]"
                              />
                            </div>
                            <div className="flex-1">
                              <h3 className="text-white font-medium truncate">{video.title}</h3>
                              <p className="text-white/60 text-sm">
                                {formatDuration(video.duration)}
                              </p>
                            </div>
                            <div className="flex items-center gap-2">
                              {/* 删除按钮 */}
                              <motion.button
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                                onClick={e => handleDeleteClick(video.id, e)}
                                disabled={deletingVideoId === video.id}
                                className="w-8 h-8 rounded-full bg-red-500/20 hover:bg-red-500/30 flex items-center justify-center text-red-400 hover:text-red-300 transition-colors disabled:opacity-50"
                                title="删除视频"
                              >
                                {deletingVideoId === video.id ? (
                                  <motion.div
                                    animate={{ rotate: 360 }}
                                    transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                                    className="w-4 h-4 border border-red-400 border-t-transparent rounded-full"
                                  />
                                ) : (
                                  <Icon icon="solar:trash-bin-minimalistic-bold" width={16} />
                                )}
                              </motion.button>
                            </div>
                          </div>
                        </motion.div>
                      ))}

                      {/* 导入按钮 */}
                      <motion.label
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        className="block p-4 rounded-xl bg-white/5 hover:bg-white/10 border-2 border-dashed border-white/20 hover:border-[#892fff]/50 cursor-pointer transition-colors"
                      >
                        <div className="flex items-center gap-4">
                          <div className="w-12 h-12 bg-[#892fff]/20 rounded-xl flex items-center justify-center">
                            <Icon
                              icon="solar:add-circle-bold"
                              width={24}
                              className="text-[#892fff]"
                            />
                          </div>
                          <div>
                            <h3 className="text-white font-medium">导入视频文件</h3>
                            <p className="text-white/60 text-sm">支持 MP4, AVI, MOV 格式</p>
                            <p className="text-white/40 text-xs mt-1">
                              文件仅存储在本地，不会上传到服务器
                            </p>
                          </div>
                        </div>
                        <input
                          ref={fileInputRef}
                          type="file"
                          accept="video/*"
                          onChange={handleFileImport}
                          className="hidden"
                        />
                        {isImporting && (
                          <div className="absolute inset-0 bg-black/50 rounded-xl flex items-center justify-center">
                            <div className="text-white text-sm flex flex-col items-center gap-3">
                              <motion.div
                                animate={{ rotate: 360 }}
                                transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                                className="w-6 h-6 border-2 border-white/40 border-t-white rounded-full"
                              />
                              <div className="text-center">
                                <div className="text-white font-medium">导入中...</div>
                                <div className="text-white/60 text-xs mt-1">{importProgress}%</div>
                              </div>
                              {/* 进度条 */}
                              <div className="w-32 h-1 bg-white/20 rounded-full overflow-hidden">
                                <motion.div
                                  className="h-full bg-[#892fff]"
                                  initial={{ width: 0 }}
                                  animate={{ width: `${importProgress}%` }}
                                  transition={{ duration: 0.3 }}
                                />
                              </div>
                            </div>
                          </div>
                        )}
                      </motion.label>

                      {importedVideos.length === 0 && (
                        <div className="text-center py-12">
                          <Icon
                            icon="solar:videocamera-linear"
                            width={48}
                            className="text-white/30 mx-auto mb-4"
                          />
                          <p className="text-white/60">暂无导入的视频</p>
                          <p className="text-white/40 text-sm mt-1">点击上方按钮导入视频文件</p>
                        </div>
                      )}
                    </>
                  )}
                </div>
              )}
            </div>
          </motion.div>

          {/* 删除确认对话框 */}
          {showDeleteConfirm && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/80 flex items-center justify-center z-50"
              onClick={handleCancelDelete}
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                onClick={e => e.stopPropagation()}
                className="bg-[#1a1d29] rounded-2xl p-6 mx-4 max-w-sm w-full"
              >
                <div className="text-center">
                  <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Icon
                      icon="solar:trash-bin-minimalistic-bold"
                      width={32}
                      className="text-red-400"
                    />
                  </div>
                  <h3 className="text-white text-lg font-semibold mb-2">删除视频</h3>
                  <p className="text-white/60 text-sm mb-6">
                    确定要删除这个视频吗？删除后无法恢复。
                  </p>
                  <div className="flex gap-3">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={handleCancelDelete}
                      className="flex-1 py-3 px-4 bg-white/10 hover:bg-white/20 text-white rounded-xl transition-colors"
                    >
                      取消
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => handleConfirmDelete(showDeleteConfirm)}
                      disabled={deletingVideoId === showDeleteConfirm}
                      className="flex-1 py-3 px-4 bg-red-500 hover:bg-red-600 text-white rounded-xl transition-colors disabled:opacity-50"
                    >
                      {deletingVideoId === showDeleteConfirm ? (
                        <div className="flex items-center justify-center gap-2">
                          <motion.div
                            animate={{ rotate: 360 }}
                            transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                            className="w-4 h-4 border border-white border-t-transparent rounded-full"
                          />
                          删除中...
                        </div>
                      ) : (
                        '确认删除'
                      )}
                    </motion.button>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </>
      )}
    </AnimatePresence>
  )
}
