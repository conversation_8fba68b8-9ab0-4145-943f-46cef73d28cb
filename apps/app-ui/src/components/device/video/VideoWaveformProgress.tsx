import React, { useRef, useEffect, useState, useCallback } from 'react'
import { motion } from 'framer-motion'

interface WaveformDataPoint {
  intensity: number
  timestamp: number
}

interface VideoWaveformProgressProps {
  videoElement: HTMLVideoElement | null
  currentTime: number
  duration: number
  onSeek: (time: number) => void
  className?: string
  height?: number
}

export const VideoWaveformProgress: React.FC<VideoWaveformProgressProps> = ({
  videoElement,
  currentTime,
  duration,
  onSeek,
  className = '',
  height = 60
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  const [waveformData, setWaveformData] = useState<WaveformDataPoint[]>([])
  const [isDragging, setIsDragging] = useState(false)
  const [justFinishedDragging, setJustFinishedDragging] = useState(false)
  const [, forceUpdate] = useState({})

  // 只使用 ref 管理分析状态，避免 state 和 ref 不同步的问题
  const isAnalyzingRef = useRef(false)
  const analyzedUrlRef = useRef<string>('')
  const waveformCacheRef = useRef<Map<string, WaveformDataPoint[]>>(new Map())
  const analyzeVideoWaveformRef = useRef<(() => Promise<void>) | null>(null)
  const lastTriggeredUrlRef = useRef<string>('')

  // 生成基于视频URL的唯一波形数据
  const generateUniqueWaveform = useCallback(
    (videoUrl: string) => {
      // 创建更复杂的种子算法
      let seed = 0
      let hashSeed = 0

      // 多重哈希算法增加唯一性
      for (let i = 0; i < videoUrl.length; i++) {
        const char = videoUrl.charCodeAt(i)
        seed = (seed * 31 + char) % 1000000
        hashSeed = (hashSeed * 37 + char * (i + 1)) % 1000000
      }

      // 添加视频时长作为额外的种子因子
      seed += Math.floor(duration * 1000)
      hashSeed += Math.floor(duration * 777)

      // 改进的伪随机数生成器（线性同余生成器）
      class SeededRandom {
        private seed: number

        constructor(seed: number) {
          this.seed = seed % 2147483647
          if (this.seed <= 0) this.seed += 2147483646
        }

        next(): number {
          this.seed = (this.seed * 16807) % 2147483647
          return (this.seed - 1) / 2147483646
        }
      }

      const rng1 = new SeededRandom(seed)
      const rng2 = new SeededRandom(hashSeed)
      const rng3 = new SeededRandom(seed + hashSeed)

      // 优化采样密度：根据视频长度合理分配点数
      const sampleCount = Math.min(200, Math.max(60, Math.floor(duration * 4))) // 降低采样密度
      const waveform: WaveformDataPoint[] = []

      // 预定义更自然的波形模式
      const patterns = [
        // 渐强模式 - 模拟音乐渐入
        (progress: number) => 0.2 + Math.pow(progress, 2) * 0.6,
        // 经典高潮模式 - 中间高两端低
        (progress: number) => 0.3 + Math.sin(progress * Math.PI) * 0.5,
        // 双峰模式 - 两个高潮点
        (progress: number) => 0.25 + (Math.sin(progress * Math.PI * 2) + 1) * 0.25,
        // 递减模式 - 开头强后面弱
        (progress: number) => 0.8 - Math.pow(progress, 1.5) * 0.5,
        // 平稳模式 - 相对稳定的强度
        (progress: number) => 0.4 + Math.sin(progress * Math.PI * 1.5) * 0.2
      ]

      // 根据种子选择主要模式
      const primaryPattern = patterns[Math.floor(rng1.next() * patterns.length)]

      // 生成基础波形数据
      const baseWaveform: number[] = []
      for (let i = 0; i < sampleCount; i++) {
        const progress = i / (sampleCount - 1)

        // 主要强度模式
        let intensity = primaryPattern(progress)

        // 添加少量变化（降低噪声）
        const variation = (rng2.next() - 0.5) * 0.15 // 减少随机噪声
        intensity += variation

        // 添加一个低频周期变化（模拟音乐节拍）
        const beatFreq = 1 + rng1.next() * 2 // 1-3的低频率
        intensity += Math.sin(progress * Math.PI * beatFreq) * 0.1

        // 限制范围
        intensity = Math.max(0.1, Math.min(0.9, intensity))
        baseWaveform.push(intensity)
      }

      // 应用平滑滤波，让波形更自然
      const smoothedWaveform = smoothWaveform(baseWaveform, 3)

      // 添加一些关键点（高潮/低谷）
      const keyPoints = Math.floor(2 + rng3.next() * 4) // 2-5个关键点
      for (let k = 0; k < keyPoints; k++) {
        const position = Math.floor(rng1.next() * sampleCount)
        const isHigh = rng2.next() > 0.5
        const influence = 0.3 + rng3.next() * 0.4 // 影响强度

        // 在关键点周围应用影响
        const radius = Math.floor(sampleCount * 0.05) // 影响半径为5%
        for (let r = -radius; r <= radius; r++) {
          const idx = position + r
          if (idx >= 0 && idx < sampleCount) {
            const distance = Math.abs(r) / radius
            const factor = Math.cos((distance * Math.PI) / 2) // 余弦衰减

            if (isHigh) {
              smoothedWaveform[idx] = Math.min(0.9, smoothedWaveform[idx] + influence * factor)
            } else {
              smoothedWaveform[idx] = Math.max(
                0.1,
                smoothedWaveform[idx] - influence * factor * 0.5
              )
            }
          }
        }
      }

      // 转换为最终数据格式
      for (let i = 0; i < sampleCount; i++) {
        const timestamp = (i / (sampleCount - 1)) * duration
        waveform.push({
          intensity: smoothedWaveform[i],
          timestamp
        })
      }

      return waveform
    },
    [duration]
  )

  // 平滑滤波方法
  const smoothWaveform = useCallback((data: number[], windowSize: number): number[] => {
    if (data.length === 0) return []

    const smoothed = [...data]
    const halfWindow = Math.floor(windowSize / 2)

    for (let i = halfWindow; i < data.length - halfWindow; i++) {
      let sum = 0
      let count = 0

      for (let j = -halfWindow; j <= halfWindow; j++) {
        const idx = i + j
        if (idx >= 0 && idx < data.length) {
          sum += data[idx]
          count++
        }
      }

      smoothed[i] = sum / count
    }

    return smoothed
  }, [])

  // 检查是否有本地缓存的视频文件
  const checkLocalVideoFile = useCallback(async (videoUrl: string): Promise<string | null> => {
    try {
      // 检查是否是本地文件路径（已缓存的视频）
      if (
        videoUrl.startsWith('capacitor://') ||
        videoUrl.startsWith('file://') ||
        videoUrl.startsWith('blob:')
      ) {
        return videoUrl // 已经是本地文件
      }

      // 检查 VideoStorageService 中是否有缓存
      const { VideoStorageService } = await import('@/services/video-storage')
      const cachedVideo = await VideoStorageService.isVideoCached(videoUrl)
      if (cachedVideo && cachedVideo.webPath) {
        console.log(`📁 发现本地缓存视频，将用于波形分析: ${cachedVideo.webPath}`)
        return cachedVideo.webPath
      }

      return null // 没有本地缓存
    } catch (error) {
      console.warn('检查本地视频文件失败:', error)
      return null
    }
  }, [])

  // 真实视频音频波形分析（仅对本地文件）
  const analyzeRealVideoAudio = useCallback(
    async (videoUrl: string, duration: number): Promise<WaveformDataPoint[] | null> => {
      try {
        // 首先检查是否有本地文件
        const localPath = await checkLocalVideoFile(videoUrl)
        if (!localPath) {
          console.log(`🌐 视频未缓存，跳过真实分析，使用伪随机波形: ${videoUrl}`)
          return null // 没有本地文件，返回null让调用方使用伪随机波形
        }

        console.log(`🎵 开始分析本地视频音频: ${localPath}`)

        // 创建音频上下文进行分析
        const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()

        // 使用本地文件进行分析
        try {
          const response = await fetch(localPath)
          const arrayBuffer = await response.arrayBuffer()
          const audioBuffer = await audioContext.decodeAudioData(arrayBuffer)

          // 如果成功解码，处理音频数据
          const channelData = audioBuffer.getChannelData(0)
          const sampleCount = 200
          const samplesPerPoint = Math.floor(channelData.length / sampleCount)
          const waveformData: WaveformDataPoint[] = []

          for (let i = 0; i < sampleCount; i++) {
            const startSample = i * samplesPerPoint
            const endSample = Math.min(startSample + samplesPerPoint, channelData.length)

            let sum = 0
            for (let j = startSample; j < endSample; j++) {
              sum += channelData[j] * channelData[j]
            }
            const rms = Math.sqrt(sum / (endSample - startSample))
            const intensity = Math.min(1, rms * 5)

            waveformData.push({
              intensity,
              timestamp: (i / sampleCount) * duration
            })
          }

          console.log(`✅ 本地视频音频分析完成，数据点: ${waveformData.length}`)
          return waveformData
        } catch (error) {
          console.warn('本地视频音频解码失败，使用伪随机波形:', error)
          return null
        }
      } catch (error) {
        console.warn('真实音频分析失败，使用伪随机波形:', error)
        return null
      }
    },
    [checkLocalVideoFile]
  )

  // 智能波形分析 - 优先伪随机，本地缓存时使用真实分析
  const analyzeVideoWaveform = useCallback(async () => {
    if (!videoElement || !duration) return

    const currentUrl = videoElement.src

    // 检查缓存
    const cached = waveformCacheRef.current.get(currentUrl)
    if (cached) {
      setWaveformData(cached)
      return
    }

    // 检查是否正在分析中
    if (isAnalyzingRef.current) return

    isAnalyzingRef.current = true
    forceUpdate({}) // 强制重新渲染以显示加载状态

    try {
      // 策略：首先立即生成伪随机波形，保证用户体验
      const pseudoWaveform = generateUniqueWaveform(currentUrl)
      waveformCacheRef.current.set(currentUrl, pseudoWaveform)
      setWaveformData(pseudoWaveform)

      console.log(`🎨 使用伪随机波形: ${currentUrl}`)

      // 然后在后台检查是否有本地缓存，如果有则升级为真实分析
      setTimeout(async () => {
        try {
          const realWaveform = await analyzeRealVideoAudio(currentUrl, duration)
          if (realWaveform && realWaveform.length > 0) {
            console.log(`🔄 升级为真实波形分析: ${currentUrl}`)
            // 更新缓存和显示
            waveformCacheRef.current.set(currentUrl, realWaveform)
            setWaveformData(realWaveform)
          }
        } catch (error) {
          console.warn('后台真实分析失败，保持伪随机波形:', error)
        }
      }, 100) // 延迟100ms，让伪随机波形先显示
    } catch (error) {
      // 如果连伪随机波形都失败了，使用默认波形
      console.error('波形生成失败:', error)
      const defaultWaveform = generateUniqueWaveform('default')
      waveformCacheRef.current.set(currentUrl, defaultWaveform)
      setWaveformData(defaultWaveform)
    } finally {
      isAnalyzingRef.current = false
      forceUpdate({}) // 强制重新渲染以隐藏加载状态
    }
  }, [videoElement, duration, generateUniqueWaveform, analyzeRealVideoAudio])

  // 将分析函数存储到 ref 中，避免依赖循环
  analyzeVideoWaveformRef.current = analyzeVideoWaveform

  // 断开音频源连接（现在不需要了，因为不使用媒体元素源）
  const disconnectAudioSource = useCallback(() => {
    // 不再需要断开连接，因为我们不使用媒体元素源
  }, [])

  // 组件初始化时确保状态正确
  useEffect(() => {
    isAnalyzingRef.current = false
  }, [])

  // 确保组件初始化时状态正确
  useEffect(() => {
    if (!videoElement) {
      isAnalyzingRef.current = false
    }
  }, [videoElement])

  // 组件卸载时清理状态
  useEffect(() => {
    return () => {
      disconnectAudioSource()
      isAnalyzingRef.current = false
      analyzedUrlRef.current = ''
    }
  }, [disconnectAudioSource])

  // 根据强度获取颜色
  const getIntensityColor = useCallback((intensity: number): string => {
    if (intensity >= 0.8) return '#ff2d97' // 高强度 - 粉色
    if (intensity >= 0.6) return '#892fff' // 中高强度 - 紫色
    if (intensity >= 0.4) return '#00d4ff' // 中强度 - 蓝色
    return '#4a5568' // 低强度 - 灰色
  }, [])

  // 绘制波形（连续线条样式）
  const drawWaveform = useCallback(() => {
    const canvas = canvasRef.current
    if (!canvas || waveformData.length === 0) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    const { width, height: canvasHeight } = canvas
    ctx.clearRect(0, 0, width, canvasHeight)

    // 绘制背景渐变
    const backgroundGradient = ctx.createLinearGradient(0, 0, 0, canvasHeight)
    backgroundGradient.addColorStop(0, 'rgba(17, 17, 27, 0.8)')
    backgroundGradient.addColorStop(0.5, 'rgba(17, 17, 27, 0.5)')
    backgroundGradient.addColorStop(1, 'rgba(17, 17, 27, 0.3)')
    ctx.fillStyle = backgroundGradient
    ctx.fillRect(0, 0, width, canvasHeight)

    if (waveformData.length < 2) return

    const progressRatio = duration > 0 ? currentTime / duration : 0
    const progressIndex = Math.floor(progressRatio * waveformData.length)

    // 绘制已播放部分（彩色）
    if (progressIndex > 0) {
      ctx.lineWidth = 3
      ctx.lineCap = 'round'
      ctx.lineJoin = 'round'

      for (let i = 1; i < Math.min(progressIndex, waveformData.length); i++) {
        const prevPoint = waveformData[i - 1]
        const currentPoint = waveformData[i]

        const prevX = ((i - 1) / (waveformData.length - 1)) * width
        const prevY = canvasHeight - prevPoint.intensity * canvasHeight * 0.8
        const currentX = (i / (waveformData.length - 1)) * width
        const currentY = canvasHeight - currentPoint.intensity * canvasHeight * 0.8

        // 根据强度获取颜色
        const color = getIntensityColor(currentPoint.intensity)

        // 创建渐变线条
        const lineGradient = ctx.createLinearGradient(prevX, prevY, currentX, currentY)
        lineGradient.addColorStop(0, getIntensityColor(prevPoint.intensity))
        lineGradient.addColorStop(1, color)

        // 绘制主线条
        ctx.strokeStyle = lineGradient
        ctx.lineWidth = 2.5
        ctx.globalAlpha = 0.9

        ctx.beginPath()
        ctx.moveTo(prevX, prevY)
        ctx.lineTo(currentX, currentY)
        ctx.stroke()

        // 高强度时添加发光效果
        if (currentPoint.intensity > 0.6) {
          ctx.strokeStyle = color
          ctx.lineWidth = 5
          ctx.globalAlpha = 0.3
          ctx.filter = 'blur(2px)'

          ctx.beginPath()
          ctx.moveTo(prevX, prevY)
          ctx.lineTo(currentX, currentY)
          ctx.stroke()

          ctx.filter = 'none'
        }

        // 添加数据点脉冲效果
        if (currentPoint.intensity > 0.7 && i % 8 === 0) {
          const time = Date.now() * 0.003
          const pulseRadius = 2 + Math.sin(time + i * 0.5) * 1
          const pulseOpacity = 0.5 + Math.sin(time * 2 + i * 0.3) * 0.3

          ctx.globalAlpha = pulseOpacity
          ctx.fillStyle = color

          ctx.beginPath()
          ctx.arc(currentX, currentY, pulseRadius, 0, Math.PI * 2)
          ctx.fill()
        }
      }
    }

    // 绘制未播放部分（灰色半透明）
    if (progressIndex < waveformData.length - 1) {
      ctx.lineWidth = 2
      ctx.strokeStyle = 'rgba(255, 255, 255, 0.2)'
      ctx.globalAlpha = 0.4

      ctx.beginPath()
      for (let i = Math.max(1, progressIndex); i < waveformData.length; i++) {
        const point = waveformData[i]
        const x = (i / (waveformData.length - 1)) * width
        const y = canvasHeight - point.intensity * canvasHeight * 0.8

        if (i === Math.max(1, progressIndex)) {
          ctx.moveTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      }
      ctx.stroke()
    }

    // 绘制进度指示器
    const progressX = progressRatio * width
    if (progressX > 0) {
      // 进度线
      ctx.strokeStyle = '#ffffff'
      ctx.lineWidth = 2
      ctx.globalAlpha = 0.8
      ctx.setLineDash([4, 4])

      ctx.beginPath()
      ctx.moveTo(progressX, 0)
      ctx.lineTo(progressX, canvasHeight)
      ctx.stroke()

      ctx.setLineDash([])

      // 进度点
      if (progressIndex < waveformData.length) {
        const currentPoint = waveformData[progressIndex]
        if (currentPoint) {
          const y = canvasHeight - currentPoint.intensity * canvasHeight * 0.8

          ctx.fillStyle = '#ffffff'
          ctx.globalAlpha = 1
          ctx.beginPath()
          ctx.arc(progressX, y, 4, 0, Math.PI * 2)
          ctx.fill()

          // 外圈光晕
          ctx.fillStyle = getIntensityColor(currentPoint.intensity)
          ctx.globalAlpha = 0.6
          ctx.beginPath()
          ctx.arc(progressX, y, 6, 0, Math.PI * 2)
          ctx.fill()
        }
      }
    }

    ctx.globalAlpha = 1
  }, [waveformData, currentTime, duration, getIntensityColor])

  // 处理点击事件
  const handleClick = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation()
      e.preventDefault()

      if (justFinishedDragging) {
        setJustFinishedDragging(false)
        return
      }

      const canvas = canvasRef.current
      if (!canvas || duration <= 0) return

      const rect = canvas.getBoundingClientRect()
      const x = e.clientX - rect.left
      const ratio = x / rect.width
      const newTime = ratio * duration

      onSeek(Math.max(0, Math.min(duration, newTime)))
    },
    [duration, onSeek, justFinishedDragging]
  )

  // 处理拖拽
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    setIsDragging(true)
    setJustFinishedDragging(false)
  }, [])

  const handleGlobalMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isDragging || !containerRef.current || duration <= 0) return

      const rect = containerRef.current.getBoundingClientRect()
      const x = e.clientX - rect.left
      const ratio = Math.max(0, Math.min(1, x / rect.width))
      const newTime = ratio * duration

      onSeek(newTime)
    },
    [isDragging, duration, onSeek]
  )

  const handleGlobalMouseUp = useCallback(() => {
    if (isDragging) {
      setIsDragging(false)
      setTimeout(() => setJustFinishedDragging(false), 100)
      setJustFinishedDragging(true)
    }
  }, [isDragging])

  // 触摸事件处理
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    e.preventDefault()
    setIsDragging(true)
    setJustFinishedDragging(false)
  }, [])

  const handleGlobalTouchMove = useCallback(
    (e: TouchEvent) => {
      if (!isDragging || !containerRef.current || duration <= 0) return

      const touch = e.touches[0]
      if (!touch) return

      const rect = containerRef.current.getBoundingClientRect()
      const x = touch.clientX - rect.left
      const ratio = Math.max(0, Math.min(1, x / rect.width))
      const newTime = ratio * duration

      onSeek(newTime)
    },
    [isDragging, duration, onSeek]
  )

  const handleGlobalTouchEnd = useCallback(() => {
    if (isDragging) {
      setIsDragging(false)
      setTimeout(() => setJustFinishedDragging(false), 100)
      setJustFinishedDragging(true)
    }
  }, [isDragging])

  // 全局事件监听
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleGlobalMouseMove)
      document.addEventListener('mouseup', handleGlobalMouseUp)
      document.addEventListener('touchmove', handleGlobalTouchMove, { passive: false })
      document.addEventListener('touchend', handleGlobalTouchEnd)
    }

    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove)
      document.removeEventListener('mouseup', handleGlobalMouseUp)
      document.removeEventListener('touchmove', handleGlobalTouchMove)
      document.removeEventListener('touchend', handleGlobalTouchEnd)
    }
  }, [
    isDragging,
    handleGlobalMouseMove,
    handleGlobalMouseUp,
    handleGlobalTouchMove,
    handleGlobalTouchEnd
  ])

  // 重置分析状态当URL变化时
  useEffect(() => {
    if (videoElement && analyzedUrlRef.current !== videoElement.src) {
      // 断开之前的音频源连接
      disconnectAudioSource()

      // 重置所有分析相关状态
      analyzedUrlRef.current = videoElement.src
      isAnalyzingRef.current = false
      lastTriggeredUrlRef.current = '' // 重置触发状态
      setWaveformData([])

      // 清除对应的缓存
      waveformCacheRef.current.delete(videoElement.src)
    }
  }, [videoElement?.src, disconnectAudioSource])

  // 初始化波形分析 - 使用更稳定的触发机制
  useEffect(() => {
    if (videoElement && duration > 0) {
      const currentUrl = videoElement.src

      // 只有当缓存中没有数据、没有正在分析、且没有触发过这个URL时才触发
      if (
        !waveformCacheRef.current.has(currentUrl) &&
        !isAnalyzingRef.current &&
        lastTriggeredUrlRef.current !== currentUrl
      ) {
        lastTriggeredUrlRef.current = currentUrl
        analyzeVideoWaveformRef.current?.()
      }
    }
  }, [videoElement?.src, duration]) // 使用 ref 调用，避免依赖循环

  // 重绘波形
  useEffect(() => {
    drawWaveform()
  }, [drawWaveform])

  // 格式化时间
  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  return (
    <div className={`relative ${className}`}>
      {/* 波形画布容器 */}
      <div
        ref={containerRef}
        className="relative cursor-pointer select-none"
        style={{
          padding: '10px 0',
          margin: '-10px 0',
          height: height + 20
        }}
        onClick={handleClick}
        onMouseDown={handleMouseDown}
        onTouchStart={handleTouchStart}
      >
        <canvas
          ref={canvasRef}
          width={800}
          height={height}
          className="w-full rounded-lg bg-white/5"
          style={{ height }}
        />

        {/* 加载状态 */}
        {isAnalyzingRef.current && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-lg">
            <div className="text-white/60 text-sm flex items-center gap-2">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                className="w-4 h-4 border-2 border-white/40 border-t-white/80 rounded-full"
              />
              分析视频音频中...
            </div>
          </div>
        )}
      </div>

      {/* 时间显示 */}
      <div className="flex justify-between text-xs text-white/60 mt-2 relative -bottom-0">
        <span>{formatTime(currentTime)}</span>
        <span>{formatTime(duration)}</span>
      </div>
    </div>
  )
}
