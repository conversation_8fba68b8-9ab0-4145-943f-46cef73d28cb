import React, { useRef, useCallback, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Icon } from '@iconify/react'

interface VideoGestureHandlerProps {
  children: React.ReactNode
  currentTime: number
  duration: number
  isPlaying: boolean
  onSeek: (time: number) => void
  onPlaybackRateChange: (rate: number) => void
  onTogglePlay?: () => void
  className?: string
}

export const VideoGestureHandler: React.FC<VideoGestureHandlerProps> = ({
  children,
  currentTime,
  duration,
  isPlaying,
  onSeek,
  onPlaybackRateChange,
  onTogglePlay,
  className = ''
}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const touchStartRef = useRef<{ x: number; y: number; time: number } | null>(null)
  const longPressTimerRef = useRef<NodeJS.Timeout | null>(null)
  const lastTapRef = useRef<{ x: number; time: number } | null>(null)

  const [isDraggingProgress, setIsDraggingProgress] = useState(false)
  const [showSpeedIndicator, setShowSpeedIndicator] = useState(false)
  const [showSeekIndicator, setShowSeekIndicator] = useState<{
    type: 'forward' | 'backward' | 'play-pause'
    amount: number
  } | null>(null)
  const [isLongPressing, setIsLongPressing] = useState(false)

  // 双击快进/快退/播放暂停处理
  const handleDoubleTap = useCallback(
    (x: number) => {
      if (!containerRef.current || duration <= 0) return

      const rect = containerRef.current.getBoundingClientRect()
      const leftThird = rect.width / 3
      const rightThird = (rect.width * 2) / 3

      // 左侧区域：快退15秒
      if (x < leftThird) {
        const seekAmount = 15
        let newTime = Math.max(0, currentTime - seekAmount)

        // 边界检查
        if (newTime < 0) newTime = 0
        if (Math.abs(newTime - currentTime) < 0.1) return

        onSeek(newTime)

        const actualSeekAmount = Math.abs(newTime - currentTime)
        setShowSeekIndicator({
          type: 'backward',
          amount: Math.round(actualSeekAmount)
        })

        setTimeout(() => setShowSeekIndicator(null), 1000)
      }
      // 右侧区域：快进15秒
      else if (x > rightThird) {
        const seekAmount = 15
        let newTime = Math.min(duration, currentTime + seekAmount)

        // 边界检查
        if (newTime > duration) newTime = duration
        if (Math.abs(newTime - currentTime) < 0.1) return

        onSeek(newTime)

        const actualSeekAmount = Math.abs(newTime - currentTime)
        setShowSeekIndicator({
          type: 'forward',
          amount: Math.round(actualSeekAmount)
        })

        setTimeout(() => setShowSeekIndicator(null), 1000)
      }
      // 中间区域：播放/暂停
      else {
        if (onTogglePlay) {
          // 显示播放/暂停提示（显示切换后的状态）
          setShowSeekIndicator({
            type: 'play-pause',
            amount: isPlaying ? 0 : 1 // 0表示暂停，1表示播放
          })

          onTogglePlay()

          setTimeout(() => setShowSeekIndicator(null), 800)
        }
      }
    },
    [currentTime, duration, isPlaying, onSeek, onTogglePlay]
  )

  // 长按倍速播放处理
  const handleLongPressStart = useCallback(() => {
    // 只有在有效视频时才允许倍速播放
    if (duration <= 0 || currentTime >= duration) return

    setIsLongPressing(true)
    setShowSpeedIndicator(true)
    onPlaybackRateChange(2) // 2倍速
  }, [onPlaybackRateChange, duration, currentTime])

  const handleLongPressEnd = useCallback(() => {
    setIsLongPressing(false)
    setShowSpeedIndicator(false)
    onPlaybackRateChange(1) // 恢复正常速度
  }, [onPlaybackRateChange])

  // 水平拖拽进度条处理
  const handleProgressDrag = useCallback(
    (startX: number, currentX: number) => {
      if (!containerRef.current || duration <= 0) return

      const rect = containerRef.current.getBoundingClientRect()
      const deltaX = currentX - startX

      // 根据视频长度动态调整敏感度
      // 短视频（<60s）：降低敏感度，长视频（>600s）：提高敏感度
      let sensitivity = 1
      if (duration < 60) {
        sensitivity = 0.3 // 短视频降低敏感度
      } else if (duration < 300) {
        sensitivity = 0.6 // 中等长度视频
      } else if (duration > 600) {
        sensitivity = 1.5 // 长视频提高敏感度
      }

      const deltaRatio = (deltaX / rect.width) * sensitivity
      const deltaTime = deltaRatio * duration

      const newTime = Math.max(0, Math.min(duration, currentTime + deltaTime))
      onSeek(newTime)
    },
    [currentTime, duration, onSeek]
  )

  // 触摸开始处理
  const handleTouchStart = useCallback(
    (e: React.TouchEvent) => {
      const touch = e.touches[0]
      if (!touch || duration <= 0) return

      const touchInfo = {
        x: touch.clientX,
        y: touch.clientY,
        time: Date.now()
      }

      touchStartRef.current = touchInfo

      // 检查双击
      if (lastTapRef.current) {
        const timeDiff = touchInfo.time - lastTapRef.current.time
        const distanceDiff = Math.abs(touchInfo.x - lastTapRef.current.x)

        if (timeDiff < 300 && distanceDiff < 50) {
          // 双击检测成功
          handleDoubleTap(touchInfo.x)
          lastTapRef.current = null
          return
        }
      }

      lastTapRef.current = { x: touchInfo.x, time: touchInfo.time }

      // 启动长按检测
      longPressTimerRef.current = setTimeout(() => {
        handleLongPressStart()
      }, 500) // 500ms 长按
    },
    [handleDoubleTap, handleLongPressStart]
  )

  // 触摸移动处理
  const handleTouchMove = useCallback(
    (e: React.TouchEvent) => {
      const touch = e.touches[0]
      if (!touch || !touchStartRef.current || duration <= 0) return

      const deltaX = Math.abs(touch.clientX - touchStartRef.current.x)
      const deltaY = Math.abs(touch.clientY - touchStartRef.current.y)

      // 如果移动距离超过阈值，取消长按
      if (deltaX > 20 || deltaY > 20) {
        if (longPressTimerRef.current) {
          clearTimeout(longPressTimerRef.current)
          longPressTimerRef.current = null
        }
      }

      // 水平拖拽检测（进度控制）
      // 增加最小移动距离要求，避免误触发
      const minDragDistance = 40
      if (deltaX > minDragDistance && deltaX > deltaY * 2) {
        if (!isDraggingProgress) {
          setIsDraggingProgress(true)
        }
        handleProgressDrag(touchStartRef.current.x, touch.clientX)
      }
    },
    [isDraggingProgress, handleProgressDrag, duration]
  )

  // 触摸结束处理
  const handleTouchEnd = useCallback(() => {
    // 清理长按定时器
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current)
      longPressTimerRef.current = null
    }

    // 如果正在长按，结束长按
    if (isLongPressing) {
      handleLongPressEnd()
    }

    // 重置状态
    setIsDraggingProgress(false)
    touchStartRef.current = null

    // 清理双击检测
    setTimeout(() => {
      lastTapRef.current = null
    }, 300)
  }, [isLongPressing, handleLongPressEnd])

  return (
    <div
      ref={containerRef}
      className={`relative ${className}`}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      onTouchCancel={handleTouchEnd}
    >
      {children}

      {/* 快进/快退提示 */}
      <AnimatePresence>
        {showSeekIndicator && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="absolute inset-0 flex items-center justify-center pointer-events-none z-30"
          >
            <div
              className="rounded-xl px-4 py-2 flex items-center gap-2 shadow-lg"
              style={{
                background:
                  'linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.5) 100%)',
                backdropFilter: 'blur(15px) saturate(180%)',
                WebkitBackdropFilter: 'blur(15px) saturate(180%)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
              }}
            >
              <Icon
                icon={
                  showSeekIndicator.type === 'forward'
                    ? 'solar:skip-next-bold'
                    : showSeekIndicator.type === 'backward'
                    ? 'solar:skip-previous-bold'
                    : showSeekIndicator.amount === 1
                    ? 'solar:play-bold'
                    : 'solar:pause-bold'
                }
                width={20}
                className="text-white"
              />
              {showSeekIndicator.type === 'play-pause' ? (
                <span className="text-white text-sm font-medium">
                  {showSeekIndicator.amount === 1 ? '播放' : '暂停'}
                </span>
              ) : (
                <span className="text-white text-sm font-medium">
                  {showSeekIndicator.type === 'forward' ? '+' : '-'}
                  {showSeekIndicator.amount}s
                </span>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 倍速播放提示 */}
      <AnimatePresence>
        {showSpeedIndicator && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className="absolute top-8 left-1/2 transform -translate-x-1/2 pointer-events-none z-30"
          >
            <div
              className="rounded-lg px-3 py-1.5 flex items-center gap-2 shadow-lg"
              style={{
                background:
                  'linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.5) 100%)',
                backdropFilter: 'blur(15px) saturate(180%)',
                WebkitBackdropFilter: 'blur(15px) saturate(180%)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
              }}
            >
              <Icon icon="solar:speed-bold" width={16} className="text-white" />
              <span className="text-white text-xs font-medium">2.0x</span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 进度拖拽提示 */}
      <AnimatePresence>
        {isDraggingProgress && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none z-30"
          >
            <div
              className="rounded-lg px-3 py-2 shadow-lg"
              style={{
                background:
                  'linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.5) 100%)',
                backdropFilter: 'blur(15px) saturate(180%)',
                WebkitBackdropFilter: 'blur(15px) saturate(180%)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
              }}
            >
              <div className="text-center">
                <div className="text-white text-sm font-medium">
                  {Math.floor(currentTime / 60)}:
                  {Math.floor(currentTime % 60)
                    .toString()
                    .padStart(2, '0')}
                </div>
                <div className="text-white/60 text-xs">
                  / {Math.floor(duration / 60)}:
                  {Math.floor(duration % 60)
                    .toString()
                    .padStart(2, '0')}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
