import React, { useRef, useEffect, useState, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Icon } from '@iconify/react'
import type { StoredVideoItem } from '@/services/video-storage'
import { useVideoPlayer } from '@/stores/video-player'
import { videoService } from '@/services/video'
import { VideoDrawer } from './VideoDrawer'
import { VideoWaveformProgress } from './VideoWaveformProgress'
import { VideoGestureHandler } from './VideoGestureHandler'
import { useWaveformData } from '@/hooks/useWaveformData'

interface ImmersiveVideoPlayerProps {
  video: StoredVideoItem
  onBack: () => void
  onVideoChange?: (video: StoredVideoItem) => void
}

// 控制栏隐藏时间
const CONTROLS_HIDE_TIME = 5000

export function ImmersiveVideoPlayer({ video, onBack, onVideoChange }: ImmersiveVideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const controlsTimeoutRef = useRef<NodeJS.Timeout>()

  const {
    isPlaying,
    currentTime,
    duration,
    showControls,
    isLoading,
    error,
    setIsPlaying,
    setCurrentTime,
    setDuration,
    setShowControls,
    setIsLoading,
    setError,
    togglePlay
  } = useVideoPlayer()

  const [allVideos, setAllVideos] = useState<StoredVideoItem[]>([])
  const [currentIndex, setCurrentIndex] = useState(0)
  const [showVideoDrawer, setShowVideoDrawer] = useState(false)
  const [playbackRate, setPlaybackRate] = useState(1)

  // 蓝牙广播数据管理 - 统一架构
  const bluetoothWaveform = useWaveformData({
    mode: 'video',
    maxPoints: 400
  })

  // 移除组件层重复的频率控制，统一由策略层管理

  // 加载所有视频
  useEffect(() => {
    const loadVideos = async () => {
      try {
        const videos = await videoService.getAllVideos()
        setAllVideos(videos)
        const index = videos.findIndex(v => v.id === video.id)
        setCurrentIndex(index >= 0 ? index : 0)
      } catch (err) {
        console.error('加载视频列表失败:', err)
      }
    }
    loadVideos()
  }, [video.id])

  // 切换到下一个视频
  const handleNext = useCallback(() => {
    if (allVideos.length === 0 || !onVideoChange) return
    const nextIndex = (currentIndex + 1) % allVideos.length
    const nextVideo = allVideos[nextIndex]
    if (nextVideo) {
      setCurrentIndex(nextIndex)
      onVideoChange(nextVideo)
    }
  }, [allVideos, currentIndex, onVideoChange])

  // 切换到上一个视频
  const handlePrevious = useCallback(() => {
    if (allVideos.length === 0 || !onVideoChange) return
    const prevIndex = currentIndex === 0 ? allVideos.length - 1 : currentIndex - 1
    const prevVideo = allVideos[prevIndex]
    if (prevVideo) {
      setCurrentIndex(prevIndex)
      onVideoChange(prevVideo)
    }
  }, [allVideos, currentIndex, onVideoChange])

  // 视频切换时重置状态
  useEffect(() => {
    console.log(`🔄 视频切换: ${video.title} (${video.url})`)

    // 重置播放状态
    setCurrentTime(0)
    setIsLoading(true)
    setError(null)
    setPlaybackRate(1)
    setIsPlaying(false) // 强制设置为暂停状态

    // 重置视频元素状态
    const videoElement = videoRef.current
    if (videoElement) {
      // 先移除所有事件监听器，避免状态冲突
      videoElement.pause() // 强制暂停视频
      videoElement.currentTime = 0
      videoElement.playbackRate = 1

      // 清除可能的错误状态
      videoElement.load() // 重新加载视频源
    }
  }, [video.id, video.url, setCurrentTime, setIsLoading, setError, setIsPlaying])

  // 视频事件处理
  useEffect(() => {
    const videoElement = videoRef.current
    if (!videoElement) return

    const handleLoadStart = () => {
      setIsLoading(true)
      setError(null)
    }
    const handleLoadedData = () => {
      console.log(`📺 视频加载完成: ${video.title}`)
      setIsLoading(false)
      setDuration(videoElement.duration || 0)
      setError(null)

      // 确保视频处于暂停状态
      if (!videoElement.paused) {
        console.log('🛑 强制暂停意外播放的视频')
        videoElement.pause()
      }
      setIsPlaying(false)
    }
    const handleTimeUpdate = () => {
      setCurrentTime(videoElement.currentTime)
    }
    const handlePlay = () => {
      // 确保状态同步
      setIsPlaying(true)
    }
    const handlePause = () => {
      // 确保状态同步
      setIsPlaying(false)
    }
    const handleError = () => {
      setIsLoading(false)
      setError('视频加载失败')
      setIsPlaying(false)
    }
    const handleEnded = () => {
      setIsPlaying(false)
      setCurrentTime(duration)
      // 自动播放下一个视频
      handleNext()
    }

    videoElement.addEventListener('loadstart', handleLoadStart)
    videoElement.addEventListener('loadeddata', handleLoadedData)
    videoElement.addEventListener('timeupdate', handleTimeUpdate)
    videoElement.addEventListener('play', handlePlay)
    videoElement.addEventListener('pause', handlePause)
    videoElement.addEventListener('error', handleError)
    videoElement.addEventListener('ended', handleEnded)

    return () => {
      videoElement.removeEventListener('loadstart', handleLoadStart)
      videoElement.removeEventListener('loadeddata', handleLoadedData)
      videoElement.removeEventListener('timeupdate', handleTimeUpdate)
      videoElement.removeEventListener('play', handlePlay)
      videoElement.removeEventListener('pause', handlePause)
      videoElement.removeEventListener('error', handleError)
      videoElement.removeEventListener('ended', handleEnded)
    }
  }, [setIsLoading, setDuration, setCurrentTime, setIsPlaying, setError, handleNext])

  // 同步播放状态（带防抖和错误处理）
  useEffect(() => {
    const videoElement = videoRef.current
    if (!videoElement) return

    // 防止状态循环：检查实际状态是否与期望状态一致
    const actualPlaying = !videoElement.paused
    if (actualPlaying === isPlaying) return

    console.log(`🎮 状态同步: 期望=${isPlaying}, 实际=${actualPlaying}`)

    // 添加小延迟避免快速状态切换
    const syncTimeout = setTimeout(() => {
      if (isPlaying) {
        console.log('▶️ 开始播放视频')
        videoElement.play().catch(error => {
          console.error('❌ 视频播放失败:', error)
          setIsPlaying(false)
          setError(`播放失败: ${error.message || '未知错误'}`)
        })
      } else {
        console.log('⏸️ 暂停视频')
        videoElement.pause()
      }
    }, 50) // 50ms防抖

    return () => clearTimeout(syncTimeout)
  }, [isPlaying, setIsPlaying, setError])

  // 同步音量
  // 同步播放速度
  useEffect(() => {
    const videoElement = videoRef.current
    if (!videoElement) return

    videoElement.playbackRate = playbackRate
  }, [playbackRate])

  // 视频强度计算和蓝牙广播 - 统一架构
  useEffect(() => {
    if (!isPlaying) {
      // 播放停止时，立即发送强度为0的数据点
      bluetoothWaveform.addDataPoint(0)
      return
    }

    const interval = setInterval(() => {
      // 基于视频内容的简化强度计算
      // 使用播放时间和伪随机算法生成强度，模拟视频内容变化
      const timeBasedSeed = Math.floor(currentTime * 10) // 每100ms变化一次
      const pseudoRandom = Math.sin(timeBasedSeed * 0.1) * Math.cos(timeBasedSeed * 0.07)
      const baseIntensity = Math.abs(pseudoRandom) * 9 + 1 // 1-9的强度范围
      const finalIntensity = Math.round(baseIntensity)

      // 直接发送蓝牙广播，频率控制交给策略层
      bluetoothWaveform.addDataPoint(finalIntensity)
    }, 200) // 与音乐模式保持一致的更新频率

    return () => clearInterval(interval)
  }, [isPlaying, currentTime, bluetoothWaveform])

  // 自动隐藏控制栏 - 暂停时保持显示
  useEffect(() => {
    // 清除之前的定时器
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current)
    }

    // 只有在显示控制栏且正在播放时才设置自动隐藏
    if (showControls && isPlaying) {
      controlsTimeoutRef.current = setTimeout(() => {
        setShowControls(false)
      }, CONTROLS_HIDE_TIME)
    }

    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current)
      }
    }
  }, [showControls, isPlaying, setShowControls])

  // 播放状态改变时显示控制栏
  useEffect(() => {
    // 当暂停时，立即显示控制栏
    if (!isPlaying) {
      setShowControls(true)
    }
  }, [isPlaying, setShowControls])

  // 处理点击显示/隐藏控制栏
  const handleVideoClick = () => {
    setShowControls(!showControls)
  }

  // 处理双击播放/暂停
  const handleDoubleClick = () => {
    togglePlay()
  }

  // 处理手势跳转
  const handleGestureSeek = useCallback(
    (time: number) => {
      const videoElement = videoRef.current
      if (!videoElement || isNaN(time) || time < 0) return

      // 确保时间在有效范围内
      const clampedTime = Math.max(0, Math.min(duration || 0, time))

      setCurrentTime(clampedTime)
      try {
        videoElement.currentTime = clampedTime
      } catch (error) {
        console.warn('设置视频时间失败:', error)
      }
    },
    [setCurrentTime, duration]
  )

  // 处理播放速度变化
  const handlePlaybackRateChange = useCallback((rate: number) => {
    // 边界检查：播放速度应该在合理范围内
    if (isNaN(rate) || rate <= 0 || rate > 4) return

    setPlaybackRate(rate)
  }, [])

  // 组件卸载时清理蓝牙广播
  useEffect(() => {
    return () => {
      // 组件卸载时立即发送强度为0，停止设备（不受频率限制）
      bluetoothWaveform.addDataPoint(0)
    }
  }, [bluetoothWaveform])

  // 蓝牙状态监控和重连
  useEffect(() => {
    const checkBluetoothStatus = async () => {
      if (!bluetoothWaveform.isBluetoothReady()) {
        console.warn('视频模式：蓝牙未准备就绪，尝试重新初始化...')
        try {
          await bluetoothWaveform.reinitializeBluetooth()
          console.log('视频模式：蓝牙重新初始化成功')
        } catch (error) {
          console.error('视频模式：蓝牙重新初始化失败:', error)
        }
      }
    }

    // 延迟检查蓝牙状态，给蓝牙初始化时间
    const timer = setTimeout(checkBluetoothStatus, 1000)

    return () => clearTimeout(timer)
  }, [bluetoothWaveform])

  return (
    <VideoGestureHandler
      currentTime={currentTime}
      duration={duration}
      isPlaying={isPlaying}
      onSeek={handleGestureSeek}
      onPlaybackRateChange={handlePlaybackRateChange}
      onTogglePlay={togglePlay}
      className="w-screen h-screen"
    >
      <div
        ref={containerRef}
        className="relative w-full h-full bg-black overflow-hidden"
        onClick={handleVideoClick}
        onDoubleClick={handleDoubleClick}
      >
        {/* 视频元素 */}
        <video
          ref={videoRef}
          src={`${video.url}#t=0.001`}
          poster=""
          className="w-full h-full object-contain bg-black [&::-webkit-media-controls]:hidden [&::-webkit-media-controls-panel]:hidden [&::-webkit-media-controls-play-button]:hidden [&::-webkit-media-controls-start-playback-button]:hidden [&::-webkit-media-controls-overlay-play-button]:hidden"
          playsInline
          preload="metadata"
          controls={false}
          disablePictureInPicture
          disableRemotePlayback
          x-webkit-airplay="deny"
          // 添加视频格式兼容性属性
          crossOrigin="anonymous"
          style={
            {
              backgroundColor: 'black',
              // 尝试隐藏默认播放按钮的多种方法
              WebkitMediaControlsOverlayPlayButton: 'none',
              WebkitMediaControlsStartPlaybackButton: 'none'
            } as any
          }
          onError={async e => {
            const error = e.currentTarget.error
            let errorMessage = `视频加载失败: ${video.title}`

            if (error) {
              switch (error.code) {
                case error.MEDIA_ERR_ABORTED:
                  errorMessage += ' (播放被中止)'
                  break
                case error.MEDIA_ERR_NETWORK:
                  errorMessage += ' (网络错误)'
                  break
                case error.MEDIA_ERR_DECODE:
                  errorMessage += ' (解码错误，可能是格式不支持)'
                  break
                case error.MEDIA_ERR_SRC_NOT_SUPPORTED:
                  errorMessage += ' (视频格式不支持)'
                  break
                default:
                  errorMessage += ` (错误代码: ${error.code})`
              }
            }

            console.error('🎬 视频播放错误:', errorMessage, error)

            // 如果是缓存视频播放失败，尝试使用原始URL
            if ('isLocal' in video && video.isLocal && video.isOnline) {
              console.log('🔄 缓存视频播放失败，尝试使用原始URL')
              // 这里可以触发回退到原始URL的逻辑
              // 但为了避免无限循环，暂时只记录错误
            }

            setError(errorMessage)
          }}
          onLoadStart={() => {
            console.log('🔄 开始加载视频:', video.title)
            setIsLoading(true)
          }}
          onCanPlay={() => {
            console.log('✅ 视频可以播放:', video.title)
            setIsLoading(false)
          }}
          onWaiting={() => {
            console.log('⏳ 视频缓冲中...')
            setIsLoading(true)
          }}
          onCanPlayThrough={() => {
            console.log('🚀 视频完全加载完成')
            setIsLoading(false)
          }}
        />

        {/* 加载状态 */}
        <AnimatePresence>
          {isLoading && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="absolute inset-0 flex items-center justify-center bg-black/50 z-10"
            >
              <div className="flex flex-col items-center gap-4">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                  className="w-12 h-12 border-4 border-[#892fff] border-t-transparent rounded-full"
                />
                <p className="text-white/80 text-lg">视频加载中...</p>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* 错误状态 */}
        <AnimatePresence>
          {error && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="absolute inset-0 flex items-center justify-center bg-black/80 z-10"
            >
              <div className="flex flex-col items-center gap-4 text-center px-6">
                <Icon icon="solar:videocamera-broken" width={64} className="text-red-400" />
                <p className="text-white text-lg">{error}</p>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => window.location.reload()}
                  className="px-6 py-3 bg-[#892fff] text-white rounded-full hover:bg-[#892fff]/80 transition-colors"
                >
                  重新加载
                </motion.button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* 控制栏 */}
        {/* 顶部控制栏 */}
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: showControls && !error ? 1 : 0, y: showControls && !error ? 0 : -50 }}
          className="absolute top-0 left-0 right-0 z-20 bg-gradient-to-b from-black/60 to-transparent backdrop-blur-md py-6 px-4"
          style={{ pointerEvents: showControls && !error ? 'auto' : 'none' }}
        >
          <div className="flex items-center justify-between">
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={e => {
                e.stopPropagation()
                onBack()
              }}
              className="w-10 h-10 flex items-center justify-center text-white"
            >
              <Icon icon="solar:arrow-left-linear" width={24} />
            </motion.button>

            <div className="flex-1 mx-4 text-center">
              <h3 className="text-white text-lg font-semibold truncate">{video.title}</h3>
            </div>

            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={e => {
                e.stopPropagation()
                setShowVideoDrawer(true)
              }}
              className="w-10 h-10 flex items-center justify-center text-white"
            >
              <Icon icon="solar:playlist-minimalistic-2-linear" width={24} />
            </motion.button>
          </div>
        </motion.div>

        {/* 底部控制栏 */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: showControls && !error ? 1 : 0, y: showControls && !error ? 0 : 50 }}
          className="absolute bottom-0 left-0 right-0 z-20 p-4 pb-8"
          style={{ pointerEvents: showControls && !error ? 'auto' : 'none' }}
        >
          {/* 精美控制面板卡片 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            whileHover={{ scale: 1.01 }}
            onClick={e => e.stopPropagation()}
            className="
              relative overflow-hidden rounded-2xl p-6
              bg-black/20 backdrop-blur-sm border border-white/20
              hover:border-[#892fff]/50 hover:bg-black/30 transition-all duration-300
            "
          >
            {/* 背景波形装饰 */}
            <div className="absolute inset-0 overflow-hidden">
              <motion.div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-[#892fff]/20 to-[#ff2d97]/20 rounded-full blur-xl" />
              <motion.div className="absolute -bottom-10 -left-10 w-24 h-24 bg-gradient-to-tr from-[#00d4ff]/20 to-[#892fff]/20 rounded-full blur-xl" />
            </div>

            {/* 内容区域 */}
            <div className="relative z-10 space-y-4">
              {/* 波形进度条 */}
              <div>
                <VideoWaveformProgress
                  videoElement={videoRef.current}
                  currentTime={currentTime}
                  duration={duration}
                  onSeek={time => {
                    setCurrentTime(time)
                    if (videoRef.current) {
                      videoRef.current.currentTime = time
                    }
                  }}
                  height={50}
                />
              </div>

              {/* 控制按钮 */}
              <div className="flex items-center justify-center gap-8">
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={e => {
                    e.stopPropagation()
                    handlePrevious()
                  }}
                  className="text-white/80 hover:text-white transition-colors"
                >
                  <Icon icon="solar:skip-previous-bold" width={32} />
                </motion.button>

                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={e => {
                    e.stopPropagation()
                    togglePlay()
                  }}
                  className="w-16 h-16 bg-[#892fff] rounded-full flex items-center justify-center text-white hover:bg-[#892fff]/80 transition-colors"
                >
                  <Icon icon={isPlaying ? 'solar:pause-bold' : 'solar:play-bold'} width={24} />
                </motion.button>

                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={e => {
                    e.stopPropagation()
                    handleNext()
                  }}
                  className="text-white/80 hover:text-white transition-colors"
                >
                  <Icon icon="solar:skip-next-bold" width={32} />
                </motion.button>
              </div>
            </div>

            {/* 悬浮效果 */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-[#892fff]/10 to-[#ff2d97]/10 opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none"
              whileHover={{ opacity: 1 }}
            />
          </motion.div>
        </motion.div>

        {/* 视频选择抽屉 */}
        <VideoDrawer
          isOpen={showVideoDrawer}
          onClose={() => setShowVideoDrawer(false)}
          currentVideo={video}
          onVideoSelect={selectedVideo => {
            if (onVideoChange) {
              onVideoChange(selectedVideo)
            }
          }}
        />
      </div>
    </VideoGestureHandler>
  )
}
