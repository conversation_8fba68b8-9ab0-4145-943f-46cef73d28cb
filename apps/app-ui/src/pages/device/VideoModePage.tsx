import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router'
import { useTranslation } from 'react-i18next'
import { Icon } from '@iconify/react'
import { motion } from 'framer-motion'
import type { StoredVideoItem } from '@/services/video-storage'
import { useVideoPlayer } from '@/stores/video-player'
import { videoService } from '@/services/video'
import { ImmersiveVideoPlayer } from '@/components/device/video/ImmersiveVideoPlayer'

export default function VideoModePage() {
  const navigate = useNavigate()
  const { t } = useTranslation('device')

  const { currentVideo, setCurrentVideo, setDeviceConnected } = useVideoPlayer()
  const [isInitialized, setIsInitialized] = useState(false)

  // 初始化数据并自动开始播放
  useEffect(() => {
    const initializeData = async () => {
      try {
        // 加载在线视频数据
        const onlineVideos = await videoService.getOnlineVideos()

        // 自动选择第一个视频开始播放
        if (onlineVideos.length > 0) {
          setCurrentVideo(onlineVideos[0])
        }

        // 后台加载导入视频数据
        videoService.getImportedVideos()

        // 设置设备连接状态
        setDeviceConnected(true)

        // 标记初始化完成
        setIsInitialized(true)
      } catch (error) {
        console.error('初始化视频数据失败:', error)
        setIsInitialized(true) // 即使出错也要标记完成，避免无限加载
      }
    }

    initializeData()
  }, [setCurrentVideo, setDeviceConnected])

  // 处理返回
  const handleBack = () => {
    navigate(-1)
  }

  // 处理视频切换
  const handleVideoChange = async (newVideo: StoredVideoItem) => {
    console.log(
      `🔄 切换视频: ${newVideo.title} (在线: ${newVideo.isOnline}, 本地: ${newVideo.isLocal})`
    )

    // 立即设置视频，不等待缓存完成
    setCurrentVideo(newVideo)

    // 如果是在线视频且未缓存，后台异步缓存（不阻塞播放）
    if (newVideo.isOnline && !newVideo.isLocal) {
      console.log('🌐 后台开始缓存视频:', newVideo.title)

      // 后台异步缓存，不阻塞当前播放
      videoService
        .cacheVideo(newVideo)
        .then(cachedVideo => {
          console.log('✅ 后台缓存完成:', cachedVideo.title)
          // 缓存完成后，如果当前还在播放这个视频，可以选择性地更新为本地版本
          // 但不强制切换，避免打断用户体验
        })
        .catch(error => {
          console.warn('⚠️ 后台缓存失败:', error)
          // 缓存失败不影响当前播放
        })
    } else {
      console.log('📁 使用本地视频或已缓存视频')
    }
  }

  // 加载状态
  if (!isInitialized) {
    return (
      <div className="min-h-screen bg-[#121521] relative overflow-hidden flex items-center justify-center">
        {/* 背景 */}
        <div className="absolute top-0 left-0 w-screen pointer-events-none">
          <img src="/images/device/bg.svg" className="w-full h-full" />
        </div>

        {/* 加载动画 */}
        <div className="relative z-10 flex flex-col items-center gap-4">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
            className="w-12 h-12 border-4 border-[#892fff] border-t-transparent rounded-full"
          />
          <p className="text-white/60 text-lg">正在加载视频...</p>
        </div>
      </div>
    )
  }

  // 如果没有视频，显示空状态
  if (!currentVideo) {
    return (
      <div className="min-h-screen bg-[#121521] relative overflow-hidden flex items-center justify-center">
        {/* 背景 */}
        <div className="absolute top-0 left-0 w-screen pointer-events-none">
          <img src="/images/device/bg.svg" className="w-full h-full" />
        </div>

        {/* 空状态 */}
        <div className="relative z-10 flex flex-col items-center gap-4">
          <Icon icon="solar:videocamera-linear" width={64} className="text-white/40" />
          <p className="text-white/60 text-lg">暂无可播放的视频</p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleBack}
            className="px-6 py-3 bg-[#892fff] text-white rounded-full hover:bg-[#892fff]/80 transition-colors"
          >
            返回
          </motion.button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-[#121521] relative overflow-hidden flex flex-col">
      {/* 背景元素 */}
      <div className="absolute top-0 left-0 w-screen pointer-events-none z-20">
        <img src="/images/device/bg.svg" className="w-full h-full" />
      </div>

      {/* 沉浸式视频播放器 */}
      <div className="relative z-10 flex-1 min-h-0">
        <ImmersiveVideoPlayer
          video={currentVideo}
          onBack={handleBack}
          onVideoChange={handleVideoChange}
        />
      </div>
    </div>
  )
}
