import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router'
import { motion, AnimatePresence } from 'framer-motion'
import { useTranslation } from 'react-i18next'
import { Icon } from '@iconify/react'
import { DeviceConnectionForm } from '../../components/device/DeviceConnectionForm'
import { useDeviceStore } from '../../stores/device-store'
import { modes } from '@/utils/deviceModes'
import { Button } from '@heroui/react'
import { LocalDeviceControlProvider } from '@/contexts/local-device-control-provider'
import { ClassicModeControl } from '@/components/device/classic-mode'

// 定义设备模式配置
const deviceModes = [
  {
    id: 'classic',
    name: '经典模式',
    icon: 'solar:gameboy-bold',
    color: '#ff2d97',
    description: '9种预设强度模式',
    route: null // 在当前页面展示
  },
  {
    id: 'swipe',
    name: '划屏模式',
    icon: 'solar:hand-stars-bold',
    color: '#892fff',
    description: '手指滑动控制',
    route: '/device/swipe-mode'
  },
  {
    id: 'audio',
    name: '音乐模式',
    icon: 'solar:music-note-4-bold',
    color: '#00d4ff',
    description: '音频节拍同步',
    route: '/device/audio-mode'
  },
  {
    id: 'video',
    name: '视频模式',
    icon: 'solar:videocamera-bold',
    color: '#ff9500',
    description: '视频内容同步',
    route: '/device/video-mode'
  },
  {
    id: 'remote',
    name: '远程模式',
    icon: 'solar:global-bold',
    color: '#00ff88',
    description: '远程连接控制',
    route: '/device/remote-host'
  },
  {
    id: 'waveform',
    name: '波形库',
    icon: 'solar:chart-2-bold',
    color: '#ffc107',
    description: '保存的轨迹回放',
    route: '/device/waveform-library'
  }
]

export default function DeviceConnectionPage() {
  const navigate = useNavigate()
  const { t } = useTranslation('device')
  const [selectedMode, setSelectedMode] = useState<string | null>(null)

  // 使用全局设备状态
  const { connectedDevice, connectDevice, disconnectDevice } = useDeviceStore()

  // 处理设备连接
  const handleDeviceConnect = async (device: any) => {
    try {
      await connectDevice(device, 'home')
    } catch (error) {
      console.error(t('connection.failed'), error)
    }
  }

  // 处理断开连接
  const handleDisconnect = async () => {
    try {
      await disconnectDevice()
      setSelectedMode(null)
    } catch (error) {
      console.error(t('connection.disconnect_failed'), error)
    }
  }

  // 处理模式选择
  const handleModeSelect = (mode: (typeof deviceModes)[0]) => {
    if (mode.route) {
      // 跳转到对应的模式页面
      navigate(mode.route)
    } else if (mode.id === 'classic') {
      // 经典模式在当前页面展示
      setSelectedMode('classic')
    }
  }

  // 如果没有连接设备，显示连接界面
  if (!connectedDevice) {
    return (
      <div
        className="min-h-screen relative overflow-hidden flex flex-col"
        style={{ backgroundColor: '#121521' }}
      >
        {/* 背景装饰元素 */}
        <div className="absolute top-0 left-0 pointer-events-none w-screen">
          <img src="/images/device/bg.svg" className="w-full h-full" />
        </div>

        {/* 页面标题 */}
        <div className="pt-16 pb-8 text-center relative z-10">
          <h1
            className="text-white text-xl font-semibold"
            style={{ fontFamily: "'PingFang SC', sans-serif" }}
          >
            {t('connection.title')}
          </h1>
        </div>

        {/* 主要内容区域 */}
        <div className="flex-1 flex flex-col items-center px-6 relative z-10">
          {/* 设备图标 */}
          <motion.div
            className="mb-12"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <img
              src="/images/device/bluetooth.svg"
              alt={t('connection.bluetooth_device')}
              className="w-18 h-28 object-contain"
            />
          </motion.div>

          {/* 设备连接表单 */}
          <DeviceConnectionForm onDeviceConnect={handleDeviceConnect} />

          <motion.div
            className="w-full max-w-sm mt-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.8 }}
          >
            <Button
              className="w-full h-12 rounded-full text-base flex items-center font-semibold justify-center gap-2 bg-[#00ff88] hover:bg-[#00e67a] text-black"
              onPress={() => navigate('/device/remote-controller')}
            >
              远程控制
            </Button>
          </motion.div>
        </div>
      </div>
    )
  }

  // 设备已连接，显示模式选择界面
  return (
    <div className="min-h-screen bg-[#121521] relative overflow-hidden flex flex-col">
      {/* 背景装饰元素 */}
      <div className="absolute top-0 left-0 w-screen pointer-events-none">
        <img src="/images/device/bg-2.svg" alt="" className="w-screen" />
      </div>

      {/* 顶部标题栏 */}
      <div className="relative z-10 pt-12 pb-8">
        <div className="text-center">
          <h1 className="text-white text-xl font-semibold">设备控制</h1>
        </div>
      </div>

      {/* 设备信息区域 */}
      <div className="relative z-11 pl-7 pr-7 mb-12">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <h2 className="text-white text-2xl font-semibold mb-1.5">{connectedDevice.name}</h2>
            <div className="flex flex-col gap-1">
              <div className="flex items-center">
                <p className="text-[#7c85b6] text-sm">已连接</p>

                <motion.button
                  onClick={handleDisconnect}
                  className="size-6 flex items-center justify-center bg-red-500/10 hover:bg-red-500/20 rounded-full text-red-400 hover:text-red-300 transition-colors ml-2"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  title="断开连接"
                >
                  <Icon icon="solar:power-bold" width={14} />
                </motion.button>
              </div>
            </div>
          </div>
          <div className="flex items-center justify-center absolute right-5 -top-10">
            {connectedDevice.pic ? (
              <img
                src={connectedDevice.pic}
                alt={connectedDevice.name}
                className="size-40 object-contain"
              />
            ) : (
              <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center">
                <div className="w-12 h-12 bg-gradient-to-br from-yellow-300 to-green-400 rounded-full transform rotate-45"></div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 模式选择网格 */}
      <AnimatePresence>
        {selectedMode !== 'classic' && (
          <motion.div
            initial={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="relative z-10 px-4"
          >
            {/* <h3 className="text-white text-lg font-semibold mb-4 px-3">选择控制模式</h3> */}
            <div className="grid grid-cols-2 gap-4 mb-6">
              {deviceModes.map((mode, index) => (
                <motion.div
                  key={mode.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => handleModeSelect(mode)}
                  className="
                    relative overflow-hidden rounded-2xl p-4 cursor-pointer
                    bg-black/20 backdrop-blur-sm border border-white/20
                    hover:border-white/40 hover:bg-black/30 transition-all duration-300
                  "
                >
                  {/* 背景装饰 */}
                  <div className="absolute inset-0 overflow-hidden">
                    <motion.div
                      className="absolute -top-8 -right-8 w-20 h-20 rounded-full blur-xl opacity-30"
                      style={{
                        background: `linear-gradient(135deg, ${mode.color}40, ${mode.color}20)`
                      }}
                    />
                    <div
                      className="absolute -bottom-6 -left-6 w-16 h-16 rounded-full blur-xl opacity-20"
                      style={{
                        background: `linear-gradient(135deg, ${mode.color}30, transparent)`
                      }}
                    />
                  </div>

                  {/* 内容区域 */}
                  <div className="relative z-10 flex flex-col items-center text-center">
                    {/* 图标区域 */}
                    <div className="mb-4 flex justify-center">
                      <motion.div
                        className="w-14 h-14 rounded-full flex items-center justify-center relative"
                        style={{
                          background: `linear-gradient(135deg, ${mode.color}40, ${mode.color}20)`,
                          boxShadow: `0 4px 20px ${mode.color}30`
                        }}
                      >
                        <Icon icon={mode.icon} width={28} style={{ color: mode.color }} />

                        {/* 图标光晕效果 */}
                        <motion.div
                          animate={{
                            scale: [1, 1.2, 1],
                            opacity: [0.3, 0.6, 0.3]
                          }}
                          transition={{
                            duration: 2,
                            repeat: Infinity,
                            ease: 'easeInOut'
                          }}
                          className="absolute inset-0 rounded-full"
                          style={{
                            background: `linear-gradient(135deg, ${mode.color}20, transparent)`
                          }}
                        />
                      </motion.div>
                    </div>

                    {/* 标题和描述 */}
                    <h4 className="text-white text-base font-semibold mb-2">{mode.name}</h4>
                    <p className="text-white/60 text-xs leading-relaxed">{mode.description}</p>
                  </div>

                  {/* 悬浮效果 */}
                  <motion.div
                    className="absolute inset-0 opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none"
                    style={{
                      background: `linear-gradient(135deg, ${mode.color}10, transparent)`
                    }}
                    whileHover={{ opacity: 1 }}
                  />
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 经典模式展示区域 */}
      <AnimatePresence>
        {selectedMode === 'classic' && (
          <LocalDeviceControlProvider>
            <ClassicModeControl onBack={() => setSelectedMode(null)} showBackButton={true} />
          </LocalDeviceControlProvider>
        )}
      </AnimatePresence>
    </div>
  )
}
