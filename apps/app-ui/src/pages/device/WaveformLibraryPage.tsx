import React, { useState, useEffect, useCallback } from 'react'
import { useNavigate } from 'react-router'
import { useTranslation } from 'react-i18next'
import { Icon } from '@iconify/react'
import { motion, AnimatePresence } from 'framer-motion'
import { addToast } from '@heroui/react'
import type { WaveformLibraryItem, WaveformLibraryFilter } from '@/types/swipe-mode'
import { WaveformCard, WaveformCardSkeleton } from '@/components/device/waveform/WaveformCard'
import { waveformStorageDexie } from '@/services/waveform-storage-dexie'

export default function WaveformLibraryPage() {
  const navigate = useNavigate()
  const { t } = useTranslation('device')

  // 状态管理
  const [waveforms, setWaveforms] = useState<WaveformLibraryItem[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [sortBy, setSortBy] = useState<'latest' | 'popular' | 'favorites'>('latest')

  // 加载波形数据
  const loadWaveforms = useCallback(
    async (isRefresh = false) => {
      try {
        if (isRefresh) {
          setRefreshing(true)
        } else {
          setLoading(true)
        }

        // 构建筛选条件
        const filter: WaveformLibraryFilter = {
          sortBy: sortBy === 'favorites' ? 'popular' : sortBy, // favorites实际上按popular排序
          showType: 'all',
          searchQuery: '',
          tags: []
        }

        // 根据排序类型加载数据
        let result: WaveformLibraryItem[] = []

        if (sortBy === 'favorites') {
          // 获取收藏的波形
          result = await waveformStorageDexie.getFavoriteWaveforms()
        } else {
          // 获取公共波形
          const response = await waveformStorageDexie.getPublicWaveforms(filter)
          result = response.items
        }

        setWaveforms(result)
      } catch (error) {
        console.error('加载波形失败:', error)
        addToast({
          title: '加载失败',
          description: '无法加载波形数据',
          color: 'danger'
        })
      } finally {
        setLoading(false)
        setRefreshing(false)
      }
    },
    [sortBy]
  )

  // 处理播放波形
  const handlePlayWaveform = useCallback(
    (waveform: WaveformLibraryItem) => {
      navigate(`/device/swipe-mode?playback=${waveform.id}`)
    },
    [navigate]
  )

  // 处理点赞
  const handleLikeWaveform = useCallback(
    async (waveformId: string): Promise<void> => {
      try {
        const waveform = waveforms.find(w => w.id === waveformId)
        if (!waveform) return

        if (waveform.isLiked) {
          await waveformStorageDexie.unlikeWaveform(waveformId)
        } else {
          await waveformStorageDexie.likeWaveform(waveformId)
        }

        // 更新本地状态
        setWaveforms(prev =>
          prev.map(w =>
            w.id === waveformId
              ? {
                  ...w,
                  isLiked: !w.isLiked,
                  likeCount: w.isLiked ? w.likeCount - 1 : w.likeCount + 1
                }
              : w
          )
        )
      } catch (error) {
        console.error('点赞操作失败:', error)
        addToast({
          title: '操作失败',
          description: '点赞操作失败',
          color: 'danger'
        })
      }
    },
    [waveforms]
  )

  // 处理收藏
  const handleFavoriteWaveform = useCallback(
    async (waveformId: string): Promise<void> => {
      try {
        const waveform = waveforms.find(w => w.id === waveformId)
        if (!waveform) return

        if (waveform.isFavorited) {
          await waveformStorageDexie.unfavoriteWaveform(waveformId)
        } else {
          await waveformStorageDexie.favoriteWaveform(waveformId)
        }

        // 更新本地状态
        setWaveforms(prev =>
          prev.map(w =>
            w.id === waveformId
              ? {
                  ...w,
                  isFavorited: !w.isFavorited,
                  favoriteCount: w.isFavorited ? w.favoriteCount - 1 : w.favoriteCount + 1
                }
              : w
          )
        )
      } catch (error) {
        console.error('收藏操作失败:', error)
        addToast({
          title: '操作失败',
          description: '收藏操作失败',
          color: 'danger'
        })
      }
    },
    [waveforms]
  )

  // 初始化加载
  useEffect(() => {
    waveformStorageDexie.initialize().then(() => {
      loadWaveforms()
    })
  }, [loadWaveforms])

  // 排序条件变化时重新加载
  useEffect(() => {
    if (!loading) {
      loadWaveforms()
    }
  }, [sortBy])

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0a0b14] via-[#121521] to-[#1a1b2e] relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-[#ff2d97]/20 to-[#892fff]/20 rounded-full blur-3xl" />
        <div className="absolute bottom-40 right-10 w-40 h-40 bg-gradient-to-r from-[#892fff]/20 to-[#00d4ff]/20 rounded-full blur-3xl" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-gradient-to-r from-[#ff2d97]/10 to-[#892fff]/10 rounded-full blur-3xl" />
      </div>

      {/* 顶部标题栏 */}
      <div className="relative z-10 pt-12 pb-6">
        <div className="flex items-center justify-between px-4">
          <motion.button
            onClick={() => navigate(-1)}
            className="w-11 h-11 flex items-center justify-center text-white/80 bg-white/8 backdrop-blur-sm border border-white/10 rounded-2xl hover:bg-white/12 hover:text-white transition-all duration-200"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Icon icon="solar:arrow-left-linear" width={20} />
          </motion.button>

          <div className="text-center">
            <h1 className="text-white text-xl font-semibold mb-1">波形库</h1>
            <p className="text-white/50 text-xs">发现和分享精彩波形</p>
          </div>

          <motion.button
            onClick={() => navigate('/device/my-waveforms')}
            className="w-11 h-11 flex items-center justify-center text-white/80 bg-white/8 backdrop-blur-sm border border-white/10 rounded-2xl hover:bg-white/12 hover:text-white transition-all duration-200"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Icon icon="solar:user-bold" width={20} />
          </motion.button>
        </div>
      </div>

      {/* 筛选栏 */}
      <div className="relative z-10 px-4 mb-4">
        <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-2">
          <div className="flex gap-1">
            {[
              { key: 'latest', label: '最新发布' },
              { key: 'popular', label: '最多人用' },
              { key: 'favorites', label: '我的收藏' }
            ].map(item => (
              <motion.button
                key={item.key}
                onClick={() => setSortBy(item.key as any)}
                className={`flex-1 flex items-center justify-center gap-2 px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 ${
                  sortBy === item.key
                    ? 'bg-gradient-to-r from-[#ff2d97] to-[#892fff] text-white shadow-lg shadow-[#ff2d97]/25'
                    : 'text-white/70 hover:text-white hover:bg-white/8'
                }`}
                whileHover={{ scale: sortBy === item.key ? 1 : 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <span>{item.label}</span>
              </motion.button>
            ))}
          </div>
        </div>
      </div>

      {/* 波形列表 */}
      <div className="relative z-10 flex-1 px-4 pb-8">
        <AnimatePresence mode="wait">
          {loading ? (
            /* 骨架屏 */
            <motion.div
              key="skeleton"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-4"
            >
              {Array.from({ length: 6 }).map((_, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <WaveformCardSkeleton />
                </motion.div>
              ))}
            </motion.div>
          ) : waveforms.length > 0 ? (
            /* 波形列表 */
            <motion.div
              key="waveforms"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-4"
            >
              {waveforms.map((waveform, index) => (
                <motion.div
                  key={waveform.id}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{
                    duration: 0.4,
                    delay: index * 0.1,
                    ease: [0.25, 0.1, 0.25, 1]
                  }}
                >
                  <WaveformCard
                    waveform={waveform}
                    onPlay={handlePlayWaveform}
                    onLike={handleLikeWaveform}
                    onFavorite={handleFavoriteWaveform}
                  />
                </motion.div>
              ))}
            </motion.div>
          ) : (
            /* 空状态 */
            <motion.div
              key="empty"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="flex flex-col items-center justify-center py-20"
            >
              <div className="w-20 h-20 bg-gradient-to-br from-[#ff2d97]/20 to-[#892fff]/20 rounded-3xl flex items-center justify-center mb-6">
                <Icon icon="solar:music-note-2-linear" width={32} className="text-white/40" />
              </div>
              <h3 className="text-white/80 text-lg font-medium mb-2">
                {sortBy === 'favorites' ? '暂无收藏的波形' : '暂无波形数据'}
              </h3>
              <p className="text-white/50 text-sm text-center mb-8">
                {sortBy === 'favorites' ? '去发现一些精彩的波形并收藏吧' : '成为第一个分享波形的人'}
              </p>
              <motion.button
                onClick={() => navigate('/device/swipe-mode')}
                className="px-6 py-3 bg-button-primary text-white text-sm font-medium rounded-2xl shadow-lg shadow-[#ff2d97]/25 hover:shadow-[#ff2d97]/40 transition-all duration-200"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                开始创作
              </motion.button>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* 下拉刷新提示 */}
      {refreshing && (
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -50 }}
          className="absolute top-24 left-1/2 transform -translate-x-1/2 z-20"
        >
          <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl px-4 py-2 flex items-center gap-2">
            <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
            <span className="text-white/80 text-sm">刷新中...</span>
          </div>
        </motion.div>
      )}
    </div>
  )
}
