import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router'
import { useTranslation } from 'react-i18next'
import { Icon } from '@iconify/react'
import { motion } from 'framer-motion'
import { useMusicPlayer } from '@/stores/music-player'
import { musicService } from '@/services/music'
import { ImmersivePlayer } from '@/components/device/music/ImmersivePlayer'

export default function AudioModePage() {
  const navigate = useNavigate()
  const { t } = useTranslation('device')

  const { currentMusic, setCurrentMusic, setDeviceConnected } = useMusicPlayer()
  const [isInitialized, setIsInitialized] = useState(false)

  // 初始化数据并自动开始播放
  useEffect(() => {
    const initializeData = async () => {
      try {
        // 加载在线音乐数据
        const onlineMusic = await musicService.getOnlineMusic()

        // 自动选择第一首音乐开始播放
        if (onlineMusic.length > 0) {
          setCurrentMusic(onlineMusic[0])
        }

        // 后台加载导入音乐数据
        musicService.getImportedMusic()

        // 设置设备连接状态
        setDeviceConnected(true)

        // 标记初始化完成
        setIsInitialized(true)
      } catch (error) {
        console.error('初始化音乐数据失败:', error)
        setIsInitialized(true) // 即使出错也要标记完成，避免无限加载
      }
    }

    initializeData()
  }, [setCurrentMusic, setDeviceConnected])

  // 处理返回
  const handleBack = () => {
    navigate(-1)
  }

  // 加载状态
  if (!isInitialized) {
    return (
      <div className="min-h-screen bg-[#121521] relative overflow-hidden flex items-center justify-center">
        {/* 背景 */}
        <div className="absolute top-0 left-0 w-screen pointer-events-none">
          <img src="/images/device/bg.svg" className="w-full h-full" />
        </div>

        {/* 加载动画 */}
        <div className="relative z-10 flex flex-col items-center gap-4">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
            className="w-12 h-12 border-4 border-[#892fff] border-t-transparent rounded-full"
          />
          <p className="text-white/60 text-lg">正在加载音乐...</p>
        </div>
      </div>
    )
  }

  // 如果没有音乐，显示空状态
  if (!currentMusic) {
    return (
      <div className="min-h-screen bg-[#121521] relative overflow-hidden flex items-center justify-center">
        {/* 背景 */}
        <div className="absolute top-0 left-0 w-screen pointer-events-none">
          <img src="/images/device/bg.svg" className="w-full h-full" />
        </div>

        {/* 空状态 */}
        <div className="relative z-10 flex flex-col items-center gap-4">
          <Icon icon="solar:music-note-3-linear" width={64} className="text-white/40" />
          <p className="text-white/60 text-lg">暂无可播放的音乐</p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleBack}
            className="px-6 py-3 bg-[#892fff] text-white rounded-full hover:bg-[#892fff]/80 transition-colors"
          >
            返回
          </motion.button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-[#121521] relative overflow-hidden flex flex-col">
      {/* 背景元素 */}
      <div className="absolute top-0 left-0 w-screen pointer-events-none z-20">
        <img src="/images/device/bg.svg" className="w-full h-full" />
      </div>
      {/* 沉浸式播放器 */}
      <div className="relative z-10 flex-1 min-h-0">
        <ImmersivePlayer music={currentMusic} onBack={handleBack} />
      </div>
    </div>
  )
}
