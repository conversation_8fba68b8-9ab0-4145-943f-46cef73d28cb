import React from 'react'
import { useSearchParams } from 'react-router'
import { LocalDeviceControlProvider } from '../../contexts/local-device-control-provider'
import { RemoteDeviceControlProvider } from '../../contexts/remote-device-control-provider'
import SwipeModePage from './SwipeModePage'

export default function SwipeModeWrapper() {
  const [searchParams] = useSearchParams()
  const isRemoteMode = searchParams.get('remote') === 'true'

  // 根据是否为远程模式选择对应的Provider
  if (isRemoteMode) {
    return (
      <RemoteDeviceControlProvider>
        <SwipeModePage />
      </RemoteDeviceControlProvider>
    )
  }

  return (
    <LocalDeviceControlProvider>
      <SwipeModePage />
    </LocalDeviceControlProvider>
  )
}
