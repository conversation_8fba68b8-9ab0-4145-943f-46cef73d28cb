import React, { useEffect, useState } from 'react'
import { useNavigate } from 'react-router'
import { Icon } from '@iconify/react'
import { motion } from 'framer-motion'
import { App } from '@capacitor/app'
import { useRemoteControlStore } from '@/stores/remote-control-store'
import { EmojiToast } from '@/components/device/EmojiToast'
import { useSmartNavigation } from '@/hooks/use-smart-navigation'

export default function RemoteControllerPage() {
  const { smartNavigate, goBack } = useSmartNavigation()
  const {
    controller,
    actions: { connectToDevice }
  } = useRemoteControlStore()

  const [controlCode, setControlCode] = useState('')
  const [isConnecting, setIsConnecting] = useState(false)

  // 处理连接
  const handleConnect = async () => {
    if (!controlCode.trim()) return

    setIsConnecting(true)
    try {
      await connectToDevice(controlCode.trim())
      // 连接成功后跳转到控制页面
      smartNavigate('/device/remote-controller/control')
    } catch (error) {
      console.error('连接失败:', error)
    } finally {
      setIsConnecting(false)
    }
  }

  // 处理粘贴
  const handlePaste = async () => {
    try {
      const text = await navigator.clipboard.readText()
      if (text && text.length === 6) {
        setControlCode(text.toUpperCase())
      }
    } catch (error) {
      console.error('粘贴失败:', error)
    }
  }

  // 监听硬件返回键（移动端）
  useEffect(() => {
    const handleBackButton = () => {
      goBack()
    }

    App.addListener('backButton', handleBackButton)

    return () => {
      App.removeAllListeners()
    }
  }, [goBack])

  // 监听连接状态变化
  useEffect(() => {
    if (controller.isConnected) {
      // 已连接时跳转到控制页面
      smartNavigate('/device/remote-controller/control')
    }
  }, [controller.isConnected, smartNavigate])

  return (
    <div className="min-h-screen bg-[#121521] relative overflow-hidden">
      {/* 背景装饰元素 */}
      <div className="absolute top-0 left-0 w-screen pointer-events-none">
        <img src="/images/device/bg.svg" className="w-full h-full" />
      </div>

      {/* 顶部标题栏 */}
      <div className="relative z-10 pt-16 pb-4">
        <div className="flex items-center justify-between px-6">
          <motion.button
            onClick={() => goBack()}
            className="w-10 h-10 flex items-center justify-center text-white hover:bg-white/20 transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Icon icon="solar:arrow-left-linear" width={20} />
          </motion.button>
          <h1 className="text-white text-xl font-semibold">远程控制</h1>
          <div className="w-10 h-10" /> {/* 占位符保持居中 */}
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="relative z-10 flex-1 px-6">
        <div className="flex flex-col items-center justify-center min-h-[60vh]">
          {/* 图标区域 */}
          <motion.div
            className="w-24 h-24 bg-gradient-to-br from-[#892fff] to-[#a855f7] rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg shadow-[#892fff]/20"
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{ duration: 0.8, type: 'spring', stiffness: 100 }}
          >
            <Icon icon="solar:global-bold" width={48} className="text-white" />
          </motion.div>

          {/* 标题区域 */}
          <motion.div
            className="text-center mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <h2 className="text-white text-2xl font-bold mb-3">连接远程设备</h2>
            <p className="text-[#7c85b6] text-base leading-relaxed">输入6位控制码来控制远程设备</p>
          </motion.div>

          {/* 输入区域 */}
          <motion.div
            className="w-full max-w-sm mb-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            <div className="relative">
              <input
                type="text"
                value={controlCode}
                onChange={e => setControlCode(e.target.value.toUpperCase())}
                placeholder="输入6位控制码"
                className="w-full bg-black/20 backdrop-blur-sm text-white text-center py-4 px-6 pr-14 rounded-2xl text-lg font-mono tracking-wider placeholder-[#7c85b6] border border-white/20 focus:border-[#892fff] focus:bg-black/30 outline-none transition-all"
                maxLength={6}
              />
              <motion.button
                onClick={handlePaste}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#7c85b6] hover:text-[#892fff] p-2 rounded-lg hover:bg-white/10 transition-colors"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <Icon icon="solar:clipboard-bold" width={20} />
              </motion.button>
            </div>
          </motion.div>

          {/* 连接按钮 */}
          <motion.button
            onClick={handleConnect}
            disabled={controlCode.length !== 6 || isConnecting}
            className="w-full max-w-sm h-14 bg-gradient-to-r from-[#892fff] to-[#a855f7] hover:from-[#7c2ae8] hover:to-[#9333ea] text-white text-lg font-semibold rounded-3xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg shadow-[#892fff]/20"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.7 }}
            whileHover={{
              scale: controlCode.length === 6 && !isConnecting ? 1.02 : 1,
              y: controlCode.length === 6 && !isConnecting ? -2 : 0
            }}
            whileTap={{ scale: 0.98 }}
          >
            {isConnecting ? (
              <div className="flex items-center justify-center gap-2">
                <motion.div
                  className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                />
                连接中...
              </div>
            ) : (
              <div className="flex items-center justify-center gap-2">
                <Icon icon="solar:link-bold" width={24} />
                连接设备
              </div>
            )}
          </motion.button>

          {/* 说明信息 */}
          <motion.div
            className="mt-8 w-full max-w-sm bg-black/20 backdrop-blur-sm border border-white/10 rounded-2xl p-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.8 }}
          >
            <div className="space-y-2 text-[#7c85b6] text-sm">
              <div className="flex items-center gap-2">
                <Icon icon="solar:shield-check-bold" width={16} className="text-[#892fff]" />
                <span>安全连接，随时可断开</span>
              </div>
              <div className="flex items-center gap-2">
                <Icon icon="solar:gameboy-bold" width={16} className="text-[#892fff]" />
                <span>支持经典模式和划屏模式</span>
              </div>
              <div className="flex items-center gap-2">
                <Icon icon="solar:clock-circle-bold" width={16} className="text-[#892fff]" />
                <span>控制码30分钟内有效</span>
              </div>
            </div>
          </motion.div>
        </div>

        {/* 错误提示 */}
        {controller.error && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-4 p-4 bg-red-500/20 border border-red-500/30 rounded-2xl backdrop-blur-sm"
          >
            <div className="flex items-center gap-2">
              <Icon icon="solar:danger-triangle-bold" width={16} className="text-red-400" />
              <p className="text-red-400 text-sm">{controller.error}</p>
            </div>
          </motion.div>
        )}
      </div>

      {/* Emoji Toast */}
      <EmojiToast />
    </div>
  )
}
