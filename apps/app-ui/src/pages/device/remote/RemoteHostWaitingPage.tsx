import React, { useEffect, useState } from 'react'
import { Icon } from '@iconify/react'
import { motion } from 'framer-motion'
import { App } from '@capacitor/app'
// import { Share } from '@capacitor/share'
// import { Clipboard } from '@capacitor/clipboard'
import { useRemoteControlStore } from '@/stores/remote-control-store'
import { EmojiToast } from '@/components/device/EmojiToast'
import { useSmartNavigation } from '@/hooks/use-smart-navigation'

export default function RemoteHostWaitingPage() {
  const { goBack, smartNavigate } = useSmartNavigation()

  const {
    host,
    actions: { revokeControlCode }
  } = useRemoteControlStore()

  const [showExitConfirm, setShowExitConfirm] = useState(false)

  // 复制控制码
  const handleCopyCode = async () => {
    if (host.controlCode?.code) {
      try {
        // TODO: 实现复制功能
        console.log('复制控制码:', host.controlCode.code)
        // 这里可以添加复制成功的提示
      } catch (error) {
        console.error('复制失败:', error)
      }
    }
  }

  // 分享控制码
  const handleShareCode = async () => {
    if (host.controlCode?.code) {
      try {
        // TODO: 实现分享功能
        console.log('分享控制码:', host.controlCode.code)
      } catch (error) {
        console.error('分享失败:', error)
      }
    }
  }

  // 撤销控制码
  const handleRevoke = async () => {
    await revokeControlCode()
    goBack()
  }

  // 退出确认处理
  const handleExitConfirm = () => {
    setShowExitConfirm(false)
    handleRevoke()
  }

  // 监听硬件返回键（移动端）
  useEffect(() => {
    const handleBackButton = () => {
      // 在等待状态下显示确认对话框
      setShowExitConfirm(true)
    }

    // 添加返回键监听
    App.addListener('backButton', handleBackButton)

    return () => {
      // 清理监听器
      App.removeAllListeners()
    }
  }, [])

  // 监听连接状态变化
  useEffect(() => {
    if (host.connectedController) {
      // 有控制端连接时跳转到已连接页面
      smartNavigate('/device/remote-host/connected')
    }
  }, [host.connectedController, smartNavigate])

  return (
    <div className="min-h-screen bg-[#121521] relative overflow-hidden">
      {/* 背景装饰元素 */}
      <div className="absolute top-0 left-0 w-screen pointer-events-none">
        <img src="/images/device/bg.svg" className="w-full h-full" />
      </div>

      {/* 顶部标题栏 */}
      <div className="relative z-10 pt-16 pb-4">
        <div className="flex items-center justify-between px-6">
          <motion.button
            onClick={() => setShowExitConfirm(true)}
            className="w-10 h-10 flex items-center justify-center text-white bg-white/10 rounded-2xl hover:bg-white/20 transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Icon icon="solar:arrow-left-linear" width={20} />
          </motion.button>
          <h1 className="text-white text-xl font-semibold">等待连接</h1>
          <motion.button
            onClick={handleRevoke}
            className="px-3 py-1.5 bg-red-500/20 hover:bg-red-500/30 text-red-400 text-sm rounded-2xl transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            停止
          </motion.button>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="relative z-10 flex-1 px-6">
        <div className="flex flex-col items-center justify-center min-h-[60vh]">
          {/* 控制码显示 */}
          <motion.div
            className="bg-black/20 backdrop-blur-sm border border-white/20 rounded-2xl p-8 mb-8 text-center w-full"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="mb-4">
              <Icon icon="solar:key-bold" width={48} className="text-[#00ff88] mx-auto mb-4" />
              <h3 className="text-white text-2xl font-semibold mb-2">控制码</h3>
              <div className="text-4xl font-mono tracking-wider text-[#00ff88] font-bold">
                {host.controlCode?.code || 'ABC123'}
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex gap-3 justify-center">
              <motion.button
                onClick={handleCopyCode}
                className="flex-1 h-12 bg-[#1d2135] hover:bg-[#242938] text-white rounded-2xl border border-white/10 hover:border-white/20 transition-all flex items-center justify-center gap-2"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Icon icon="solar:copy-bold" width={18} />
                复制
              </motion.button>
              <motion.button
                onClick={handleShareCode}
                className="flex-1 h-12 bg-[#1d2135] hover:bg-[#242938] text-white rounded-2xl border border-white/10 hover:border-white/20 transition-all flex items-center justify-center gap-2"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Icon icon="solar:share-bold" width={18} />
                分享
              </motion.button>
            </div>
          </motion.div>

          {/* 等待状态指示 */}
          <motion.div
            className="text-center mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <div className="flex items-center justify-center gap-2 mb-3">
              <motion.div
                className="w-3 h-3 bg-[#00ff88] rounded-full"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.5, 1, 0.5]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: 'easeInOut'
                }}
              />
              <span className="text-[#00ff88] text-sm font-medium">等待控制端连接</span>
            </div>
            <p className="text-[#7c85b6] text-sm">将控制码分享给需要控制设备的人</p>
          </motion.div>

          {/* 剩余时间显示 */}
          <motion.div
            className="bg-black/10 backdrop-blur-sm border border-white/5 rounded-2xl p-4 text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <div className="flex items-center justify-center gap-2">
              <Icon icon="solar:clock-circle-bold" width={16} className="text-[#7c85b6]" />
              <span className="text-[#7c85b6] text-sm">控制码将在 30分钟 后过期</span>
            </div>
          </motion.div>
        </div>

        {/* 错误提示 */}
        {host.error && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-4 p-4 bg-red-500/20 border border-red-500/30 rounded-2xl backdrop-blur-sm"
          >
            <div className="flex items-center gap-2">
              <Icon icon="solar:danger-triangle-bold" width={16} className="text-red-400" />
              <p className="text-red-400 text-sm">{host.error}</p>
            </div>
          </motion.div>
        )}
      </div>

      {/* 退出确认对话框 */}
      {showExitConfirm && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-6">
          <motion.div
            className="bg-[#1d2135] rounded-3xl p-6 w-full max-w-sm border border-white/10"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            <div className="text-center mb-6">
              <Icon
                icon="solar:danger-triangle-bold"
                width={48}
                className="text-yellow-500 mx-auto mb-4"
              />
              <h3 className="text-white text-lg font-semibold mb-2">确认退出</h3>
              <p className="text-[#7c85b6] text-sm">退出将撤销控制码，正在等待的连接将被中断</p>
            </div>
            <div className="flex gap-3">
              <button
                onClick={() => setShowExitConfirm(false)}
                className="flex-1 h-12 bg-white/10 hover:bg-white/20 text-white rounded-2xl transition-colors"
              >
                取消
              </button>
              <button
                onClick={handleExitConfirm}
                className="flex-1 h-12 bg-red-500 hover:bg-red-600 text-white rounded-2xl transition-colors"
              >
                确认退出
              </button>
            </div>
          </motion.div>
        </div>
      )}

      {/* Emoji Toast */}
      <EmojiToast />
    </div>
  )
}
