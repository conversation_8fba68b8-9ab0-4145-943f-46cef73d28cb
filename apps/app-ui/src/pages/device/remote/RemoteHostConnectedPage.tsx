import React, { useEffect, useState } from 'react'
import { Icon } from '@iconify/react'
import { motion } from 'framer-motion'
import { App } from '@capacitor/app'
import { useRemoteControlStore } from '@/stores/remote-control-store'
import { EmojiToast } from '@/components/device/EmojiToast'
import { useSmartNavigation } from '@/hooks/use-smart-navigation'

export default function RemoteHostConnectedPage() {
  const { goBack } = useSmartNavigation()
  const {
    host,
    actions: { revokeControlCode, sendEmojiReaction }
  } = useRemoteControlStore()

  const [showExitConfirm, setShowExitConfirm] = useState(false)

  // 使用模拟数据进行测试
  const connectedController = host.connectedController || {
    controllerName: '测试用户',
    currentMode: 'classic' as const,
    connectedAt: Date.now() - 5 * 60 * 1000 // 5分钟前
  }

  // 断开连接
  const handleDisconnect = async () => {
    await revokeControlCode()
    goBack()
  }

  // 退出确认处理
  const handleExitConfirm = () => {
    setShowExitConfirm(false)
    handleDisconnect()
  }

  // 发送Emoji反应
  const handleSendEmoji = async (emoji: string) => {
    try {
      await sendEmojiReaction(emoji)
    } catch (error) {
      console.error('发送Emoji失败:', error)
    }
  }

  // 监听硬件返回键（移动端）
  useEffect(() => {
    const handleBackButton = () => {
      // 在已连接状态下显示确认对话框
      setShowExitConfirm(true)
    }

    // 添加返回键监听
    App.addListener('backButton', handleBackButton)

    return () => {
      // 清理监听器
      App.removeAllListeners()
    }
  }, [])

  // 监听连接状态变化
  useEffect(() => {
    if (!host.connectedController && !host.controlCode) {
      // 连接断开时返回初始页面
      goBack()
    }
  }, [host.connectedController, host.controlCode, goBack])

  return (
    <div className="min-h-screen bg-[#121521] relative overflow-hidden">
      {/* 背景装饰元素 */}
      <div className="absolute top-0 left-0 w-screen pointer-events-none">
        <img src="/images/device/bg.svg" className="w-full h-full" />
      </div>

      {/* 顶部标题栏 */}
      <div className="relative z-10 pt-16 pb-4">
        <div className="flex items-center justify-between px-6">
          <motion.button
            onClick={() => setShowExitConfirm(true)}
            className="w-10 h-10 flex items-center justify-center text-white bg-white/10 rounded-2xl hover:bg-white/20 transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Icon icon="solar:arrow-left-linear" width={20} />
          </motion.button>
          <h1 className="text-white text-xl font-semibold">远程控制中</h1>
          <motion.button
            onClick={handleDisconnect}
            className="px-3 py-1.5 bg-red-500/20 hover:bg-red-500/30 text-red-400 text-sm rounded-2xl transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            断开
          </motion.button>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="relative z-10 flex-1 px-6">
        <div className="flex flex-col items-center justify-center min-h-[60vh] space-y-6">
          {/* 连接状态卡片 */}
          <motion.div
            className="w-full max-w-sm bg-black/20 backdrop-blur-sm border border-white/10 rounded-2xl p-6"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="text-center mb-4">
              <div className="w-16 h-16 bg-[#00ff88] rounded-2xl flex items-center justify-center mx-auto mb-3">
                <Icon icon="solar:user-bold" width={32} className="text-black" />
              </div>
              <h3 className="text-white text-xl font-semibold mb-1">
                {connectedController.controllerName}
              </h3>
              <p className="text-[#7c85b6] text-sm">正在控制你的设备</p>
            </div>

            {/* 连接信息 */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-[#7c85b6] text-sm">控制模式</span>
                <div className="flex items-center gap-2">
                  <Icon
                    icon={
                      connectedController.currentMode === 'classic'
                        ? 'solar:gameboy-bold'
                        : 'solar:hand-stars-bold'
                    }
                    width={16}
                    className="text-[#00ff88]"
                  />
                  <span className="text-white text-sm">
                    {connectedController.currentMode === 'classic' ? '经典模式' : '划屏模式'}
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-[#7c85b6] text-sm">连接状态</span>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-green-400 text-sm">在线</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-[#7c85b6] text-sm">连接时长</span>
                <span className="text-white text-sm">05:32</span>
              </div>
            </div>
          </motion.div>

          {/* Emoji快速回应 */}
          <motion.div
            className="w-full max-w-sm bg-black/20 backdrop-blur-sm border border-white/10 rounded-2xl p-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <h4 className="text-white text-lg font-semibold mb-4 text-center">快速回应</h4>
            <div className="grid grid-cols-4 gap-3">
              {['👍', '👎', '😊', '😢', '❤️', '🔥', '👏', '🎉'].map((emoji, index) => (
                <motion.button
                  key={emoji}
                  onClick={() => handleSendEmoji(emoji)}
                  className="w-12 h-12 bg-[#1d2135] hover:bg-[#242938] rounded-2xl flex items-center justify-center text-2xl transition-colors border border-white/10 hover:border-[#00ff88]/50"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.3 + index * 0.05 }}
                >
                  {emoji}
                </motion.button>
              ))}
            </div>
          </motion.div>

          {/* 控制提示 */}
          <motion.div
            className="w-full max-w-sm bg-black/10 backdrop-blur-sm border border-white/5 rounded-2xl p-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <div className="text-center">
              <Icon
                icon="solar:info-circle-bold"
                width={20}
                className="text-[#7c85b6] mx-auto mb-2"
              />
              <p className="text-[#7c85b6] text-sm">
                控制端正在使用
                {connectedController.currentMode === 'classic' ? '经典模式' : '划屏模式'}控制设备
              </p>
            </div>
          </motion.div>
        </div>

        {/* 错误提示 */}
        {host.error && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-4 p-4 bg-red-500/20 border border-red-500/30 rounded-2xl backdrop-blur-sm"
          >
            <div className="flex items-center gap-2">
              <Icon icon="solar:danger-triangle-bold" width={16} className="text-red-400" />
              <p className="text-red-400 text-sm">{host.error}</p>
            </div>
          </motion.div>
        )}
      </div>

      {/* 退出确认对话框 */}
      {showExitConfirm && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-6">
          <motion.div
            className="bg-[#1d2135] rounded-3xl p-6 w-full max-w-sm border border-white/10"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            <div className="text-center mb-6">
              <Icon
                icon="solar:danger-triangle-bold"
                width={48}
                className="text-yellow-500 mx-auto mb-4"
              />
              <h3 className="text-white text-lg font-semibold mb-2">确认断开连接</h3>
              <p className="text-[#7c85b6] text-sm">
                断开连接将结束远程控制会话，控制端将失去设备控制权
              </p>
            </div>
            <div className="flex gap-3">
              <button
                onClick={() => setShowExitConfirm(false)}
                className="flex-1 h-12 bg-white/10 hover:bg-white/20 text-white rounded-2xl transition-colors"
              >
                取消
              </button>
              <button
                onClick={handleExitConfirm}
                className="flex-1 h-12 bg-red-500 hover:bg-red-600 text-white rounded-2xl transition-colors"
              >
                确认断开
              </button>
            </div>
          </motion.div>
        </div>
      )}

      {/* Emoji Toast */}
      <EmojiToast />
    </div>
  )
}
