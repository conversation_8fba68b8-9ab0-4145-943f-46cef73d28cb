import React, { useEffect, useState } from 'react'
import { useNavigate } from 'react-router'
import { Icon } from '@iconify/react'
import { motion, AnimatePresence } from 'framer-motion'
import { App } from '@capacitor/app'
import { Modal, ModalContent, ModalBody } from '@heroui/react'
import { useRemoteControlStore } from '@/stores/remote-control-store'
import { RemoteDeviceControlProvider } from '@/contexts/remote-device-control-provider'
import { ClassicModeControl } from '@/components/device/classic-mode'
import { EmojiToast } from '@/components/device/EmojiToast'
import { useSmartNavigation } from '@/hooks/use-smart-navigation'

export default function RemoteControllerControlPage() {
  const { goBack, smartNavigate } = useSmartNavigation()
  const {
    controller,
    lastReaction,
    actions: { switchMode, disconnectFromDevice }
  } = useRemoteControlStore()

  const [showClassicMode, setShowClassicMode] = useState(false)
  const [showExitConfirm, setShowExitConfirm] = useState(false)

  // 处理模式选择
  const handleModeSelect = (mode: 'classic' | 'swipe') => {
    switchMode(mode)

    if (mode === 'classic') {
      setShowClassicMode(true)
    } else if (mode === 'swipe') {
      // 跳转到远程划屏模式
      smartNavigate('/device/swipe-mode?remote=true')
    }
  }

  // 处理经典模式弹窗关闭
  const handleClassicModeClose = () => {
    setShowClassicMode(false)
  }

  // 处理断开连接
  const handleDisconnect = async () => {
    await disconnectFromDevice()
  }

  // 退出确认处理
  const handleExitConfirm = () => {
    setShowExitConfirm(false)
    handleDisconnect()
  }

  // 监听硬件返回键（移动端）
  useEffect(() => {
    const handleBackButton = () => {
      // 在控制状态下显示确认对话框
      setShowExitConfirm(true)
    }

    App.addListener('backButton', handleBackButton)

    return () => {
      App.removeAllListeners()
    }
  }, [])

  // 监听连接状态变化
  useEffect(() => {
    if (!controller.isConnected) {
      // 连接断开时返回连接页面
      goBack()
    }
  }, [controller.isConnected, goBack])

  return (
    <div className="min-h-screen bg-[#121521] relative overflow-hidden">
      {/* 背景装饰元素 */}
      <div className="absolute top-0 left-0 w-screen pointer-events-none">
        <img src="/images/device/bg.svg" className="w-full h-full" />
      </div>

      {/* 顶部标题栏 */}
      <div className="relative z-10 pt-16 pb-4">
        <div className="flex items-center justify-between px-6">
          <motion.button
            onClick={() => setShowExitConfirm(true)}
            className="w-10 h-10 flex items-center justify-center text-white hover:bg-white/20 transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Icon icon="solar:arrow-left-linear" width={20} />
          </motion.button>
          <h1 className="text-white text-xl font-semibold">远程控制</h1>
          <motion.button
            onClick={handleDisconnect}
            className="px-3 py-1.5 bg-red-500/20 hover:bg-red-500/30 text-red-400 text-sm rounded-2xl transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            断开
          </motion.button>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="relative z-10 flex-1 px-6">
        <div className="flex flex-col items-center justify-center min-h-[70vh]">
          {/* 设备信息卡片 - 更大更突出 */}
          <motion.div
            className="w-full max-w-md p-8 mb-8"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, type: 'spring', stiffness: 100 }}
          >
            <div className="text-center">
              {/* 设备图标 - 更大更醒目 */}
              <motion.div
                className="w-20 h-20 bg-gradient-to-br from-[#00ff88] to-[#00d4aa] rounded-3xl flex items-center justify-center mx-auto mb-4 shadow-lg shadow-[#00ff88]/20"
                initial={{ rotateY: 0 }}
                animate={{ rotateY: 360 }}
                transition={{ duration: 1, delay: 0.3 }}
              >
                <Icon icon="solar:smartphone-bold" width={36} className="text-black" />
              </motion.div>

              {/* 设备名称 - 更大字体 */}
              <motion.h3
                className="text-white text-2xl font-bold mb-2"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                {controller.targetDevice?.name || '智能设备 Pro'}
              </motion.h3>

              {/* 用户信息 */}
              <motion.p
                className="text-[#7c85b6] text-base"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
              >
                {controller.targetDevice?.hostName || '用户613'}
              </motion.p>
            </div>
          </motion.div>

          {/* 模式选择 - 更大的按钮 */}
          <motion.div
            className="w-full max-w-md"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <div className="grid grid-cols-2 gap-6 mb-8">
              {/* 经典模式按钮 */}
              <motion.button
                onClick={() => handleModeSelect('classic')}
                className="group relative overflow-hidden rounded-3xl p-8 cursor-pointer bg-black/30 backdrop-blur-lg border border-white/20 hover:border-[#ff2d97]/60 hover:bg-[#ff2d97]/10 transition-all duration-500"
                whileHover={{ scale: 1.05, y: -5 }}
                whileTap={{ scale: 0.95 }}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.6 }}
              >
                {/* 背景光晕效果 */}
                <div className="absolute inset-0 overflow-hidden">
                  <motion.div
                    className="absolute -top-12 -right-12 w-32 h-32 rounded-full blur-2xl opacity-20 group-hover:opacity-40 transition-opacity duration-500"
                    style={{
                      background: 'linear-gradient(135deg, #ff2d97, #ff6b9d)'
                    }}
                    animate={{
                      scale: [1, 1.2, 1],
                      rotate: [0, 180, 360]
                    }}
                    transition={{
                      duration: 8,
                      repeat: Infinity,
                      ease: 'linear'
                    }}
                  />
                </div>

                {/* 内容区域 */}
                <div className="relative z-10 flex flex-col items-center text-center">
                  <motion.div
                    className="w-16 h-16 rounded-3xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300"
                    style={{
                      background: 'linear-gradient(135deg, #ff2d9750, #ff2d9730)',
                      boxShadow: '0 8px 32px #ff2d9740'
                    }}
                  >
                    <Icon icon="solar:gameboy-bold" width={32} className="text-[#ff2d97]" />
                  </motion.div>
                  <h4 className="text-white text-lg font-bold">经典模式</h4>
                </div>
              </motion.button>

              {/* 划屏模式按钮 */}
              <motion.button
                onClick={() => handleModeSelect('swipe')}
                className="group relative overflow-hidden rounded-3xl p-8 cursor-pointer bg-black/30 backdrop-blur-lg border border-white/20 hover:border-[#892fff]/60 hover:bg-[#892fff]/10 transition-all duration-500"
                whileHover={{ scale: 1.05, y: -5 }}
                whileTap={{ scale: 0.95 }}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.7 }}
              >
                {/* 背景光晕效果 */}
                <div className="absolute inset-0 overflow-hidden">
                  <motion.div
                    className="absolute -top-12 -right-12 w-32 h-32 rounded-full blur-2xl opacity-20 group-hover:opacity-40 transition-opacity duration-500"
                    style={{
                      background: 'linear-gradient(135deg, #892fff, #a855f7)'
                    }}
                    animate={{
                      scale: [1, 1.2, 1],
                      rotate: [360, 180, 0]
                    }}
                    transition={{
                      duration: 8,
                      repeat: Infinity,
                      ease: 'linear'
                    }}
                  />
                </div>

                {/* 内容区域 */}
                <div className="relative z-10 flex flex-col items-center text-center">
                  <motion.div
                    className="w-16 h-16 rounded-3xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300"
                    style={{
                      background: 'linear-gradient(135deg, #892fff50, #892fff30)',
                      boxShadow: '0 8px 32px #892fff40'
                    }}
                  >
                    <Icon icon="solar:hand-stars-bold" width={32} className="text-[#892fff]" />
                  </motion.div>
                  <h4 className="text-white text-lg font-bold">划屏模式</h4>
                </div>
              </motion.button>
            </div>
          </motion.div>

          {/* 收到的回应 - 更精美的设计 */}
          <AnimatePresence>
            {lastReaction && (
              <motion.div
                initial={{ opacity: 0, y: 30, scale: 0.8 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -30, scale: 0.8 }}
                transition={{ type: 'spring', stiffness: 200, damping: 20 }}
                className="w-full max-w-md bg-black/30 backdrop-blur-lg border border-white/20 rounded-3xl p-6 shadow-xl"
              >
                <div className="flex items-center gap-4">
                  {/* Emoji容器 - 更大更突出 */}
                  <motion.div
                    className="w-16 h-16 bg-gradient-to-br from-[#00ff88]/20 to-[#00d4aa]/20 rounded-3xl flex items-center justify-center border border-[#00ff88]/30"
                    initial={{ scale: 0, rotate: -180 }}
                    animate={{ scale: 1, rotate: 0 }}
                    transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
                  >
                    <motion.span
                      className="text-3xl"
                      animate={{
                        scale: [1, 1.2, 1],
                        rotate: [0, 10, -10, 0]
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: 'easeInOut'
                      }}
                    >
                      {lastReaction.emoji}
                    </motion.span>
                  </motion.div>

                  {/* 文字信息 */}
                  <div className="flex-1">
                    <motion.p
                      className="text-white text-base font-semibold mb-1"
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.3 }}
                    >
                      收到主人回应
                    </motion.p>
                    <motion.p
                      className="text-[#7c85b6] text-sm"
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.4 }}
                    >
                      {Math.floor((Date.now() - lastReaction.timestamp) / 1000)}秒前
                    </motion.p>
                  </div>

                  {/* 装饰光点 */}
                  <motion.div
                    className="w-3 h-3 bg-[#00ff88] rounded-full opacity-60"
                    animate={{
                      scale: [1, 1.5, 1],
                      opacity: [0.6, 1, 0.6]
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: 'easeInOut'
                    }}
                  />
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* 错误提示 */}
        {controller.error && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-4 p-4 bg-red-500/20 border border-red-500/30 rounded-2xl backdrop-blur-sm"
          >
            <div className="flex items-center gap-2">
              <Icon icon="solar:danger-triangle-bold" width={16} className="text-red-400" />
              <p className="text-red-400 text-sm">{controller.error}</p>
            </div>
          </motion.div>
        )}
      </div>

      {/* 经典模式底部弹出弹窗 */}
      <Modal
        isOpen={showClassicMode}
        onOpenChange={handleClassicModeClose}
        placement="bottom"
        hideCloseButton
        classNames={{
          base: 'bg-transparent shadow-none',
          wrapper: 'bg-transparent justify-end items-end',
          backdrop: 'bg-black/60 backdrop-blur-sm'
        }}
        motionProps={{
          variants: {
            enter: {
              y: 0,
              opacity: 1,
              transition: {
                duration: 0.3,
                ease: 'easeOut'
              }
            },
            exit: {
              y: '100%',
              opacity: 0,
              transition: {
                duration: 0.2,
                ease: 'easeIn'
              }
            }
          }
        }}
      >
        <ModalContent className="bg-transparent shadow-none m-0 rounded-t-3xl rounded-b-none max-h-[80vh] overflow-hidden w-full">
          <div className="relative">
            {/* 主要内容容器 */}
            <div className="relative z-10 backdrop-blur-xl rounded-t-3xl overflow-hidden bg-[#121521]">
              {/* 顶部拖拽指示器 */}
              <div className="relative z-10 flex justify-center pt-4 pb-2">
                <div className="w-10 h-1 bg-white/30 rounded-full" />
              </div>

              <ModalBody className="relative z-10 p-0">
                <RemoteDeviceControlProvider>
                  <ClassicModeControl
                    onBack={handleClassicModeClose}
                    showBackButton={true}
                    className="bg-transparent"
                  />
                </RemoteDeviceControlProvider>
              </ModalBody>
            </div>
          </div>
        </ModalContent>
      </Modal>

      {/* 退出确认对话框 */}
      {showExitConfirm && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-6">
          <motion.div
            className="bg-[#1d2135] rounded-3xl p-6 w-full max-w-sm border border-white/10"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            <div className="text-center mb-6">
              <Icon
                icon="solar:danger-triangle-bold"
                width={48}
                className="text-yellow-500 mx-auto mb-4"
              />
              <h3 className="text-white text-lg font-semibold mb-2">确认断开连接</h3>
              <p className="text-[#7c85b6] text-sm">断开连接将结束远程控制会话</p>
            </div>
            <div className="flex gap-3">
              <button
                onClick={() => setShowExitConfirm(false)}
                className="flex-1 h-12 bg-white/10 hover:bg-white/20 text-white rounded-2xl transition-colors"
              >
                取消
              </button>
              <button
                onClick={handleExitConfirm}
                className="flex-1 h-12 bg-red-500 hover:bg-red-600 text-white rounded-2xl transition-colors"
              >
                确认断开
              </button>
            </div>
          </motion.div>
        </div>
      )}

      {/* Emoji Toast */}
      <EmojiToast />
    </div>
  )
}
