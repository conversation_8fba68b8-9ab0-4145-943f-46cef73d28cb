import React, { useEffect } from 'react'
import { useNavigate } from 'react-router'
import { Icon } from '@iconify/react'
import { motion } from 'framer-motion'
import { App } from '@capacitor/app'
import { useRemoteControlStore } from '@/stores/remote-control-store'
import { EmojiToast } from '@/components/device/EmojiToast'
import { useSmartNavigation } from '@/hooks/use-smart-navigation'

export default function RemoteHostPage() {
  const { smartNavigate, goBack } = useSmartNavigation()
  const {
    host,
    actions: { generateControlCode, revokeControlCode }
  } = useRemoteControlStore()

  // 生成控制码
  const handleGenerateCode = async () => {
    try {
      await generateControlCode()
      // 生成成功后跳转到等待页面
      smartNavigate('/device/remote-host/waiting')
    } catch (error) {
      console.error('生成控制码失败:', error)
    }
  }

  // 监听硬件返回键（移动端）
  useEffect(() => {
    const handleBackButton = () => {
      // 在初始状态下直接允许返回
      goBack()
    }

    // 添加返回键监听
    App.addListener('backButton', handleBackButton)

    return () => {
      // 清理监听器
      App.removeAllListeners()
    }
  }, [goBack])

  return (
    <div className="min-h-screen bg-[#121521] relative overflow-hidden">
      {/* 背景装饰元素 */}
      <div className="absolute top-0 left-0 w-screen pointer-events-none">
        <img src="/images/device/bg.svg" className="w-full h-full" />
      </div>

      {/* 顶部标题栏 */}
      <div className="relative z-10 pt-16 pb-4">
        <div className="flex items-center justify-between px-6">
          <motion.button
            onClick={() => goBack()}
            className="w-10 h-10 flex items-center justify-center text-white bg-white/10 rounded-2xl hover:bg-white/20 transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Icon icon="solar:arrow-left-linear" width={20} />
          </motion.button>
          <h1 className="text-white text-xl font-semibold">远程模式</h1>
          <div className="w-10 h-10" /> {/* 占位符保持居中 */}
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="relative z-10 flex-1 px-6">
        <div className="flex flex-col items-center justify-center min-h-[60vh]">
          {/* 图标区域 */}
          <motion.div
            className="w-24 h-24 bg-gradient-to-br from-[#00ff88] to-[#00d4aa] rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg shadow-[#00ff88]/20"
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{ duration: 0.8, type: 'spring', stiffness: 100 }}
          >
            <Icon icon="solar:global-bold" width={48} className="text-black" />
          </motion.div>

          {/* 标题区域 */}
          <motion.div
            className="text-center mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <h2 className="text-white text-2xl font-bold mb-3">开启远程控制</h2>
            <p className="text-[#7c85b6] text-base leading-relaxed">
              生成控制码，让其他人远程控制你的设备
            </p>
          </motion.div>

          {/* 生成按钮 */}
          <motion.button
            onClick={handleGenerateCode}
            disabled={host.isGeneratingCode}
            className="w-full max-w-sm h-14 bg-gradient-to-r from-[#00ff88] to-[#00d4aa] hover:from-[#00e67a] hover:to-[#00c299] text-black text-lg font-semibold rounded-2xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg shadow-[#00ff88]/20"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            whileHover={{ scale: 1.02, y: -2 }}
            whileTap={{ scale: 0.98 }}
          >
            {host.isGeneratingCode ? (
              <div className="flex items-center justify-center gap-2">
                <motion.div
                  className="w-5 h-5 border-2 border-black border-t-transparent rounded-full"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                />
                生成中...
              </div>
            ) : (
              <div className="flex items-center justify-center gap-2">
                <Icon icon="solar:key-bold" width={24} />
                生成控制码
              </div>
            )}
          </motion.button>

          {/* 说明信息 */}
          <motion.div
            className="mt-8 w-full max-w-sm bg-black/20 backdrop-blur-sm border border-white/10 rounded-2xl p-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <div className="space-y-2 text-[#7c85b6] text-sm">
              <div className="flex items-center gap-2">
                <Icon icon="solar:clock-circle-bold" width={16} className="text-[#00ff88]" />
                <span>控制码30分钟内有效</span>
              </div>
              <div className="flex items-center gap-2">
                <Icon icon="solar:shield-check-bold" width={16} className="text-[#00ff88]" />
                <span>你可以随时断开连接</span>
              </div>
              <div className="flex items-center gap-2">
                <Icon icon="solar:gameboy-bold" width={16} className="text-[#00ff88]" />
                <span>支持经典模式和划屏模式</span>
              </div>
            </div>
          </motion.div>
        </div>

        {/* 错误提示 */}
        {host.error && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-4 p-4 bg-red-500/20 border border-red-500/30 rounded-2xl backdrop-blur-sm"
          >
            <div className="flex items-center gap-2">
              <Icon icon="solar:danger-triangle-bold" width={16} className="text-red-400" />
              <p className="text-red-400 text-sm">{host.error}</p>
            </div>
          </motion.div>
        )}
      </div>

      {/* Emoji Toast */}
      <EmojiToast />
    </div>
  )
}
