设备功能

1. 经典模式（已有）

2. 划屏模式
   用户手指划屏，有滑动的轨迹，根据滑动的速度触发不一样的强度

3. 波形库：在划屏模式下滑动的所有轨迹可以进行保存，用户可以在波形库找到对应的波形，然后自动触发轨迹动画，模拟人的真实滑动，同时触发广播。

4. 音乐模式
   音乐模式，就是上传音乐或者用在线音乐，监听音调高低，根据音调，触发不一样的强度

5. 视频模式
   同上，和音乐模式基本相同

6. 远程模式
   远程模式其实就是 A 用户生成一个码或者转发一个链接，然后发给别的用户，B 用户就可以来连接，然后通过控制经典模式或者划屏模式，来控制 A 用户的设备。

短广播指令（目前所有设备一样）：会自动停止， 按顺序会 1-9 的强度等级

```json
[
  "6db643ce97fe427cf41d7c",
  "6db643ce97fe427cf7864e",
  "6db643ce97fe427cf60f5f",
  "6db643ce97fe427cf1b02b",
  "6db643ce97fe427cf0393a",
  "6db643ce97fe427cf3a208",
  "6db643ce97fe427cf22b19",
  "6db643ce97fe427cfddce1",
  "6db643ce97fe427cfc55f0"
]
```

1. 交互样式设计
2. 交互逻辑设计

优先级划屏+波形，音乐模式+ 视频模式
