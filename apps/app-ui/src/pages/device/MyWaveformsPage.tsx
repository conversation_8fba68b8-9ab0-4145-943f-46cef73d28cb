import React, { useState, useEffect, useCallback } from 'react'
import { useNavigate } from 'react-router'
import { useTranslation } from 'react-i18next'
import { Icon } from '@iconify/react'
import { motion, AnimatePresence } from 'framer-motion'
import { addToast } from '@heroui/react'
import type { WaveformLibraryItem } from '@/types/swipe-mode'
import { WaveformCard, WaveformCardSkeleton } from '@/components/device/waveform/WaveformCard'
import { waveformStorageDexie } from '@/services/waveform-storage-dexie'

type TabType = 'created' | 'favorites'

export default function MyWaveformsPage() {
  const navigate = useNavigate()
  const { t } = useTranslation('device')

  // 状态管理
  const [activeTab, setActiveTab] = useState<TabType>('created')
  const [createdWaveforms, setCreatedWaveforms] = useState<WaveformLibraryItem[]>([])
  const [favoriteWaveforms, setFavoriteWaveforms] = useState<WaveformLibraryItem[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)

  // 加载我创建的波形
  const loadCreatedWaveforms = useCallback(async () => {
    try {
      const waveforms = await waveformStorageDexie.getMyWaveforms()
      setCreatedWaveforms(waveforms)
    } catch (error) {
      console.error('Failed to load created waveforms:', error)
      addToast({
        title: '加载失败',
        description: '无法加载我创建的波形',
        color: 'danger'
      })
    }
  }, [])

  // 加载我收藏的波形
  const loadFavoriteWaveforms = useCallback(async () => {
    try {
      const waveforms = await waveformStorageDexie.getFavoriteWaveforms()
      setFavoriteWaveforms(waveforms)
    } catch (error) {
      console.error('Failed to load favorite waveforms:', error)
      addToast({
        title: '加载失败',
        description: '无法加载收藏的波形',
        color: 'danger'
      })
    }
  }, [])

  // 加载数据
  const loadData = useCallback(
    async (isRefresh = false) => {
      try {
        if (isRefresh) {
          setRefreshing(true)
        } else {
          setLoading(true)
        }

        await Promise.all([loadCreatedWaveforms(), loadFavoriteWaveforms()])
      } finally {
        setLoading(false)
        setRefreshing(false)
      }
    },
    [loadCreatedWaveforms, loadFavoriteWaveforms]
  )

  // 处理播放波形
  const handlePlayWaveform = useCallback(
    (waveform: WaveformLibraryItem) => {
      navigate(`/device/swipe-mode?playback=${waveform.id}`)
    },
    [navigate]
  )

  // 处理点赞
  const handleLikeWaveform = useCallback(
    async (waveformId: string): Promise<void> => {
      try {
        const currentWaveforms = activeTab === 'created' ? createdWaveforms : favoriteWaveforms
        const waveform = currentWaveforms.find(w => w.id === waveformId)
        if (!waveform) return

        if (waveform.isLiked) {
          await waveformStorageDexie.unlikeWaveform(waveformId)
        } else {
          await waveformStorageDexie.likeWaveform(waveformId)
        }

        // 更新本地状态
        const updateWaveforms = (waveforms: WaveformLibraryItem[]) =>
          waveforms.map(w =>
            w.id === waveformId
              ? {
                  ...w,
                  isLiked: !w.isLiked,
                  likeCount: w.isLiked ? w.likeCount - 1 : w.likeCount + 1
                }
              : w
          )

        if (activeTab === 'created') {
          setCreatedWaveforms(updateWaveforms)
        } else {
          setFavoriteWaveforms(updateWaveforms)
        }

        addToast({
          title: waveform.isLiked ? '取消点赞' : '点赞成功',
          description: waveform.isLiked ? '已取消点赞' : '感谢你的支持！',
          color: 'success'
        })
      } catch (error) {
        console.error('Failed to like waveform:', error)
        addToast({
          title: '操作失败',
          description: '点赞操作失败，请稍后重试',
          color: 'danger'
        })
      }
    },
    [activeTab, createdWaveforms, favoriteWaveforms]
  )

  // 处理收藏
  const handleFavoriteWaveform = useCallback(
    async (waveformId: string): Promise<void> => {
      try {
        const currentWaveforms = activeTab === 'created' ? createdWaveforms : favoriteWaveforms
        const waveform = currentWaveforms.find(w => w.id === waveformId)
        if (!waveform) return

        if (waveform.isFavorited) {
          await waveformStorageDexie.unfavoriteWaveform(waveformId)
        } else {
          await waveformStorageDexie.favoriteWaveform(waveformId)
        }

        // 更新本地状态
        const updateWaveforms = (waveforms: WaveformLibraryItem[]) =>
          waveforms.map(w =>
            w.id === waveformId
              ? {
                  ...w,
                  isFavorited: !w.isFavorited,
                  favoriteCount: w.isFavorited ? w.favoriteCount - 1 : w.favoriteCount + 1
                }
              : w
          )

        if (activeTab === 'created') {
          setCreatedWaveforms(updateWaveforms)
        }

        // 如果是在收藏页面取消收藏，则从列表中移除
        if (activeTab === 'favorites' && waveform.isFavorited) {
          setFavoriteWaveforms(prev => prev.filter(w => w.id !== waveformId))
        } else if (activeTab === 'favorites') {
          setFavoriteWaveforms(updateWaveforms)
        }

        addToast({
          title: waveform.isFavorited ? '取消收藏' : '收藏成功',
          description: waveform.isFavorited ? '已取消收藏' : '已添加到收藏夹',
          color: 'success'
        })
      } catch (error) {
        console.error('Failed to favorite waveform:', error)
        addToast({
          title: '操作失败',
          description: '收藏操作失败，请稍后重试',
          color: 'danger'
        })
      }
    },
    [activeTab, createdWaveforms, favoriteWaveforms]
  )

  // 处理编辑波形（只能编辑名称）
  const handleEditWaveform = useCallback(async (updatedWaveform: WaveformLibraryItem) => {
    try {
      // 这里应该调用API更新波形名称
      // TODO: 实现波形名称更新API

      // 暂时更新本地状态
      setCreatedWaveforms(prev =>
        prev.map(w => (w.id === updatedWaveform.id ? updatedWaveform : w))
      )

      addToast({
        title: '更新成功',
        description: '波形名称已更新',
        color: 'success'
      })
    } catch (error) {
      console.error('Failed to update waveform:', error)
      addToast({
        title: '更新失败',
        description: '无法更新波形名称',
        color: 'danger'
      })
    }
  }, [])

  // 处理删除波形
  const handleDeleteWaveform = useCallback(async (waveformId: string): Promise<void> => {
    try {
      await waveformStorageDexie.deleteWaveform(waveformId)

      // 从本地状态中移除
      setCreatedWaveforms(prev => prev.filter(w => w.id !== waveformId))

      addToast({
        title: '删除成功',
        description: '波形已成功删除',
        color: 'success'
      })
    } catch (error) {
      console.error('Failed to delete waveform:', error)
      addToast({
        title: '删除失败',
        description: '删除波形时发生错误',
        color: 'danger'
      })
      throw error // 重新抛出错误，让弹窗知道删除失败
    }
  }, [])

  // 处理发布状态切换
  const handleTogglePublish = useCallback(
    async (waveformId: string) => {
      try {
        const waveform = createdWaveforms.find(w => w.id === waveformId)
        if (!waveform) return

        const newPublicState = !waveform.isPublic
        await waveformStorageDexie.togglePublish(waveformId, newPublicState)

        // 更新本地状态
        setCreatedWaveforms(prev =>
          prev.map(w =>
            w.id === waveformId
              ? {
                  ...w,
                  isPublic: newPublicState,
                  publishedAt: newPublicState ? Date.now() : undefined
                }
              : w
          )
        )

        addToast({
          title: newPublicState ? '发布成功' : '取消发布',
          description: newPublicState ? '波形已发布到公共广场' : '波形已设为私有',
          color: 'success'
        })
      } catch (error) {
        console.error('Failed to toggle publish:', error)
        addToast({
          title: '操作失败',
          description: '发布状态切换失败',
          color: 'danger'
        })
      }
    },
    [createdWaveforms]
  )

  // 初始化加载
  useEffect(() => {
    waveformStorageDexie.initialize().then(() => {
      loadData()
    })
  }, [loadData])

  // 获取当前显示的波形列表
  const currentWaveforms = activeTab === 'created' ? createdWaveforms : favoriteWaveforms

  return (
    <div className="min-h-screen bg-[#121521] relative overflow-hidden">
      {/* 背景装饰元素 */}
      <div className="absolute top-0 left-0 w-screen pointer-events-none">
        <img src="/images/device/bg.svg" className="w-full h-full" />
      </div>

      {/* 顶部标题栏 */}
      <div className="relative z-10 pt-12 pb-4">
        <div className="flex items-center justify-between px-4 relative">
          <motion.button
            onClick={() => navigate(-1)}
            className="w-11 h-11 flex items-center justify-center text-white/80 bg-white/8 backdrop-blur-sm border border-white/10 rounded-2xl active:bg-white/12 transition-all duration-200"
            whileTap={{ scale: 0.95 }}
          >
            <Icon icon="solar:arrow-left-linear" width={20} />
          </motion.button>

          <h1 className="text-white text-xl font-semibold absolute left-1/2 -translate-x-1/2">
            我的波形
          </h1>

          {/* <motion.button
            onClick={() => loadData(true)}
            className="w-10 h-10 flex items-center justify-center text-white bg-white/10 rounded-2xl hover:bg-white/20 transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            disabled={refreshing}
          >
            <Icon
              icon="solar:refresh-linear"
              width={20}
              className={refreshing ? 'animate-spin' : ''}
            />
          </motion.button> */}
        </div>
      </div>

      {/* 标签页切换 */}
      <div className="relative z-10 px-4 mb-4">
        <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-2">
          <div className="flex gap-1">
            {[
              { key: 'created' as TabType, label: '我创建的' },
              { key: 'favorites' as TabType, label: '我收藏的' }
            ].map(tab => (
              <motion.button
                key={tab.key}
                onClick={() => setActiveTab(tab.key)}
                className={`flex-1 flex items-center justify-center gap-2 px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 ${
                  activeTab === tab.key
                    ? 'bg-gradient-to-r from-[#ff2d97] to-[#892fff] text-white shadow-lg shadow-[#ff2d97]/25'
                    : 'text-white/70 hover:text-white hover:bg-white/8'
                }`}
                whileHover={{ scale: activeTab === tab.key ? 1 : 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <span>{tab.label}</span>
                <div
                  className={`px-2 py-0.5 rounded-full text-xs ${
                    activeTab === tab.key ? 'bg-white/20 text-white' : 'bg-white/10 text-white/60'
                  }`}
                >
                  {tab.key === 'created' ? createdWaveforms.length : favoriteWaveforms.length}
                </div>
              </motion.button>
            ))}
          </div>
        </div>
      </div>

      {/* 波形列表 */}
      <div className="relative z-10 flex-1 px-4 pb-6">
        <AnimatePresence mode="wait">
          {loading ? (
            /* 骨架屏 */
            <motion.div
              key="skeleton"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="space-y-4"
            >
              {Array.from({ length: 4 }).map((_, index) => (
                <WaveformCardSkeleton key={index} />
              ))}
            </motion.div>
          ) : currentWaveforms.length > 0 ? (
            /* 波形列表 */
            <motion.div
              key={`${activeTab}-waveforms`}
              initial={{ opacity: 0, x: activeTab === 'created' ? -20 : 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: activeTab === 'created' ? 20 : -20 }}
              className="space-y-4"
            >
              {currentWaveforms.map((waveform, index) => (
                <motion.div
                  key={waveform.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <WaveformCard
                    waveform={waveform}
                    onPlay={handlePlayWaveform}
                    onLike={handleLikeWaveform}
                    onFavorite={activeTab === 'favorites' ? handleFavoriteWaveform : undefined}
                    onEdit={activeTab === 'created' ? handleEditWaveform : undefined}
                    onDelete={activeTab === 'created' ? handleDeleteWaveform : undefined}
                    showOwnerActions={activeTab === 'created'}
                  />

                  {/* 发布状态切换按钮 - 仅在"我创建的"标签显示 */}
                  {activeTab === 'created' && (
                    <motion.div
                      className="mt-2 flex justify-end"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.2 }}
                    >
                      <motion.button
                        onClick={() => handleTogglePublish(waveform.id)}
                        className={`flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                          waveform.isPublic
                            ? 'bg-green-500/20 text-green-400 hover:bg-green-500/30'
                            : 'bg-gray-500/20 text-gray-400 hover:bg-gray-500/30'
                        }`}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <Icon
                          icon={waveform.isPublic ? 'solar:eye-bold' : 'solar:eye-closed-bold'}
                          width={16}
                        />
                        <span>{waveform.isPublic ? '已发布' : '私有'}</span>
                      </motion.button>
                    </motion.div>
                  )}
                </motion.div>
              ))}
            </motion.div>
          ) : (
            /* 空状态 */
            <motion.div
              key={`${activeTab}-empty`}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="flex-1 flex items-center justify-center"
            >
              <div className="text-center">
                <div className="w-20 h-20 bg-[#1d2135] rounded-full flex items-center justify-center mb-4 mx-auto">
                  <Icon
                    icon={activeTab === 'created' ? 'solar:add-circle-bold' : 'solar:star-bold'}
                    width={40}
                    className="text-[#00ff88]"
                  />
                </div>
                <h2 className="text-white text-xl font-semibold mb-2">
                  {activeTab === 'created' ? '还没有创建波形' : '还没有收藏波形'}
                </h2>
                <p className="text-[#7c85b6] text-base mb-4">
                  {activeTab === 'created'
                    ? '去划屏模式创建你的第一个波形吧'
                    : '去波形广场发现有趣的波形'}
                </p>
                <motion.button
                  onClick={() =>
                    navigate(
                      activeTab === 'created' ? '/device/swipe-mode' : '/device/waveform-library'
                    )
                  }
                  className="px-6 py-3 bg-gradient-to-r from-[#00d4ff] to-[#892fff] rounded-full text-white font-medium"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {activeTab === 'created' ? '开始创建' : '去发现'}
                </motion.button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* 下拉刷新指示器 */}
      {refreshing && (
        <div className="absolute top-20 left-1/2 transform -translate-x-1/2 z-20">
          <div className="bg-black/50 backdrop-blur-sm rounded-full px-4 py-2 flex items-center gap-2">
            <Icon icon="solar:refresh-linear" width={16} className="text-white animate-spin" />
            <span className="text-white text-sm">刷新中...</span>
          </div>
        </div>
      )}
    </div>
  )
}
