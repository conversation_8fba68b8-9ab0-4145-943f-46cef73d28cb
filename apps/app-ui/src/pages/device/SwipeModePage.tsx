import React, { useRef, useCallback, useState, useEffect } from 'react'
import { useNavigate, useSearchParams } from 'react-router'
import { useTranslation } from 'react-i18next'
import { Icon } from '@iconify/react'
import { motion, AnimatePresence } from 'framer-motion'
import { SwipeCanvas, type SwipeCanvasRef } from '@/components/device/swipe-mode/SwipeCanvas'
import { WaveformViewer } from '@/components/device/waveform/WaveformViewer'
import { WaveformPlaybackProgress } from '@/components/device/waveform/WaveformPlaybackProgress'
import { useWaveformData } from '@/hooks/useWaveformData'
import { useRecording } from '@/hooks/use-recording'
import { usePlayback } from '@/hooks/use-playback'
import { waveformStorageDexie } from '@/services/waveform-storage-dexie'
import SaveSuccessModal from '@/components/device/swipe-mode/SaveSuccessModal'
import PlaybackStartScreen from '@/components/device/waveform/PlaybackStartScreen'
import PlaybackCompletedScreen from '@/components/device/waveform/PlaybackCompletedScreen'
import Loading from '@/components/common/loading'
import type { WaveformRecording } from '@/types/recording'

import { useSmartNavigation } from '@/lib/navigation'
import { useDeviceControl } from '@/contexts/device-control-context'

export default function SwipeModePage() {
  const { goBack } = useSmartNavigation()
  const { t } = useTranslation('device')
  const [searchParams] = useSearchParams()
  const canvasRef = useRef<SwipeCanvasRef>(null)
  const completedTracksRef = useRef<import('@/types/swipe-mode').TrackPoint[][]>([]) // 稳定的空数组引用

  // 检查是否为播放模式
  const playbackRecordingId = searchParams.get('playback')
  const isPlaybackMode = !!playbackRecordingId

  // 检查是否为远程模式
  const isRemoteMode = searchParams.get('remote') === 'true'

  // 获取设备控制上下文（可能为空，如果不在Provider中）
  let deviceControl = null
  try {
    deviceControl = useDeviceControl()
  } catch (error) {
    // 不在DeviceControlProvider中，使用默认行为
  }

  // 使用新的波形数据hook
  const waveformData = useWaveformData({
    mode: 'swipe',
    isRemoteMode: isRemoteMode
  })

  // 保存成功弹窗状态
  const [showSaveModal, setShowSaveModal] = useState(false)
  const [savedWaveform, setSavedWaveform] = useState<
    import('@/types/swipe-mode').WaveformLibraryItem | null
  >(null)

  // 录制模式Hook
  const recording = useRecording({
    waveformData,
    onSaveSuccess: waveform => {
      // 远程模式下不显示保存成功弹窗
      if (!isRemoteMode && deviceControl?.canSave !== false) {
        setSavedWaveform(waveform)
        setShowSaveModal(true)
      }
    }
  })

  // 加载录制数据的函数
  const loadRecording = useCallback(async (id: string): Promise<WaveformRecording | null> => {
    try {
      await waveformStorageDexie.initialize()
      const waveform = await waveformStorageDexie.getWaveformDataById(id)

      console.log('🔍 查找录制数据:', {
        id,
        waveform: !!waveform,
        hasRecordingData: !!waveform?.recordingData,
        recordingDataType: typeof waveform?.recordingData,
        waveformKeys: waveform ? Object.keys(waveform) : []
      })

      if (waveform && waveform.recordingData) {
        // 检查是否是有效的录制数据格式
        if (typeof waveform.recordingData === 'object' && 'points' in waveform.recordingData) {
          console.log('✅ 找到有效的录制数据')
          return waveform.recordingData as WaveformRecording
        } else {
          console.error('❌ 录制数据格式无效:', waveform.recordingData)
        }
      } else {
        console.error('❌ 未找到录制数据或数据为空')
      }

      return null
    } catch (error) {
      console.error('💥 加载录制数据失败:', error)
      return null
    }
  }, [])

  // 回放模式Hook（只在回放模式下初始化）
  const playback = usePlayback({
    recordingId: playbackRecordingId || '',
    waveformData: isPlaybackMode ? waveformData : undefined,
    loadRecording: isPlaybackMode ? loadRecording : undefined,
    onComplete: () => {
      console.log('回放完成')
    },
    onError: error => {
      console.error('回放错误:', error)
      goBack()
    }
  })

  // 初始化画布尺寸
  useEffect(() => {
    const updateCanvasSize = () => {
      // 暂时使用固定尺寸，后续优化
      const width = window.innerWidth
      const height = window.innerHeight * 0.6 // 估算画布高度

      // 安全调用初始化函数
      try {
        recording.initializeCanvas(width, height)
        playback.initializeCanvas(width, height)
        console.log('📐 画布尺寸初始化:', { width, height })
      } catch (error) {
        console.warn('画布尺寸初始化失败:', error)
      }
    }

    // 延迟初始化以确保canvas已渲染
    const timer = setTimeout(updateCanvasSize, 100)

    // 监听窗口大小变化
    window.addEventListener('resize', updateCanvasSize)

    return () => {
      clearTimeout(timer)
      window.removeEventListener('resize', updateCanvasSize)
    }
  }, []) // 只在组件挂载时执行一次

  // 继续划屏（重置数据）
  const handleContinueSwipe = useCallback(() => {
    canvasRef.current?.clear()
    recording.actions.resetRecording()
    setShowSaveModal(false)
    console.log('重置划屏数据，继续创作')
  }, [recording.actions])

  // 防止页面滚动和拖动
  useEffect(() => {
    const preventScroll = (e: TouchEvent) => {
      e.preventDefault()
    }

    const preventDrag = (e: Event) => {
      e.preventDefault()
    }

    // 防止触摸滚动
    document.addEventListener('touchmove', preventScroll, { passive: false })
    // 防止拖拽
    document.addEventListener('dragstart', preventDrag)
    document.addEventListener('selectstart', preventDrag)

    // 设置body样式
    document.body.style.overflow = 'hidden'
    document.body.style.position = 'fixed'
    document.body.style.width = '100%'
    document.body.style.height = '100%'

    return () => {
      document.removeEventListener('touchmove', preventScroll)
      document.removeEventListener('dragstart', preventDrag)
      document.removeEventListener('selectstart', preventDrag)

      // 恢复body样式
      document.body.style.overflow = ''
      document.body.style.position = ''
      document.body.style.width = ''
      document.body.style.height = ''
    }
  }, [])

  // 快捷键支持
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 's':
            e.preventDefault()
            if (!isPlaybackMode && recording.recordingState.hasUnsavedData) {
              recording.actions.stopRecording()
            }
            break
          case 'Delete':
          case 'Backspace':
            e.preventDefault()
            if (!isPlaybackMode) {
              recording.actions.resetRecording()
              canvasRef.current?.clear()
            }
            break
        }
      }
    }

    window.addEventListener('keydown', handleKeyPress)
    return () => window.removeEventListener('keydown', handleKeyPress)
  }, [isPlaybackMode, recording.actions, recording.recordingState.hasUnsavedData])

  return (
    <div className="h-screen bg-[#121521] relative overflow-hidden flex flex-col touch-none">
      {/* 背景装饰元素 */}
      <div className="absolute top-0 left-0 w-screen pointer-events-none">
        <img src="/images/device/bg.svg" className="w-full h-full" />
      </div>

      {/* 回放开始界面 */}
      {isPlaybackMode && playback.isReady && playback.playbackState.recording && (
        <PlaybackStartScreen
          recording={playback.playbackState.recording}
          onStart={playback.actions.start}
          onCancel={() => goBack()}
          isLoading={playback.isLoading}
        />
      )}

      {/* 回放完成界面 */}
      {isPlaybackMode && playback.isCompleted && playback.playbackState.recording && (
        <PlaybackCompletedScreen
          recording={playback.playbackState.recording}
          onRestart={playback.actions.restart}
          onBack={() => goBack()}
        />
      )}

      {/* 顶部标题栏 */}
      <div className="relative z-10 pt-12 pb-6 flex-shrink-0">
        <div className="flex items-center justify-between px-6">
          <motion.button
            onClick={() => goBack()}
            className="w-10 h-10 flex items-center justify-center text-white hover:bg-white/20 transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Icon icon="solar:arrow-left-linear" width={20} />
          </motion.button>

          <h1 className="text-white text-xl font-semibold absolute left-1/2 -translate-x-1/2">
            {isPlaybackMode ? '波形播放' : isRemoteMode ? '远程划屏' : '划屏模式'}
          </h1>

          {/* 右侧按钮区域 */}
          <AnimatePresence>
            {!isPlaybackMode &&
            recording.recordingState.hasUnsavedData &&
            deviceControl?.canSave !== false ? (
              <motion.button
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                onClick={recording.actions.stopRecording}
                disabled={recording.isSaving}
                className={`flex items-center gap-2 px-4 py-2 rounded-full text-white text-sm font-medium transition-colors ${
                  recording.isSaving
                    ? 'bg-[#ff2d97]/50 cursor-not-allowed'
                    : 'bg-[#ff2d97] hover:bg-[#ff2d97]/80'
                }`}
                whileHover={recording.isSaving ? {} : { scale: 1.05 }}
                whileTap={recording.isSaving ? {} : { scale: 0.95 }}
              >
                {recording.isSaving ? (
                  <>
                    <Loading size="sm" variant="white" />
                    <span>保存中...</span>
                  </>
                ) : (
                  <>
                    <Icon icon="solar:diskette-bold" width={16} />
                    <span>保存</span>
                  </>
                )}
              </motion.button>
            ) : (
              <div className="w-[72px]" />
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* 触控绘制区域 - 占据大部分空间 */}
      <div className="relative z-10 flex-1 overflow-hidden">
        <SwipeCanvas
          ref={canvasRef}
          onTrackStart={() => {
            if (!isPlaybackMode) {
              recording.actions.updateIntensity(1)
            }
          }}
          onTrackUpdate={async (points, intensity) => {
            if (!isPlaybackMode && points.length > 0) {
              const lastPoint = points[points.length - 1]
              recording.actions.updateTouch(lastPoint.x, lastPoint.y, lastPoint.pressure || 1)
              recording.actions.updateIntensity(intensity)
            }
          }}
          onTrackEnd={async () => {
            if (!isPlaybackMode) {
              recording.actions.endTouch()
            }
          }}
          completedTracks={completedTracksRef.current}
          disabled={recording.isSaving}
          hasStartedRecording={recording.recordingState.hasStarted}
          isPlaybackMode={isPlaybackMode}
          playbackPosition={isPlaybackMode ? playback.playbackState.currentPosition : null}
          playbackIntensity={isPlaybackMode ? playback.playbackState.currentIntensity : 0}
        />

        {/* 竖直强度条 - 悬浮在左侧 */}
        <div className="absolute left-2 top-1/2 transform -translate-y-1/2 pointer-events-none z-20">
          <div className="flex flex-col items-center gap-3">
            {/* 强度数值 */}
            <div className="bg-black/70 backdrop-blur-sm rounded-full px-3 py-2 text-white text-lg font-bold shadow-lg border border-white/20 min-w-[50px] flex items-center justify-center">
              {isPlaybackMode
                ? playback.playbackState.currentIntensity || 0
                : recording.recordingState.currentIntensity || 0}
            </div>

            {/* 竖直进度条外框 */}
            <div className="relative">
              {/* 背景轨道 */}
              <div className="w-5 h-60 bg-gradient-to-t from-gray-800 via-gray-700 to-gray-600 rounded-full border border-white/20 overflow-hidden shadow-inner relative">
                {/* 渐变填充 */}
                <motion.div
                  className="w-full bg-gradient-to-t from-[#00d4ff] via-[#892fff] to-[#ff2d97] rounded-full shadow-lg"
                  animate={{
                    height: `${
                      ((isPlaybackMode
                        ? playback.playbackState.currentIntensity
                        : recording.recordingState.currentIntensity) /
                        9) *
                      100
                    }%`
                  }}
                  transition={{ duration: 0.2, ease: 'easeOut' }}
                  style={{
                    position: 'absolute',
                    bottom: 0,
                    left: 0,
                    right: 0
                  }}
                />

                {/* 高光效果 */}
                {(isPlaybackMode
                  ? playback.playbackState.currentIntensity
                  : recording.recordingState.currentIntensity) > 0 && (
                  <motion.div
                    className="absolute inset-x-0 bg-white/20 rounded-full"
                    animate={{
                      height: `${
                        ((isPlaybackMode
                          ? playback.playbackState.currentIntensity
                          : recording.recordingState.currentIntensity) /
                          9) *
                        100
                      }%`,
                      opacity:
                        (isPlaybackMode
                          ? playback.playbackState.currentIntensity
                          : recording.recordingState.currentIntensity) > 6
                          ? 0.6
                          : 0.3
                    }}
                    transition={{ duration: 0.2, ease: 'easeOut' }}
                    style={{
                      bottom: 0,
                      width: '2px',
                      left: '1px'
                    }}
                  />
                )}
              </div>

              {/* 强度刻度 */}
              <div className="absolute -right-7 top-0 h-full flex flex-col justify-between text-white/40 text-xs font-medium">
                <span>9</span>
                <span>5</span>
                <span>1</span>
              </div>
            </div>

            {/* 强度标签 */}
            <div className="text-white/60 text-xs font-medium">强度</div>
          </div>
        </div>
      </div>

      {/* 底部波形区域 */}
      <div className="relative z-10 pb-4 flex-shrink-0">
        <div className="px-2">
          {isPlaybackMode ? (
            // 回放模式：使用累积式进度显示
            <WaveformPlaybackProgress
              recording={playback.playbackState.recording}
              currentTime={playback.playbackState.currentTime}
              height={64}
              className="w-full"
            />
          ) : (
            // 录制模式：使用实时波形显示
            <WaveformViewer
              data={waveformData.getWaveformData()}
              currentIntensity={recording.recordingState.currentIntensity}
              height={64}
              showCurrentIndicator={recording.recordingState.isRecording}
              showControls={true}
              emptyMessage="实时波形"
              className="w-full"
            />
          )}
        </div>
      </div>

      {/* 保存成功弹窗 */}
      <SaveSuccessModal
        isOpen={showSaveModal}
        onClose={() => setShowSaveModal(false)}
        waveform={savedWaveform || undefined}
        onContinue={handleContinueSwipe}
      />
    </div>
  )
}
