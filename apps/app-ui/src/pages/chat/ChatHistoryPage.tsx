import { useNavigate } from 'react-router'
import {
  Button,
  useDisclosure,
  Skeleton,
  Card,
  CardBody,
  addToast,
  Drawer,
  Drawer<PERSON>ontent,
  DrawerHeader,
  DrawerB<PERSON>
} from '@heroui/react'
import { Icon } from '@iconify/react'
import { useEffect, useState, useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import { ChatHistoryGroup } from '@/components/chat-history/chat-history-group'
import { groupChatsByDate } from '@/lib/chat-history-utils'
import { apiService } from '@/api'
import type { ChatHistory } from '@/lib/types'
import { useRoleStore } from '@/stores/role-store'
import { useUserCharactersStore } from '@/stores/user-characters-store'
import { useChatHistory } from '@/hooks/use-chat-history'
import GradientModal from '@/components/common/gradient-modal'

// 角色信息缓存相关常量和工具函数
const ROLE_CACHE_KEY = 'role_cache'

const getCachedRoles = (): Record<string, { name: string; avatar: string }> => {
  try {
    const cached = sessionStorage.getItem(ROLE_CACHE_KEY)
    return cached ? JSON.parse(cached) : {}
  } catch {
    return {}
  }
}

const setCachedRoles = (roles: Record<string, { name: string; avatar: string }>) => {
  try {
    sessionStorage.setItem(ROLE_CACHE_KEY, JSON.stringify(roles))
  } catch (error) {
    console.warn('保存角色缓存失败:', error)
  }
}

export default function ChatHistoryPage() {
  const navigate = useNavigate()
  const { t } = useTranslation('chat-history')
  const [deleteId, setDeleteId] = useState<string | null>(null)
  const [selectedChatForAction, setSelectedChatForAction] = useState<ChatHistory | null>(null)

  // ActionSheet 控制
  const {
    isOpen: isActionSheetOpen,
    onOpen: openActionSheet,
    onClose: closeActionSheet,
    onOpenChange: setActionSheetOpen
  } = useDisclosure()

  // 删除确认模态框控制
  const {
    isOpen: showDeleteDialog,
    onOpen: openDeleteDialog,
    onClose: closeDeleteDialog,
    onOpenChange: setShowDeleteDialog
  } = useDisclosure()

  // 🚀 使用新的 SQLite 优先的聊天历史 Hook
  const { chatHistory, isLoading, error, deleteChat: deleteChatFromHook } = useChatHistory()

  // 角色信息缓存 - 保持现有的角色缓存逻辑
  const [roleCache, setRoleCache] = useState<Record<string, { name: string; avatar: string }>>(() =>
    getCachedRoles()
  )

  // 使用全局角色状态管理
  const { setRole: setGlobalRole } = useRoleStore()

  // 批量获取角色信息 - 保持现有逻辑
  const fetchRoleInfo = useCallback(
    async (roleIds: string[]) => {
      try {
        const newCache = { ...roleCache }
        const idsToFetch = roleIds.filter(id => !roleCache[id])

        // 如果所有角色信息都已缓存，则不再请求
        if (idsToFetch.length === 0) {
          return
        }

        // 1. 使用Zustand store获取用户自定义角色
        let userCharacters: Array<{ id: string; name: string; imageUrl?: string }> = []
        try {
          const userCharactersStore = useUserCharactersStore.getState()
          const characters = await userCharactersStore.fetchUserCharacters()
          userCharacters = (characters || []) as any[]
        } catch (error) {
          console.error(t('log.get_user_roles_failed'), error)
        }

        // 2. 从自定义角色中匹配需要的角色
        const foundInUserChars: Array<{ id: string; name: string; avatar: string }> = []
        const remainingIds: string[] = []

        idsToFetch.forEach(roleId => {
          const char = userCharacters.find(c => c.id === roleId)
          if (char) {
            foundInUserChars.push({
              id: roleId,
              name: char.name,
              avatar: char.imageUrl || '/images/roles/default.jpg'
            })
          } else {
            remainingIds.push(roleId)
          }
        })

        // 3. 对于剩余的ID，批量获取系统角色
        const systemRolePromises = remainingIds.map(async roleId => {
          try {
            const data = await apiService.roles.getSystemRole(roleId)
            return {
              id: roleId,
              name: data.character || t('item.unknown_role'),
              avatar: data.avatar || '/images/roles/default.jpg'
            }
          } catch (error) {
            return {
              id: roleId,
              name: t('item.unknown_role'),
              avatar: '/images/roles/default.jpg'
            }
          }
        })

        const systemRoles = await Promise.all(systemRolePromises)

        // 4. 合并结果并更新缓存
        const allResults = [...foundInUserChars, ...systemRoles]
        allResults.forEach(result => {
          newCache[result.id] = {
            name: result.name,
            avatar: result.avatar
          }
        })

        setRoleCache(newCache)
        setCachedRoles(newCache) // 同步保存到sessionStorage
      } catch (error) {
        console.error(t('log.batch_get_role_info_failed'), error)
      }
    },
    [roleCache]
  )

  // 确保角色信息完整
  const ensureRoleInfo = useCallback(
    async (chats: ChatHistory[]) => {
      const uniqueRoleIds = [...new Set(chats.map(chat => chat.roleId).filter(Boolean))]
      // 获取当前最新的角色缓存（从sessionStorage和当前状态中取最新的）
      const currentRoleCache = { ...getCachedRoles(), ...roleCache }
      const missingRoleIds = uniqueRoleIds.filter(roleId => !currentRoleCache[roleId])

      if (missingRoleIds.length > 0) {
        await fetchRoleInfo(missingRoleIds as string[])
      }
    },
    [roleCache, fetchRoleInfo]
  )

  // 监听聊天历史数据变化，确保角色信息完整
  useEffect(() => {
    if (chatHistory.length > 0) {
      ensureRoleInfo(chatHistory)
    }
  }, [chatHistory, ensureRoleInfo])

  // 处理长按聊天项
  const handleChatLongPress = useCallback(
    (chat: ChatHistory) => {
      setSelectedChatForAction(chat)
      openActionSheet()
    },
    [openActionSheet]
  )

  // 处理聊天选择
  const handleChatSelect = async (chatId: string, roleId: string) => {
    try {
      // 先设置全局角色状态
      await setGlobalRole(roleId)
      // 然后导航到聊天页面
      navigate(`/chat/${chatId}?role=${roleId}`)
    } catch (error) {
      console.error(t('log.set_role_failed'), error)
      // 即使设置角色失败，也尝试导航
      navigate(`/chat/${chatId}?role=${roleId}`)
    }
  }

  // 准备删除聊天记录
  const handleDeletePrepare = () => {
    if (selectedChatForAction) {
      setDeleteId(selectedChatForAction.id)
      closeActionSheet() // 关闭 ActionSheet
      openDeleteDialog() // 打开删除确认对话框
    }
  }

  // 处理删除聊天
  const handleDelete = async () => {
    if (!deleteId) return

    try {
      await deleteChatFromHook(deleteId)
      addToast({
        title: t('delete.success'),
        color: 'primary'
      })
    } catch (error) {
      console.error(t('log.delete_chat_error'), error)
      addToast({
        title: t('delete.error'),
        color: 'danger'
      })
    } finally {
      closeDeleteDialog()
      setDeleteId(null)
      setSelectedChatForAction(null)
    }
  }

  // 取消删除操作
  const handleCancelDelete = () => {
    closeDeleteDialog()
    setDeleteId(null)
    setSelectedChatForAction(null)
  }

  // 关闭 ActionSheet
  const handleCloseActionSheet = () => {
    closeActionSheet()
    setSelectedChatForAction(null)
  }

  // 按日期分组聊天历史
  const groupedChats = groupChatsByDate(chatHistory)

  // 格式化时间的函数
  const formatTime = (dateStr: string) => {
    const date = new Date(dateStr)
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="container px-4 py-6 pb-20 max-w-4xl mx-auto relative bg-background">
      {/* <div className="flex items-center justify-center gap-3 mb-6 pt-10">
        <h1 className="text-2xl font-light">{t('page.title')}</h1>
      </div> */}

      <div className="absolute top-0 left-0 w-full">
        <img
          src="/images/history/bg.svg"
          alt="chat-history-bg"
          className="w-full h-full pointer-events-none"
        />
      </div>

      {/* 错误状态显示 */}
      {error && !isLoading && (
        <Card className="w-full mb-4">
          <CardBody className="flex flex-col items-center justify-center py-8 text-center">
            <div className="w-16 h-16 rounded-full bg-danger/10 flex items-center justify-center mb-4">
              <Icon icon="solar:wifi-router-minimalistic-bold" className="text-3xl text-danger" />
            </div>
            <h2 className="text-xl font-semibold mb-2 text-danger">{t('page.error.title')}</h2>
            <p className="text-default-500 mb-4">{error}</p>
            <Button
              color="primary"
              variant="solid"
              onPress={() => window.location.reload()}
              startContent={<Icon icon="solar:refresh-bold" width={18} />}
            >
              {t('page.error.retry')}
            </Button>
          </CardBody>
        </Card>
      )}

      {isLoading ? (
        <div className="space-y-4">
          {[1, 2, 3].map(placeholder => (
            <Card key={placeholder} className="w-full">
              <CardBody className="p-4">
                <div className="flex items-center gap-4">
                  <Skeleton className="w-12 h-12 rounded-full" />
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-3 w-1/2" />
                  </div>
                  <Skeleton className="h-3 w-16" />
                </div>
              </CardBody>
            </Card>
          ))}
        </div>
      ) : chatHistory.length > 0 ? (
        <div className="space-y-6">
          {Object.entries(groupedChats).map(([date, chats]) => (
            <ChatHistoryGroup
              key={date}
              title={date}
              chats={chats}
              formatTime={formatTime}
              onSelect={handleChatSelect}
              onLongPress={handleChatLongPress}
              roleCache={roleCache}
            />
          ))}
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center py-16 mt-20 text-center">
          <div className="w-24 h-24 flex items-center justify-center mb-4">
            <img src="/images/history/empty.svg" alt="empty" className="w-full h-full" />
          </div>
          <h2 className="text-xl mb-2">{t('page.empty.title')}</h2>
          <p className="text-default-500 mb-6 text-sm">{t('page.empty.description')}</p>
          <Button
            color="primary"
            className="w-40 h-12 rounded-3xl"
            onPress={() => navigate('/roles/custom')}
          >
            {t('page.empty.explore_roles')}
          </Button>
        </div>
      )}

      {/* ActionSheet - 长按聊天项后显示的操作菜单 */}
      <Drawer
        isOpen={isActionSheetOpen}
        onOpenChange={setActionSheetOpen}
        placement="bottom"
        hideCloseButton
        classNames={{
          base: 'max-h-[40vh] select-none',
          backdrop: 'bg-black/50'
        }}
      >
        <DrawerContent className="bg-background rounded-t-2xl border-t border-divider">
          <DrawerHeader className="flex flex-col gap-1 px-6 py-4 border-b border-divider">
            <div className="flex items-center justify-between w-full">
              <h3 className="text-lg font-semibold">{t('actionSheet.title')}</h3>
              <Button
                isIconOnly
                variant="light"
                size="sm"
                onPress={handleCloseActionSheet}
                className="text-default-400 hover:text-default-600"
              >
                <Icon icon="solar:close-circle-linear" width={24} />
              </Button>
            </div>
            {selectedChatForAction && (
              <div className="flex items-center gap-3 mt-2">
                <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                  <Icon icon="solar:chat-round-line-linear" className="text-primary" width={16} />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium truncate">
                    {roleCache[selectedChatForAction.roleId]?.name || t('item.unknown_role')}
                  </p>
                  <p className="text-xs text-default-500 truncate">
                    {selectedChatForAction.title ||
                      selectedChatForAction.lastMessage ||
                      t('item.no_content')}
                  </p>
                </div>
              </div>
            )}
          </DrawerHeader>
          <DrawerBody className="px-6 py-4">
            <div className="space-y-2">
              <Button
                variant="light"
                className="w-full h-12 text-danger hover:bg-danger/10"
                onPress={handleDeletePrepare}
              >
                {t('actionSheet.delete')}
              </Button>
            </div>
          </DrawerBody>
        </DrawerContent>
      </Drawer>

      {/* 删除确认对话框 - 使用 GradientModal */}
      <GradientModal
        isOpen={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        title={t('delete.title')}
        confirmText={t('delete.confirm')}
        cancelText={t('delete.cancel')}
        onConfirm={handleDelete}
        onCancel={handleCancelDelete}
      >
        {/* <div className="flex items-center justify-center gap-3 mb-4"> */}
        <p className="text-white/50 text-center text-sm">{t('delete.description')}</p>
        {/* </div> */}
      </GradientModal>
    </div>
  )
}
