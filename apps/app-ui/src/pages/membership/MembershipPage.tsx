import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router'
import { useTranslation } from 'react-i18next'
import { useMembershipData } from '@/hooks/use-membership'
import { cn } from '@/lib/utils'
import { paymentService } from '@/api/services/payment'
import type { PaymentOrder } from '@/api/services/payment'
import { LoadingOverlay } from '@/components/common/loading'
// HeroUI 组件
import {
  Card,
  CardBody,
  Button,
  addToast,
  Progress,
  useDisclosure,
  Drawer,
  DrawerContent,
  DrawerBody,
  DrawerFooter
} from '@heroui/react'

// Iconify 图标
import { Icon } from '@iconify/react'

// 本地图标资源路径
const iconAssets = {
  vipPro: '/images/vip/pro.png',
  vipVectorPro: '/images/vip/vector.svg',
  point: '/images/vip/point.svg'
}

const vipIcons = {
  pro: {
    icon: '/images/vip/pro.png',
    bgIcon: '/images/vip/vector-pro.svg',
    interests: [
      { name: 'max<PERSON><PERSON>cters', icon: '/images/vip/i-1.svg', key: 'maxCharacters' },
      { name: 'unlimitedChat', icon: '/images/vip/i-2.svg' },
      { name: 'imageGeneration', icon: '/images/vip/i-3.svg' },
      { name: 'voiceGeneration', icon: '/images/vip/i-4.svg' }
    ]
  },
  elite: {
    icon: '/images/vip/elite.png',
    bgIcon: '/images/vip/vector-elite.svg',
    interests: [
      { name: 'maxCharacters', icon: '/images/vip/i-1.svg', key: 'maxCharacters' },
      { name: 'proFeatures', icon: '/images/vip/i-2.svg' },
      { name: 'photoAlbum', icon: '/images/vip/i-3.svg' },
      { name: 'prioritySupport', icon: '/images/vip/i-5.svg' }
    ]
  },
  ultra: {
    icon: '/images/vip/ultra.png',
    bgIcon: '/images/vip/vector-ultra.svg',
    interests: [
      { name: 'maxCharacters', icon: '/images/vip/i-1.svg', key: 'maxCharacters' },
      { name: 'unlimitedChat', icon: '/images/vip/i-2.svg' },
      { name: 'imageGeneration', icon: '/images/vip/i-3.svg' },
      { name: 'voiceGeneration', icon: '/images/vip/i-4.svg' }
    ]
  }
}

export default function MembershipPage() {
  const { t } = useTranslation('membership')
  const { isOpen, onOpen, onOpenChange } = useDisclosure()
  const [planData, setPlanData] = useState({
    id: '',
    name: 'pro',
    description: '',
    durationDays: 30,
    pointsIncluded: 3000,
    price: 146,
    originalPrice: 188
  })

  const navigate = useNavigate()

  const { plans, status: membershipStatus, isLoading, error } = useMembershipData()

  const changeVip = (data: any) => {
    onOpen()
    setPlanData(data)
  }

  // 判断是否为升级操作
  const isUpgrade = (planId: string) => {
    if (!membershipStatus?.currentSubscription) return false

    const currentPlan = plans.find(p => p.id === membershipStatus.currentSubscription?.planId)
    const targetPlan = plans.find(p => p.id === planId)

    if (!currentPlan || !targetPlan) return false

    // 根据sortOrder判断是否为升级（sortOrder越小等级越高，所以升级是 current < target）
    return currentPlan.sortOrder < targetPlan.sortOrder
  }

  // 判断是否为降级操作
  const isDowngrade = (planId: string) => {
    if (!membershipStatus?.currentSubscription) return false

    const currentPlan = plans.find(p => p.id === membershipStatus.currentSubscription?.planId)
    const targetPlan = plans.find(p => p.id === planId)

    if (!currentPlan || !targetPlan) return false

    // 降级是 current > target（sortOrder更大）
    return currentPlan.sortOrder > targetPlan.sortOrder
  }

  if (isLoading) {
    return <LoadingOverlay show={true} text={t('page.loading')} />
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-background via-content1 to-content2 flex items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <p className="text-danger">
            {t('page.error')}: {error}
          </p>
          <Button onPress={() => window.location.reload()}>{t('page.retry')}</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-[#131520] from-background via-content1 to-content2 safe-area-top">
      {/* 顶部导航 */}
      <div className="flex items-center justify-between p-4 pt-12 bg-conic-[<value>] relative">
        <Button
          isIconOnly
          variant="light"
          className="text-width-500 hover:text-foreground z-[1]"
          onPress={() => navigate(-1)}
        >
          <Icon icon="lucide:arrow-left" className="w-5 h-5" />
        </Button>
        <h1 className="text-lg font-semibold text-foreground z-[1]">{t('page.title')}</h1>
        <div className="w-10" /> {/* 占位元素，保持居中 */}
        <img
          alt=""
          className="block w-[14rem] h-[11rem] absolute left-0 top--1"
          src={iconAssets.vipVectorPro}
        />
      </div>

      <img src="/images/vip/light-bg.svg" alt="" className="absolute top-0 right-0" />

      {/* 当前会员状态 */}
      {membershipStatus && (
        <div className="px-6 mb-6 relative">
          <div className="flex items-center justify-between">
            <div>
              <span className="text-sm text-[#7c85b6]">{t('status.current_plan')}</span>

              <p className="text-xl font-semibold text-[#fff]">
                {membershipStatus.membership.isMember
                  ? t('plan.name', { name: membershipStatus.membership.plan?.name || 'Pro' })
                  : t('status.regular_user')}
              </p>
            </div>

            <img alt="" className="block size-24" src={iconAssets.vipPro} />
          </div>

          <div className="flex items-center justify-between text-[#7C85B6] mb-2 text-xs">
            <span>{t('status.available_points')}</span>

            <div>
              <span className="text-[#fff] text-lg">{membershipStatus.points.availablePoints}</span>
              /{membershipStatus.points.totalPoints}
            </div>
          </div>

          <Progress
            size="sm"
            value={
              membershipStatus.points.totalPoints > 0
                ? (membershipStatus.points.usedPoints / membershipStatus.points.totalPoints) * 100
                : 0
            }
            classNames={{
              track: 'bg-[#343C5B]',
              indicator: 'bg-[#62B9FE]'
            }}
          />

          <div className="text-[#4B5383] text-sm mt-2">
            {t('status.days_remaining', {
              days: membershipStatus.currentSubscription?.daysRemaining || 0
            })}
          </div>
        </div>
      )}

      {/* 套餐列表 */}
      <div className="px-6 pt-4 bg-[#1D2135] rounded-t-3xl pb-12">
        <p className="text-sm text-[#7C85B6]">{t('plan.select')}</p>

        <ul>
          {plans.map((plan, index) => {
            const isCurrentPlan = membershipStatus?.currentSubscription?.planId === plan.id

            return (
              <li
                key={plan.id || index}
                className={cn(
                  'overflow-hidden rounded-3xl border border-[#5F90BB] bg-gradient-to-r from-[#D5F3FF] to-[#A0D6ED] mt-4',
                  {
                    'from-[#F5F3F0] to-[#F7E1CB]': plan.name === 'elite',
                    'from-[#F8D6F7] to-[#ea4495]': plan.name === 'ultra'
                  }
                )}
              >
                <div
                  className={cn('flex items-center text-[#1878DF] px-4 ph-2 relative', {
                    'text-[#9C6749]': plan.name === 'elite',
                    'text-[#fff]': plan.name === 'ultra'
                  })}
                >
                  <img
                    alt=""
                    className="block size-16 relative z-1"
                    src={vipIcons[plan.name as keyof typeof vipIcons]?.icon}
                  />
                  <div className="ml-4 relative z-1">
                    <p className="text-base">{t('plan.name', { name: plan.name })}</p>
                    <p className="text-xs">
                      <span className="text-sm">¥{plan.price}</span>
                      <span>{t('plan.per_month')}</span>
                    </p>
                  </div>

                  <div className="flex items-center ml-auto self-start mt-2 relative z-1">
                    <img alt="" className="block size-4" src={iconAssets.point} />
                    <span className="text-xl mr-1 ml-1 font-bold">{plan.pointsIncluded}</span>
                    <span className="text-xs mt-1">{t('plan.points')}</span>
                  </div>

                  <img
                    src={vipIcons[plan.name as keyof typeof vipIcons]?.bgIcon}
                    alt=""
                    className="absolute top-0 right-0 w-39"
                  />
                </div>

                <div className="bg-white px-4 pt-2 pb-4 rounded-3xl relative">
                  <div className="flex items-center justify-center">
                    <div
                      className={cn('w-3 h-0.5 bg-[#1878DF]', {
                        'bg-[#9C6749]': plan.name === 'elite',
                        'bg-[#FF2D97]': plan.name === 'ultra'
                      })}
                    ></div>
                    <p
                      className={cn('text-[#1878DF] px-2', {
                        'text-[#9C6749]': plan.name === 'elite',
                        'text-[#FF2D97]': plan.name === 'ultra'
                      })}
                    >
                      {t('plan.privileges')}
                    </p>
                    <div
                      className={cn('w-3 h-0.5 bg-[#1878DF]', {
                        'bg-[#9C6749]': plan.name === 'elite',
                        'bg-[#FF2D97]': plan.name === 'ultra'
                      })}
                    ></div>
                  </div>

                  <div className="flex items-center justify-between">
                    {vipIcons[plan.name as keyof typeof vipIcons]?.interests.map((item, index) => {
                      // 获取特征值的函数
                      const getFeatureValue = (key?: string) => {
                        if (!key || !plan.features) return ''

                        // 如果 features 是对象格式
                        if (typeof plan.features === 'object' && !Array.isArray(plan.features)) {
                          const value = plan.features[key as keyof typeof plan.features]
                          if (key === 'maxCharacters' && typeof value === 'number') {
                            return value
                          }
                          return value ? '' : '' // 布尔值特征不显示数字
                        }

                        return ''
                      }

                      const featureValue = getFeatureValue(item.key)
                      const translationKey = `features.${item.name}`

                      return (
                        <div key={index} className="flex flex-col items-center">
                          <img alt="" className="block size-10" src={item.icon} />
                          <span className="text-[#7C85B6] text-xs text-center">
                            {item.name === 'maxCharacters' && typeof featureValue === 'number'
                              ? t(translationKey, { count: featureValue })
                              : t(translationKey)}
                          </span>
                        </div>
                      )
                    })}
                  </div>

                  <Button
                    color="default"
                    variant="solid"
                    onPress={() => !isCurrentPlan && !isDowngrade(plan.id) && changeVip(plan)}
                    className={cn(
                      'w-full h-12 bg-[#E4E4E4] text-[#ACACAC] text-base mt-4 rounded-3xl',
                      {
                        'bg-[#1D2135] text-[#fff]': !isCurrentPlan && !isDowngrade(plan.id)
                      }
                    )}
                  >
                    {isCurrentPlan
                      ? t('plan.current')
                      : isDowngrade(plan.id)
                      ? t('plan.downgrade_not_supported')
                      : t('plan.upgrade')}
                  </Button>
                </div>
              </li>
            )
          })}
        </ul>
      </div>

      {/* 开发环境测试 */}
      {process.env.NODE_ENV === 'development' && (
        <div className="px-6 mb-6">
          <Card className="bg-gradient-to-r from-warning-50 to-warning-100 border-warning-200">
            <CardBody className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-warning-100 rounded-full flex items-center justify-center">
                    <Icon icon="lucide:zap" className="w-4 h-4 text-warning-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-warning-700">{t('dev.title')}</h3>
                    <p className="text-sm text-warning-600">{t('dev.alipay_test')}</p>
                  </div>
                </div>
                <Button
                  color="warning"
                  variant="solid"
                  size="sm"
                  onPress={() => navigate('/alipay-test')}
                >
                  {t('dev.test_payment')}
                </Button>
              </div>
            </CardBody>
          </Card>
        </div>
      )}

      {/* 支付弹窗 */}
      <Drawer isOpen={isOpen} hideCloseButton placement="bottom" onOpenChange={onOpenChange}>
        <DrawerContent
          className={cn('bg-gradient-to-r from-[#D5F3FF] to-[#A0D6ED]', {
            'from-[#F5F3F0] to-[#F7E1CB]': planData.name === 'elite',
            'from-[#F8D6F7] to-[#ea4495]': planData.name === 'ultra'
          })}
        >
          {onClose => {
            const [paymentOrder, setPaymentOrder] = useState<PaymentOrder | null>(null)
            const [loading, setLoading] = useState(false)
            const [error, setError] = useState<string | null>(null)
            const [step, setStep] = useState<'plan' | 'processing' | 'success' | 'failed'>('plan')

            // 预创建订单以获取价格信息
            useEffect(() => {
              if (planData?.id) {
                preCreateOrder(planData)
              }
            }, [planData])

            // 预创建订单以获取价格信息
            const preCreateOrder = async (selectedPlan: any) => {
              try {
                setLoading(true)
                const response = await paymentService.createOrder({
                  planId: selectedPlan.id,
                  description: `订阅${selectedPlan.name}会员`,
                  metadata: {
                    planName: selectedPlan.name,
                    pointsIncluded: selectedPlan.pointsIncluded
                  }
                })
                setPaymentOrder(response.data)
                setStep('plan')
              } catch (error: any) {
                console.error('预创建订单失败:', error)
                setError(error.message || '获取价格信息失败')
              } finally {
                setLoading(false)
              }
            }

            // 确认订单，直接模拟支付成功
            const handleCreateOrder = async () => {
              if (!paymentOrder) return

              try {
                setLoading(true)
                setStep('processing')

                // 模拟订单处理时间
                await new Promise(resolve => setTimeout(resolve, 2000))

                // 直接设置为成功状态
                setStep('success')

                // 3秒后关闭弹窗并刷新会员状态
                setTimeout(() => {
                  onClose()
                  window.location.reload()
                }, 3000)
              } catch (error: any) {
                console.error('处理订单失败:', error)
                setError(error.message || '处理订单失败')
                setStep('failed')
              } finally {
                setLoading(false)
              }
            }

            // 渲染不同状态的内容
            const renderContent = () => {
              switch (step) {
                case 'processing':
                  return (
                    <div className="text-center py-4">
                      <div className="flex justify-center mb-4">
                        <div className="w-12 h-12 rounded-full border-4 border-primary border-t-transparent animate-spin"></div>
                      </div>
                      <p className="text-white text-lg font-medium">订单生成中</p>
                      <p className="text-gray-400 text-sm">请稍候，正在为您创建订单...</p>
                    </div>
                  )
                case 'success':
                  return (
                    <div className="text-center py-4">
                      <div className="flex justify-center mb-4">
                        <div className="w-12 h-12 rounded-full bg-success-100 flex items-center justify-center">
                          <Icon icon="lucide:check" className="w-6 h-6 text-success-600" />
                        </div>
                      </div>
                      <p className="text-white text-lg font-medium">支付成功</p>
                      <p className="text-gray-400 text-sm">您的会员已升级，即将刷新页面...</p>
                    </div>
                  )
                case 'failed':
                  return (
                    <div className="text-center py-4">
                      <div className="flex justify-center mb-4">
                        <div className="w-12 h-12 rounded-full bg-danger-100 flex items-center justify-center">
                          <Icon icon="lucide:x" className="w-6 h-6 text-danger-600" />
                        </div>
                      </div>
                      <p className="text-white text-lg font-medium">支付失败</p>
                      <p className="text-danger-400 text-sm">{error || '订单处理失败，请重试'}</p>
                      <Button
                        className="mt-4 bg-[#1D2135] border border-white/30 text-white"
                        onPress={() => setStep('plan')}
                      >
                        返回
                      </Button>
                    </div>
                  )
                default:
                  return null
              }
            }

            return (
              <>
                <DrawerBody className="px-6 ph-2">
                  <img
                    alt=""
                    className="block size-16 absolute top-0 right-0 w-[13rem] h-[8rem]"
                    src={vipIcons[planData?.name as keyof typeof vipIcons].bgIcon}
                  />

                  <div
                    className={cn('flex items-center text-[#1878DF] relative', {
                      'text-[#9C6749]': planData.name === 'elite',
                      'text-[#fff]': planData.name === 'ultra'
                    })}
                  >
                    <img
                      alt=""
                      className="block size-16"
                      src={vipIcons[planData?.name as keyof typeof vipIcons].icon}
                    />
                    <div className="ml-4">
                      <p className="text-base">{t('plan.name', { name: planData.name })}</p>
                      <p className="text-xs">{planData.description}</p>
                    </div>
                  </div>
                </DrawerBody>
                <DrawerFooter className="block bg-[#1D2135] rounded-t-3xl relative p-6 pb-12">
                  {['processing', 'success', 'failed'].includes(step) ? (
                    renderContent()
                  ) : (
                    <>
                      {error && (
                        <div className="bg-danger-50 border border-danger-200 rounded-lg p-3 mb-4">
                          <p className="text-danger-700 text-sm">{error}</p>
                        </div>
                      )}

                      <ul>
                        <li className="flex items-center justify-between text-base">
                          <span className="text-[#7C85B6]">{t('payment.duration')}</span>
                          <span className="text-[#fff]">{t('payment.days')}</span>
                        </li>

                        <li className="flex items-center justify-between mt-2 text-base border-b border-[#343C5B] pb-3">
                          <span className="text-[#7C85B6]">{t('payment.points_included')}</span>
                          <span className="text-[#fff]">
                            {t('payment.points_value', { points: planData.pointsIncluded })}
                          </span>
                        </li>

                        <li className="flex items-center mt-2 mt-3 justify-between text-base">
                          <span className="text-[#7C85B6]">{t('payment.original_price')}</span>
                          <span className="text-[#fff]">
                            {t('payment.price_value', {
                              price: paymentOrder?.originalAmount?.toFixed(2) || planData.price
                            })}
                          </span>
                        </li>

                        {paymentOrder?.isUpgrade && (
                          <li className="flex items-center mt-2 justify-between text-base">
                            <span className="text-[#7C85B6]">{t('payment.discount')}</span>
                            <span>
                              {t('payment.discount_value', {
                                discount: paymentOrder?.savings?.toFixed(2) || '0.00'
                              })}
                            </span>
                          </li>
                        )}

                        <li className="flex items-center mt-2 justify-between text-base font-semibold">
                          <span className="text-[#7C85B6]">{t('payment.final_price')}</span>
                          <span className="text-[#FF4747]">
                            {t('payment.final_price_value', {
                              price: paymentOrder?.amount?.toFixed(2) || planData.price
                            })}
                          </span>
                        </li>
                      </ul>

                      <div className="flex justify-center mt-8">
                        <Button
                          className="bg-[#1e2134] flex-1 mr-2 h-12 border border-[#fff] rounded-3xl text-base font-medium"
                          onPress={onClose}
                          isDisabled={loading}
                        >
                          {t('payment.cancel')}
                        </Button>
                        <Button
                          className="flex-1 rounded-3xl h-12 bg-gradient-to-r from-[#FF2D97] to-[#892FFF] text-base font-medium"
                          onPress={handleCreateOrder}
                          isLoading={loading}
                        >
                          {step === 'processing' ? '订单生成中...' : t('payment.confirm_upgrade')}
                        </Button>
                      </div>

                      {/* 开发环境下显示模拟支付失败按钮 */}
                      {process.env.NODE_ENV === 'development' && (
                        <div className="mt-4">
                          <Button
                            size="sm"
                            color="danger"
                            variant="flat"
                            className="w-full"
                            onPress={() => setStep('failed')}
                          >
                            模拟支付失败
                          </Button>
                        </div>
                      )}
                    </>
                  )}
                </DrawerFooter>
              </>
            )
          }}
        </DrawerContent>
      </Drawer>
    </div>
  )
}
