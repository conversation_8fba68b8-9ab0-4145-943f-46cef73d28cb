/**
 * 积分商城页面
 */

import { useState, useEffect, useCallback } from 'react'
import { useNavigate } from 'react-router'
import { useTranslation } from 'react-i18next'
import { useUserPoints } from '@/hooks/use-membership'
import { pointsService } from '@/api/services/points'
import type { PointsPackage, CreatePointsOrderRequest } from '@/api/services/points'
import { cn } from '@/lib/utils'
import { LoadingOverlay } from '@/components/common/loading'

// HeroUI 组件
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  addToast,
  Spinner,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure
} from '@heroui/react'

// Lucide 图标
import { ArrowLeft, Coins } from 'lucide-react'

export default function PointsStorePage() {
  const { t } = useTranslation('points')
  const navigate = useNavigate()
  const { points, isLoading: pointsLoading } = useUserPoints()
  const [packages, setPackages] = useState<PointsPackage[]>([])
  const [loading, setLoading] = useState(true)
  const [purchasing, setPurchasing] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [selectedPackage, setSelectedPackage] = useState<PointsPackage | null>(null)
  const { isOpen, onOpen, onClose } = useDisclosure()

  const loadPackages = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      console.log('🛍️ [POINTS-STORE] 加载积分包列表')

      const response = await pointsService.getPackages()

      if (response.success) {
        setPackages(response.data || [])
        console.log('✅ [POINTS-STORE] 积分包加载成功，数量:', response.data?.length)
      } else {
        throw new Error(response.error || t('store.load_failed'))
      }
    } catch (error: any) {
      console.error('❌ [POINTS-STORE] 加载积分包失败:', error)
      setError(error.message || t('store.load_failed'))
      addToast({
        title: t('store.load_failed'),
        description: error.message || t('store.load_failed'),
        color: 'danger'
      })
    } finally {
      setLoading(false)
    }
  }, [t])

  // 加载积分包列表
  useEffect(() => {
    loadPackages()
  }, [loadPackages])

  // 购买积分包
  const handlePurchase = useCallback(
    (pkg: PointsPackage) => {
      setSelectedPackage(pkg)
      onOpen()
    },
    [onOpen]
  )

  const confirmPurchase = useCallback(
    async (paymentMethod: 'alipay' | 'wechat') => {
      if (!selectedPackage) return

      try {
        setPurchasing(selectedPackage.id)
        console.log('🛒 [POINTS-STORE] 开始购买积分包:', selectedPackage.name)

        const purchaseData: CreatePointsOrderRequest = {
          description: `购买${selectedPackage.name} - ${
            selectedPackage.points + selectedPackage.bonusPoints
          }积分`,
          paymentMethod,
          metadata: {
            source: 'points-store',
            packageName: selectedPackage.name
          }
        }

        const response = await pointsService.purchasePackage(selectedPackage.id, purchaseData)

        if (response.success && response.data) {
          console.log('✅ [POINTS-STORE] 积分包订单创建成功:', response.data.orderId)

          addToast({
            title: t('store.purchase_success'),
            description: t('store.redirecting'),
            color: 'success'
          })

          // 打开支付页面
          if (response.data.redirectUrl) {
            window.open(response.data.redirectUrl, '_blank')
          }

          onClose()
        } else {
          throw new Error(response.error || t('store.purchase_failed'))
        }
      } catch (error: any) {
        console.error('❌ [POINTS-STORE] 购买积分包失败:', error)

        let errorMessage = t('store.purchase_failed')
        if (error.issues) {
          errorMessage = t('store.validation_failed')
        } else if (error.message) {
          errorMessage = error.message
        }

        addToast({
          title: t('store.purchase_failed'),
          description: errorMessage,
          color: 'danger'
        })
      } finally {
        setPurchasing(null)
      }
    },
    [selectedPackage, t, onClose]
  )

  // 获取积分包图标
  const getPackageIcon = useCallback((points: number) => {
    if (points <= 300) return <img src="/images/point/point-1.svg" className="w-6 h-6" />
    if (points <= 400) return <img src="/images/point/point-2.svg" className="w-6 h-6" />
    if (points <= 600) return <img src="/images/point/point-3.svg" className="w-6 h-6" />
    return <img src="/images/point/point-4.svg" className="w-6 h-6" />
  }, [])

  // 获取积分包颜色
  const getPackageColor = useCallback((points: number) => {
    if (points <= 100) return 'warning'
    if (points <= 300) return 'primary'
    if (points <= 600) return 'secondary'
    return 'success'
  }, [])

  // 计算节省金额
  const getSavings = useCallback((pkg: PointsPackage) => {
    if (pkg.bonusPoints <= 0) return 0
    const totalPoints = pkg.points + pkg.bonusPoints
    const originalPrice = (totalPoints / pkg.points) * parseFloat(pkg.price)
    return originalPrice - parseFloat(pkg.price)
  }, [])

  if (loading) {
    return <LoadingOverlay show />
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-background via-content1 to-content2 safe-area-top pb-14">
      {/* 顶部导航 */}
      <div className="flex items-center justify-between p-4 pt-12 relative z-1">
        <Button
          isIconOnly
          variant="light"
          className="text-[#fff] hover:text-foreground"
          onPress={() => navigate(-1)}
        >
          <ArrowLeft className="w-5 h-5" />
        </Button>
        <h1 className="text-lg font-semibold text-[#fff]">{t('store.title')}</h1>
        <div className="w-10" /> {/* 占位元素，保持居中 */}
      </div>

      <img src="/images/point/light-bg.svg" alt="" className="absolute top-0 left-0 w-full" />

      {/* 当前积分显示 */}
      <div className="px-4 mb-6 relative z-1">
        <Card className="bg-gradient-to-r from-primary-50 to-primary-100 border-primary-200">
          <CardBody className="bg-gradient-to-r from-[#F5F3F0] to-[#F7E1CB] pt-7 pb-7 pr-4 pl-5 relative">
            <div className="flex items-center justify-between relative z-1">
              <div className="flex items-center gap-3">
                <div>
                  <img src="/images/point/point-icon.png" alt="" className="w-13 h-13" />
                </div>
                <div>
                  <h3 className="text-lg text-[#262626] font-bold">{t('store.current_points')}</h3>
                  <p className="text-sm text-[#8C8C8C]">{t('store.available_points')}</p>
                </div>
              </div>
              <div className="text-right">
                {pointsLoading ? (
                  <Spinner size="sm" color="primary" />
                ) : (
                  <p className="text-2xl font-bold text-[#FF831D] ">
                    {points?.availablePoints || 0}
                  </p>
                )}
                <p className="text-xs text-[#8C8C8C]">{t('store.points_unit')}</p>
              </div>
            </div>

            <img
              src="/images/point/lightning.svg"
              alt=""
              className="w-30 h-full absolute right-3 top-0"
            />
          </CardBody>
        </Card>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="px-4 mb-6">
          <Card className="bg-danger-50 border-danger-200">
            <CardBody className="p-4">
              <p className="text-danger-700 text-sm">{error}</p>
              <Button
                size="sm"
                color="danger"
                variant="light"
                onPress={loadPackages}
                className="mt-2"
              >
                {t('store.reload')}
              </Button>
            </CardBody>
          </Card>
        </div>
      )}

      {/* 积分包列表 */}
      <div className="px-4">
        <h2 className="text-sm font-semibold text-[#7C85B6] mb-3">{t('store.select_package')}</h2>

        <div className="space-y-4">
          {packages.map(pkg => {
            const totalPoints = pkg.points + pkg.bonusPoints
            const savings = getSavings(pkg)
            const isPopular = pkg.points >= 200 && pkg.points <= 400 // 中等价位的包推荐

            return (
              <Card
                key={pkg.id}
                className="backdrop-blur-sm relative rounded-3xl pt-3 px-4 pb-6"
                style={{
                  background: "url('/images/point/light-bg-card.svg') no-repeat #1D2135",
                  backgroundPosition: 'top right'
                }}
              >
                {isPopular && (
                  <div className="absolute top-[-2rem] left-[-2rem] w-15 h-15 pb-[1px] bg-[#FF4747] rotate-[-45deg] text-xs flex justify-center items-end">
                    推荐
                  </div>
                )}

                <CardHeader className="pb-2 pt-0">
                  <div className="flex justify-between w-full">
                    <div className="flex items-center gap-3">
                      {getPackageIcon(pkg.points)}
                      <div>
                        <h3 className="text-base font-semibold text-foreground text-[#fff]">
                          {pkg.name}
                        </h3>
                        <p className="text-xs text-[#7C85B6]">{pkg.description}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center gap-2">
                        <span className="text-2xl font-bold text-[#FF8B25] ml-[auto] width-[fit-content]">
                          ¥{pkg.price}
                        </span>
                      </div>
                      {savings > 0 && (
                        <p className="text-xs text-[#FF2D97]">
                          {t('store.savings')} ¥{savings.toFixed(2)}
                        </p>
                      )}
                    </div>
                  </div>
                </CardHeader>

                <CardBody className="p-0 mt-4">
                  <div className="space-y-4">
                    {/* 积分详情 */}
                    <div className="bg-[#121521] rounded-2xl p-4">
                      <div className="flex items-center justify-between mb-4">
                        <span className="text-sm text-[#7C85B6] font-bold">
                          {t('store.base_points')}
                        </span>
                        <span className="font-[#fff] text-sm">{pkg.points}</span>
                      </div>
                      {pkg.bonusPoints > 0 && (
                        <div className="flex items-center justify-between mb-4">
                          <span className="text-sm text-[#FF2D97] font-bold flex items-center gap-1">
                            {t('store.bonus_points')}
                          </span>
                          <span className="text-[#FF2D97] text-sm">+{pkg.bonusPoints}</span>
                        </div>
                      )}
                      <div className="flex items-center justify-between pt-4 border-t border-default-200">
                        <span className="text-sm text-[#7C85B6] font-bold">
                          {t('store.total_points')}
                        </span>
                        <span className="font-[#fff] text-sm">{totalPoints}</span>
                      </div>
                    </div>

                    {/* 购买按钮 */}
                    <Button
                      color={getPackageColor(pkg.points) as any}
                      variant="solid"
                      className="w-full h-12 bg-gradient-to-r from-[#FF2D97] to-[#892FFF] text-base rounded-3xl"
                      isLoading={purchasing === pkg.id}
                      onPress={() => handlePurchase(pkg)}
                    >
                      {t('store.purchase_now')}
                    </Button>
                  </div>
                </CardBody>
              </Card>
            )
          })}
        </div>

        {packages.length === 0 && !loading && !error && (
          <div className="text-center py-12">
            <Coins className="w-16 h-16 text-default-300 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-default-600 mb-2">
              {t('store.no_packages')}
            </h3>
            <p className="text-default-500">{t('store.no_packages_description')}</p>
          </div>
        )}
      </div>

      {/* 购买确认对话框 */}
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        size="md"
        hideCloseButton
        className="m-0 rounded-b-[0] bg-gradient-to-r from-[#F5F3F0] to-[#F7E1CB]"
      >
        <ModalContent>
          <ModalHeader className="relative block pb-2">
            <h3 className="text-lg font-bold text-[#262626]">{t('store.confirm_purchase')}</h3>
            <p className="text-[#8C8C8C] mt-1 text-sm">{selectedPackage?.name}</p>
            <img
              src="/images/point/lightning.svg"
              alt=""
              className="w-30 h-27 absolute right-5 top-[-1rem]"
            />
          </ModalHeader>
          <ModalBody className="p-0 bg-[#1D2135] rounded-t-3xl relative p-6">
            {selectedPackage && (
              <div className="space-y-4">
                <div>
                  <div className="text-base">
                    <div
                      className={cn('flex justify-between items-center', {
                        'mb-3': selectedPackage.bonusPoints <= 0
                      })}
                    >
                      <span className="text-[#7C85B6]">{t('store.base_points')}</span>
                      <span className="text-[#fff]">{selectedPackage.points}</span>
                    </div>
                    {selectedPackage.bonusPoints > 0 && (
                      <div className="flex justify-between items-center text-[#FF2D97] mt-2 mb-3">
                        <span>{t('store.bonus_points')}</span>
                        <span>+{selectedPackage.bonusPoints}</span>
                      </div>
                    )}
                    <div className="flex justify-between items-center border-t border-[#343C5B] font-semibold pt-3 mb-6">
                      <span className="text-[#7C85B6]">{t('store.total_points')}</span>
                      <span className="text-[#fff]">
                        {selectedPackage.points + selectedPackage.bonusPoints}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-[#7C85B6]">{t('store.payment_amount')}</span>
                      <span className="text-[#FF4747] font-bold text-xl">
                        ¥{selectedPackage.price}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </ModalBody>
          <ModalFooter className="bg-[#1D2135]">
            <div className="flex gap-2 w-full">
              <Button
                variant="light"
                onPress={onClose}
                className="flex-1 rounded-3xl border border-[#fff] font-medium text-base h-12"
              >
                {t('store.cancel')}
              </Button>
              <Button
                color="primary"
                onPress={() => confirmPurchase('alipay')}
                isLoading={purchasing === selectedPackage?.id}
                className="flex-1 rounded-3xl font-medium text-base h-12 bg-gradient-to-r from-[#FF2D97] to-[#892FFF]"
              >
                {t('store.alipay_payment')}
              </Button>
            </div>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  )
}
