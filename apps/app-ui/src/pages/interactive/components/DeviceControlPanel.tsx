import React from 'react'
import { <PERSON><PERSON>, Switch } from '@heroui/react'
import { Icon } from '@iconify/react'
import { DraggableSlider } from '@/components/ui/draggable-slider'
import type { DeviceFunction } from '../types'
import { ControlMode } from '../types'
import { useTranslation } from 'react-i18next'

interface DeviceControlPanelProps {
  isOpen: boolean
  onClose: () => void
  deviceFunctions: DeviceFunction[]
  currentIntensity: { [key: string]: number }
  controlMode: ControlMode
  onIntensityChange: (key: string, intensity: number) => void
  onControlModeChange: (mode: ControlMode) => void
  onBackToScriptSelect: () => void
}

/**
 * 设备控制面板组件 - 底部抽屉样式
 * 用于控制设备功能强度
 */
export const DeviceControlPanel: React.FC<DeviceControlPanelProps> = ({
  isOpen,
  onClose,
  deviceFunctions,
  currentIntensity,
  controlMode,
  onIntensityChange,
  onControlModeChang<PERSON>
}) => {
  const { t } = useTranslation('interactive')
  // 获取最大强度值
  const getMaxIntensity = (func: DeviceFunction): number => {
    const commands = func.commands.filter(cmd => cmd.intensity > 0)
    return commands.length > 0 ? Math.max(...commands.map(cmd => cmd.intensity)) : 0
  }

  if (!isOpen) return null

  return (
    <>
      {/* 背景遮罩 */}
      <div
        className="fixed inset-0 bg-black/50 z-40 transition-opacity duration-300"
        onClick={onClose}
        style={{ opacity: isOpen ? 1 : 0 }}
      />

      {/* 底部抽屉 */}
      <div
        className={`fixed bottom-0 left-0 right-0 z-50 bg-[#2a1b3d] rounded-t-3xl transform transition-transform duration-300 ease-out ${
          isOpen ? 'translate-y-0' : 'translate-y-full'
        }`}
        data-drawer
      >
        {/* 顶部拖拽指示器 */}
        <div className="flex justify-center pt-3 pb-2">
          <div className="w-10 h-1 bg-white/30 rounded-full" />
        </div>

        {/* 标题栏 */}
        <div className="flex items-center justify-between px-6 py-4">
          <h3 className="text-white text-lg font-medium">{t('deviceControlPanel.title')}</h3>
          <Button
            isIconOnly
            variant="flat"
            color="default"
            size="sm"
            onPress={onClose}
            className="bg-transparent text-white hover:bg-white/20"
            aria-label={t('deviceControlPanel.closeAriaLabel')}
          >
            <Icon icon="material-symbols:close" width={20} />
          </Button>
        </div>

        {/* 控制内容 */}
        <div className="px-6 pb-8">
          {/* 提示文字 */}
          <p className="text-white/70 text-sm mb-6">
            {t('deviceControlPanel.currentMode')}
            {controlMode === ControlMode.AUTO
              ? t('deviceControlPanel.autoMode')
              : t('deviceControlPanel.manualMode')}
          </p>

          {/* 手动模式开关 */}
          <div className="flex items-center justify-between mb-8">
            <span className="text-white text-base">{t('deviceControlPanel.manualMode')}</span>
            <Switch
              isSelected={controlMode === ControlMode.MANUAL}
              onValueChange={isSelected =>
                onControlModeChange(isSelected ? ControlMode.MANUAL : ControlMode.AUTO)
              }
              color="secondary"
              size="lg"
              classNames={{
                wrapper: 'group-data-[selected=true]:bg-[#ff2d97]',
                thumb: 'bg-white'
              }}
            />
          </div>

          {/* 设备功能控制列表 */}
          <div className="space-y-4">
            {deviceFunctions.map((func, index) => {
              const maxIntensity = getMaxIntensity(func)
              const currentValue = currentIntensity[func.key] || 0

              // 为不同功能分配不同颜色
              const colors = ['#ff2d97', '#892fff', '#00d4ff', '#ff9500', '#00ff88']
              const color = colors[index % colors.length]

              return (
                <div key={func.key} className="space-y-1">
                  {/* 所有元素在同一水平线上：功能名称 + 强度 + 滑块 + 档位 */}
                  <div className="flex items-center gap-3">
                    {/* 功能名称 */}
                    <span className="text-white text-base w-8 flex-shrink-0">{func.name}</span>
                    <div className="flex items-center w-[70%] ml-auto">
                      {/* 强度文字 */}
                      <span className="text-white/70 text-sm w-14 flex-shrink-0">
                        {t('deviceControlPanel.intensity')}
                      </span>

                      {/* 滑块区域 - 限制最大宽度 */}
                      <div className="flex-1 max-w-[200px]">
                        <DraggableSlider
                          value={currentValue <= 0 ? 0 : currentValue}
                          min={0}
                          max={maxIntensity}
                          onChange={(value: number) => {
                            onIntensityChange(func.key, value === 0 ? -1 : value)
                          }}
                          color={color}
                          showTicks={false}
                          disabled={controlMode === ControlMode.AUTO}
                        />
                      </div>

                      {/* 档位显示 */}
                      <span
                        className="text-sm font-medium w-10 flex-shrink-0 text-right"
                        style={{ color }}
                      >
                        {currentValue === 0 || currentValue === -1 ? (
                          <span className="text-white/70  flex-shrink-0">
                            {t('deviceControlPanel.off')}
                          </span>
                        ) : (
                          <span className="flex items-center justify-end">
                            <span className="font-bold text-md">{currentValue}</span>
                            <span className="text-white/70 flex-shrink-0 ml-1">
                              {t('deviceControlPanel.level')}
                            </span>
                          </span>
                        )}
                      </span>
                    </div>
                  </div>

                  {/* 滑块下方的刻度线 */}
                  <div className="flex items-center gap-3">
                    <div className="w-8 flex-shrink-0"></div> {/* 功能名称占位 */}
                    <div className="flex items-center w-[70%] ml-auto">
                      <div className="w-14 flex-shrink-0"></div> {/* 强度文字占位 */}
                      <div className="flex-1 max-w-[200px] relative h-3 flex items-center">
                        {/* 只显示刻度线，不显示数字 */}
                        <div className="flex justify-between w-full">
                          {Array.from({ length: maxIntensity + 1 }, (_, i) => (
                            <div
                              key={i}
                              className="w-px h-2 rounded-full"
                              style={{
                                backgroundColor:
                                  i <= (currentValue <= 0 ? 0 : currentValue) ? color : '#9c9fab'
                              }}
                            />
                          ))}
                        </div>
                      </div>
                      <div className="w-10 flex-shrink-0"></div> {/* 档位显示占位 */}
                    </div>
                  </div>
                </div>
              )
            })}
          </div>

          {/* 如果没有设备功能，显示提示 */}
          {deviceFunctions.length === 0 && (
            <div className="text-center py-8">
              <Icon icon="solar:device-bold" className="text-white/30 mx-auto mb-3" width={48} />
              <p className="text-white/50 text-sm">{t('deviceControlPanel.noDeviceFunctions')}</p>
            </div>
          )}
        </div>

        {/* 底部安全区域 */}
        <div className="h-8" />
      </div>
    </>
  )
}
