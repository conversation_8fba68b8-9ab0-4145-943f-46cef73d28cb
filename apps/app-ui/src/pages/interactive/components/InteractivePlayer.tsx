import React, { useEffect, useRef, useState, useCallback } from 'react'
import { usePlayer } from '../context/PlayerContext'
import { SceneDisplay } from './SceneDisplay'
import { ImmersiveControlPanel } from './ImmersiveControlPanel'
import { InteractiveGestureHandler } from './InteractiveGestureHandler'
import { sendBluetoothCommand } from '@/utils/bluetooth/bluetoothUtils'

interface InteractivePlayerProps {
  onBackToScriptSelect: () => void
}

/**
 * 互动播放器组件
 * 整合所有子组件
 */
export const InteractivePlayer: React.FC<InteractivePlayerProps> = ({ onBackToScriptSelect }) => {
  const {
    script,
    device,
    currentTime,
    duration,
    currentStageIndex,
    playerState,
    play,
    pause,
    seek,
    setStage
  } = usePlayer()

  // 控制面板显示状态
  const [showControls, setShowControls] = useState(true)
  const [isLongPressing, setIsLongPressing] = useState(false) // 长按状态
  const hideControlsTimer = useRef<NodeJS.Timeout | null>(null)

  // 显示控制面板并重置隐藏计时器
  const handleShowControls = useCallback(() => {
    setShowControls(true)

    // 清除现有计时器
    if (hideControlsTimer.current) {
      clearTimeout(hideControlsTimer.current)
    }

    // 5秒后自动隐藏（但长按时不隐藏）
    hideControlsTimer.current = setTimeout(() => {
      if (!isLongPressing) {
        setShowControls(false)
      }
    }, 5000)
  }, [isLongPressing])

  // 隐藏控制面板
  const handleHideControls = useCallback(() => {
    setShowControls(false)

    // 清除现有计时器
    if (hideControlsTimer.current) {
      clearTimeout(hideControlsTimer.current)
    }
  }, [])

  // 长按状态变化处理
  const handleLongPressChange = useCallback(
    (pressing: boolean) => {
      setIsLongPressing(pressing)

      if (pressing) {
        // 长按开始时，清除隐藏计时器，保持面板显示
        if (hideControlsTimer.current) {
          clearTimeout(hideControlsTimer.current)
        }
      } else {
        // 长按结束时，重新启动隐藏计时器
        if (showControls) {
          hideControlsTimer.current = setTimeout(() => {
            setShowControls(false)
          }, 5000)
        }
      }
    },
    [showControls]
  )

  // 检查是否有抽屉打开
  const isDrawerOpen = false // 这里可以根据实际抽屉状态来设置

  // 调试信息
  useEffect(() => {
    console.log('🔍 InteractivePlayer - 设备状态:', device)
    console.log('🔍 InteractivePlayer - 设备是否存在:', !!device)
    console.log('🔍 InteractivePlayer - 设备功能:', device?.func)
  }, [device])

  // 获取当前阶段
  const currentStage =
    script.length > 0 && currentStageIndex < script.length ? script[currentStageIndex] : null

  // 快进指定秒数
  const handleForward = (seconds: number) => {
    seek(currentTime + seconds)
  }

  // 获取阶段标题列表
  const stageTitles = script.map(stage => stage.stageTitle)

  // 页面退出时的额外清理逻辑
  useEffect(() => {
    const handlePageExit = () => {
      if (device) {
        console.log('🛑 InteractivePlayer - 页面退出，停止所有设备')
        // 确保所有设备功能都停止
        device.func.forEach(func => {
          // 发送停止命令 -1
          try {
            sendBluetoothCommand(func.key, -1, device.func)
          } catch (error) {
            console.error('发送停止命令失败:', error)
          }
        })
      }
    }

    // 组件卸载时的清理
    return () => {
      handlePageExit()
      // 清理控制面板隐藏计时器
      if (hideControlsTimer.current) {
        clearTimeout(hideControlsTimer.current)
      }
    }
  }, [device])

  return (
    <div className="relative size-full flex flex-col bg-black h-screen" data-swipe-disabled="true">
      {/* 场景显示区域 */}
      <div className="relative size-full flex-1 overflow-hidden">
        <SceneDisplay scenePics={currentStage?.pics} />

        {/* 手势处理层 */}
        <InteractiveGestureHandler
          currentTime={currentTime}
          duration={duration}
          playerState={playerState}
          currentStageIndex={currentStageIndex}
          stageCount={script.length}
          onSeek={seek}
          onPlay={play}
          onPause={pause}
          onStageChange={setStage}
          onShowControls={handleShowControls}
          onHideControls={handleHideControls}
          onLongPressChange={handleLongPressChange}
          isControlsVisible={showControls}
          isDrawerOpen={isDrawerOpen}
          className="absolute inset-0 z-10 w-screen h-screen"
        >
          {/* 沉浸式控制面板 */}
          <ImmersiveControlPanel
            currentTime={currentTime}
            duration={duration}
            playerState={playerState}
            currentStageIndex={currentStageIndex}
            stageCount={script.length}
            stageTitles={stageTitles}
            onPlay={play}
            onPause={pause}
            onSeek={seek}
            onStageChange={setStage}
            onForward={handleForward}
            hideStageControls={true} // 隐藏阶段控制
            showControls={showControls}
            onShowControls={handleShowControls}
          />
        </InteractiveGestureHandler>
      </div>
    </div>
  )
}
