import React, { useState, useEffect, useRef, useCallback } from 'react'
import { <PERSON>, CardBody, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Spinner, addToast } from '@heroui/react'
import { Icon } from '@iconify/react'
import { motion, AnimatePresence } from 'framer-motion'
import { Capacitor } from '@capacitor/core'
import { useTranslation } from 'react-i18next'
import { PlayerState } from '../types'
import { secondsToTime } from '../utils/timeUtils'
import { usePlayer } from '../context/PlayerContext'
import { StageSelector } from './StageSelector'
import { DeviceControlPanel } from './DeviceControlPanel'
import { CommandPanel } from './CommandPanel'
import { StageProgressBar } from './StageProgressBar'
import { useDeviceStore } from '../../../stores/device-store'
import type { DeviceFunction, ControlMode } from '../types'

interface ImmersiveControlPanelProps {
  currentTime: number
  duration: number
  playerState: PlayerState
  currentStageIndex: number
  stageCount: number
  stageTitles: string[]
  onPlay: () => void
  onPause: () => void
  onSeek: (time: number) => void
  onStageChange: (index: number) => void
  onForward: (seconds: number) => void
  hideStageControls?: boolean
  showControls: boolean
  onShowControls: () => void
}

/**
 * 沉浸式控制面板组件
 * 只负责UI显示，手势处理由外部组件负责
 */
export const ImmersiveControlPanel: React.FC<ImmersiveControlPanelProps> = ({
  currentTime,
  duration,
  playerState,
  currentStageIndex,
  stageCount,
  stageTitles,
  onPlay,
  onPause,
  onSeek,
  onStageChange,
  onForward,
  hideStageControls,
  showControls,
  onShowControls
}) => {
  // 原有的控制面板状态
  const [isProcessingClick, setIsProcessingClick] = useState(false)
  const [playAttempts, setPlayAttempts] = useState(0)
  const [audioLoadingStatus, setAudioLoadingStatus] = useState<'loading' | 'ready' | 'error'>(
    'loading'
  )

  // 抽屉状态
  const [showStageSelector, setShowStageSelector] = useState(false)
  const [showDevicePanel, setShowDevicePanel] = useState(false)
  const [showCommandPanel, setShowCommandPanel] = useState(false)

  // 设备控制相关状态
  const [currentIntensity, setCurrentIntensity] = useState<{ [key: string]: number }>({})
  const [controlMode, setControlMode] = useState<ControlMode>('auto' as ControlMode)

  // 定时器引用
  const playTimeoutRef = useRef<number | null>(null)
  const debounceTimerRef = useRef<number | null>(null)
  const audioElementRef = useRef<HTMLAudioElement | null>(null)

  // 其他引用
  const maxPlayAttempts = 3
  const isCapacitor = Capacitor.isNativePlatform()
  const platform = Capacitor.getPlatform()

  // 获取播放器上下文
  const { device, sendCommand, commands, script } = usePlayer()
  const { connectedDevice } = useDeviceStore()
  const { t } = useTranslation('interactive')
  const lastCommandsRef = useRef<{ [key: string]: number }>({})

  // 检查是否有指令
  const hasCommands = commands && commands.length > 0

  // 获取音频元素的函数（复用原有逻辑）
  const getAudioElement = (): HTMLAudioElement | null => {
    if (audioElementRef.current) {
      return audioElementRef.current
    }

    const audioElement = document.querySelector('audio') as HTMLAudioElement
    if (audioElement) {
      audioElementRef.current = audioElement
    }
    return audioElement
  }

  // 音频加载状态检查（复用原有逻辑）
  useEffect(() => {
    const audioElement = getAudioElement()
    if (audioElement) {
      const handleCanPlay = () => setAudioLoadingStatus('ready')
      const handleError = () => setAudioLoadingStatus('error')
      const handleLoadStart = () => setAudioLoadingStatus('loading')

      audioElement.addEventListener('canplay', handleCanPlay)
      audioElement.addEventListener('error', handleError)
      audioElement.addEventListener('loadstart', handleLoadStart)

      // 检查当前状态
      if (audioElement.readyState >= 3) {
        setAudioLoadingStatus('ready')
      } else if (audioElement.readyState === 0) {
        setAudioLoadingStatus('loading')
      }

      return () => {
        audioElement.removeEventListener('canplay', handleCanPlay)
        audioElement.removeEventListener('error', handleError)
        audioElement.removeEventListener('loadstart', handleLoadStart)
      }
    }
  }, [])

  // 复用原有的播放控制逻辑
  const handlePlayPause = async () => {
    if (isProcessingClick) return

    if (audioLoadingStatus === 'loading') {
      return
    } else if (audioLoadingStatus === 'error' && !isCapacitor) {
      return
    }

    setIsProcessingClick(true)

    try {
      if (playerState === PlayerState.PLAYING) {
        onPause()
        setIsProcessingClick(false)
        setPlayAttempts(0)
      } else {
        onPlay()
        setTimeout(() => {
          setIsProcessingClick(false)
        }, 1000)
        setPlayAttempts(0)
      }
    } catch (error) {
      console.error('播放/暂停失败:', error)
      setIsProcessingClick(false)

      addToast({
        title: '播放失败',
        description: '播放时发生错误，请重试',
        color: 'danger'
      })
    }
  }

  // 其他控制函数（复用原有逻辑）
  const handleBackward = () => {
    onForward(-15)
  }

  const handleForward = () => {
    onForward(15)
  }

  // 设备控制相关函数（复用原有逻辑）
  const handleIntensityChange = (functionKey: string, intensity: number) => {
    setCurrentIntensity(prev => ({
      ...prev,
      [functionKey]: intensity
    }))

    if (device && sendCommand) {
      sendCommand(functionKey, intensity)
    }
  }

  const handleControlModeChange = (mode: ControlMode) => {
    setControlMode(mode)
  }

  const handleCommandJump = (timeStr: string) => {
    const [hours, minutes, seconds] = timeStr.split(':').map(Number)
    const totalSeconds = hours * 3600 + minutes * 60 + seconds
    onSeek(totalSeconds)
    setShowCommandPanel(false)
  }

  const handleBackToScriptSelect = () => {
    // 这个功能需要从父组件传入
    console.log('返回剧本选择')
  }

  return (
    <>
      {/* 指令快速访问按钮 - 与控制面板联动 */}
      <AnimatePresence>
        {hasCommands && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{
              opacity: 1,
              y: showControls ? -200 : 0 // 控制面板显示时上移200px
            }}
            exit={{ opacity: 0, y: 50 }}
            transition={{ duration: 0.3, ease: 'easeOut' }}
            className="absolute bottom-5 left-1/2 transform -translate-x-1/2 z-50"
          >
            <Button
              isIconOnly
              variant="flat"
              size="sm"
              onPress={() => {
                setShowCommandPanel(true)
                onShowControls() // 显示指令面板时重置隐藏计时器
              }}
              className="text-white hover:bg-white/20 border border-white/20 w-10 h-10 min-w-10 rounded-full shadow-lg"
              style={{
                background:
                  'linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%)',
                backdropFilter: 'blur(15px) saturate(180%)',
                WebkitBackdropFilter: 'blur(15px) saturate(180%)',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2)'
              }}
              aria-label={t('player.showCommands')}
            >
              <Icon icon="solar:double-alt-arrow-up-linear" width={18} className="text-[#ff2d97]" />
            </Button>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 沉浸式控制面板 */}
      <AnimatePresence>
        {showControls && (
          <motion.div
            initial={{ opacity: 0, y: 100 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 100 }}
            transition={{ duration: 0.3, ease: 'easeOut' }}
            className="absolute bottom-0 inset-x-0 z-20  backdrop-blur-md pt-8 rounded-2xl"
            data-control-panel
          >
            {/* 进度条区域 */}
            <div className="relative px-6 pt-4">
              <StageProgressBar
                currentTime={currentTime}
                duration={duration}
                script={script}
                currentStageIndex={currentStageIndex}
                onSeek={onSeek}
              />
            </div>

            {/* 控制按钮区域 */}
            <div className="relative px-6 py-4">
              <div className="relative flex items-center justify-between">
                {/* 左侧：设备控制按钮 */}
                <div className="p-3 cursor-pointer" onClick={() => setShowDevicePanel(true)}>
                  <div className="w-6 h-6 bg-[#ff2d97] hover:bg-[#ff2d97]/80 rounded-full flex items-center justify-center">
                    <div className="w-2 h-2 bg-white rounded-full" />
                  </div>
                </div>

                {/* 中间：播放控制按钮组 */}
                <div className="flex items-center gap-4">
                  {/* 后退15秒 */}
                  <Button
                    isIconOnly
                    variant="light"
                    size="lg"
                    onPress={handleBackward}
                    className="text-white hover:bg-white/10 w-12 h-12"
                    aria-label={t('player.backward15s')}
                  >
                    <Icon icon="solar:rewind-15-seconds-back-linear" width={24} />
                  </Button>

                  {/* 播放/暂停按钮 */}
                  <Button
                    isIconOnly
                    size="lg"
                    isDisabled={isProcessingClick}
                    onPress={handlePlayPause}
                    className="w-16 h-16 bg-white hover:bg-white/90 text-black min-w-16 rounded-full"
                    aria-label={
                      playerState === PlayerState.PLAYING ? t('player.pause') : t('player.play')
                    }
                  >
                    {isProcessingClick ? (
                      <Spinner size="sm" color="default" />
                    ) : playerState === PlayerState.PLAYING ? (
                      <Icon icon="solar:pause-bold" width={24} className="text-black" />
                    ) : (
                      <Icon icon="solar:play-bold" width={24} className="text-black ml-1" />
                    )}
                  </Button>

                  {/* 快进15秒 */}
                  <Button
                    isIconOnly
                    variant="light"
                    size="lg"
                    onPress={handleForward}
                    className="text-white hover:bg-white/10 w-12 h-12"
                    aria-label={t('player.forward15s')}
                  >
                    <Icon icon="solar:rewind-15-seconds-forward-linear" width={24} />
                  </Button>
                </div>

                {/* 右侧：菜单按钮 */}
                <Button
                  isIconOnly
                  variant="light"
                  size="lg"
                  onPress={() => setShowStageSelector(true)}
                  className="text-white hover:bg-white/10 w-12 h-12"
                  aria-label={t('player.selectStage')}
                >
                  <Icon icon="solar:hamburger-menu-linear" width={24} />
                </Button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 原有的抽屉组件保持不变 */}
      <StageSelector
        isOpen={showStageSelector}
        onClose={() => setShowStageSelector(false)}
        stageTitles={stageTitles}
        currentStageIndex={currentStageIndex}
        onStageChange={onStageChange}
      />

      <DeviceControlPanel
        isOpen={showDevicePanel}
        onClose={() => setShowDevicePanel(false)}
        deviceFunctions={device?.func || []}
        currentIntensity={currentIntensity}
        controlMode={controlMode}
        onIntensityChange={handleIntensityChange}
        onControlModeChange={handleControlModeChange}
        onBackToScriptSelect={handleBackToScriptSelect}
      />

      <CommandPanel
        isOpen={showCommandPanel}
        onClose={() => setShowCommandPanel(false)}
        commands={commands || []}
        onCommandClick={handleCommandJump}
      />
    </>
  )
}
