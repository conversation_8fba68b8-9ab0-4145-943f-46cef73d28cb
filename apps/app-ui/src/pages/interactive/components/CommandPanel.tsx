import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@heroui/react'
import { Icon } from '@iconify/react'
import { useTranslation } from 'react-i18next'

// 定义指令类型
interface Command {
  command: string
  time: string
}

interface CommandPanelProps {
  isOpen: boolean
  onClose: () => void
  commands: Command[]
  onCommandClick: (time: string) => void
}

/**
 * 指令面板组件
 * 显示剧本中的指令列表，点击可跳转到对应时间
 */
export const CommandPanel: React.FC<CommandPanelProps> = ({
  isOpen,
  onClose,
  commands,
  onCommandClick
}) => {
  const { t } = useTranslation('interactive')
  // 处理指令点击
  const handleCommandClick = (command: Command) => {
    onCommandClick(command.time)
    onClose() // 点击后自动收起面板
  }

  // 将时间字符串转换为显示格式
  const formatTime = (timeStr: string) => {
    // 去掉小时部分的前导零，例如 "00:01:31" -> "1:31"
    const parts = timeStr.split(':')
    if (parts.length === 3) {
      const hours = parseInt(parts[0], 10)
      const minutes = parts[1]
      const seconds = parts[2]

      if (hours === 0) {
        return `${minutes}:${seconds}`
      } else {
        return `${hours}:${minutes}:${seconds}`
      }
    }
    return timeStr
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* 背景遮罩 */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 bg-black/30 z-40"
            onClick={onClose}
          />

          {/* 指令面板 */}
          <motion.div
            initial={{ y: '100%', opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: '100%', opacity: 0 }}
            transition={{
              type: 'spring',
              damping: 25,
              stiffness: 200,
              duration: 0.3
            }}
            className="fixed bottom-0 left-0 right-0 z-50"
            style={{
              // 确保面板在ControlPanel上方，但与之紧密贴合
              bottom: '190px' // ControlPanel的高度约为190px
            }}
            data-drawer
          >
            {/* 毛玻璃背景 */}
            <div className="relative mx-4 mb-4 rounded-2xl overflow-hidden bg-black/40 backdrop-blur-md">
              {/* 边框光效 */}
              <div
                className="absolute inset-0 rounded-2xl"
                style={{
                  background:
                    'linear-gradient(135deg, rgba(255, 45, 151, 0.6) 0%, rgba(137, 47, 255, 0.6) 100%)',
                  mask: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
                  maskComposite: 'xor',
                  WebkitMask: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
                  WebkitMaskComposite: 'xor',
                  padding: '1px'
                }}
              />

              {/* 内容区域 */}
              <div
                className="relative p-6 max-h-80 overflow-hidden"
                onTouchStart={e => {
                  // 阻止事件冒泡到背景手势检测层
                  e.stopPropagation()
                }}
                onTouchMove={e => {
                  // 阻止事件冒泡到背景手势检测层
                  e.stopPropagation()
                }}
                onTouchEnd={e => {
                  // 阻止事件冒泡到背景手势检测层
                  e.stopPropagation()
                }}
              >
                {/* 标题栏 */}
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-white text-lg font-semibold flex items-center">
                    <Icon
                      icon="solar:command-square-bold"
                      width={24}
                      className="mr-2 text-[#ff2d97]"
                    />
                    {t('commandPanel.title')}
                  </h3>
                  <Button
                    isIconOnly
                    variant="light"
                    size="sm"
                    onPress={onClose}
                    className="text-gray-300 hover:text-white hover:bg-white/10"
                    aria-label={t('commandPanel.closeAriaLabel')}
                  >
                    <Icon icon="solar:close-circle-linear" width={20} />
                  </Button>
                </div>

                {/* 指令按钮 - 滚动容器 */}
                <div
                  className="max-h-60 overflow-y-scroll"
                  style={{
                    WebkitOverflowScrolling: 'touch', // iOS 平滑滚动
                    touchAction: 'pan-y', // 只允许垂直滚动
                    overscrollBehavior: 'contain', // 防止滚动传播
                    scrollbarWidth: 'thin', // Firefox 细滚动条
                    scrollbarColor: '#ff2d97 transparent' // Firefox 滚动条颜色
                  }}
                  onTouchStart={e => {
                    // 阻止事件冒泡到父级手势检测层
                    e.stopPropagation()
                  }}
                  onTouchMove={e => {
                    // 阻止事件冒泡到父级手势检测层
                    e.stopPropagation()
                  }}
                  onTouchEnd={e => {
                    // 阻止事件冒泡到父级手势检测层
                    e.stopPropagation()
                  }}
                >
                  {/* flex 布局容器 */}
                  <div className="flex flex-wrap gap-2 p-1">
                    {commands.map((command, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: index * 0.02, duration: 0.15 }}
                      >
                        <Button
                          size="sm"
                          variant="flat"
                          className="h-8 px-3 bg-gray-800/90 hover:bg-gray-700/90 text-white border border-gray-600/60 hover:border-[#ff2d97]/80 transition-all duration-200 text-xs font-medium shadow-lg"
                          onPress={() => handleCommandClick(command)}
                        >
                          {command.command}
                        </Button>
                      </motion.div>
                    ))}
                  </div>
                </div>

                {/* 空状态 */}
                {commands.length === 0 && (
                  <div className="text-center py-8">
                    <Icon
                      icon="solar:command-square-broken"
                      width={48}
                      className="mx-auto mb-2 text-gray-500"
                    />
                    <p className="text-gray-400">{t('commandPanel.noCommands')}</p>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  )
}
