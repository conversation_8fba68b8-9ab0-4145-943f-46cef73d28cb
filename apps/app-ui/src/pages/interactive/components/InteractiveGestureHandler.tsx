import React, { useRef, useCallback, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Icon } from '@iconify/react'
import { useTranslation } from 'react-i18next'
import { PlayerState } from '../types'
import { usePlayer } from '../context/PlayerContext'

interface InteractiveGestureHandlerProps {
  children: React.ReactNode
  currentTime: number
  duration: number
  playerState: PlayerState
  currentStageIndex: number
  stageCount: number
  onSeek: (time: number) => void
  onPlay: () => void
  onPause: () => void
  onStageChange: (index: number) => void
  onShowControls: () => void
  onHideControls: () => void
  onLongPressChange: (pressing: boolean) => void
  isControlsVisible: boolean
  isDrawerOpen: boolean
  className?: string
}

interface GestureIndicator {
  type: 'forward' | 'backward' | 'play-pause' | 'stage-next' | 'stage-prev'
  amount: number
}

export const InteractiveGestureHandler: React.FC<InteractiveGestureHandlerProps> = ({
  children,
  currentTime,
  duration,
  playerState,
  currentStageIndex,
  stageCount,
  onSeek,
  onPlay,
  onPause,
  onStageChange,
  onShowControls,
  onHideControls,
  onLongPressChange,
  isControlsVisible,
  isDrawerOpen,
  className = ''
}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const touchStartRef = useRef<{ x: number; y: number; time: number } | null>(null)
  const longPressTimerRef = useRef<NodeJS.Timeout | null>(null)
  const lastTapRef = useRef<{ x: number; time: number } | null>(null)
  const playbackRateRef = useRef<number>(1)

  // 获取播放器上下文中的音频引用
  const playerContext = usePlayer()
  const { t } = useTranslation('interactive')

  const [isDraggingProgress, setIsDraggingProgress] = useState(false)
  const [showSpeedIndicator, setShowSpeedIndicator] = useState(false)
  const [showGestureIndicator, setShowGestureIndicator] = useState<GestureIndicator | null>(null)
  const [isLongPressing, setIsLongPressing] = useState(false)

  // 显示手势提示
  const showIndicator = useCallback((indicator: GestureIndicator, duration = 1000) => {
    setShowGestureIndicator(indicator)
    setTimeout(() => setShowGestureIndicator(null), duration)
  }, [])

  // 双击手势处理
  const handleDoubleTap = useCallback(
    (x: number) => {
      if (!containerRef.current || duration <= 0 || isDrawerOpen) return

      const rect = containerRef.current.getBoundingClientRect()
      const leftThird = rect.width / 3
      const rightThird = (rect.width * 2) / 3

      // 左侧区域：快退15秒
      if (x < leftThird) {
        const seekAmount = 15
        let newTime = Math.max(0, currentTime - seekAmount)

        if (newTime < 0) newTime = 0
        if (Math.abs(newTime - currentTime) < 0.1) return

        onSeek(newTime)

        const actualSeekAmount = Math.abs(newTime - currentTime)
        showIndicator({
          type: 'backward',
          amount: Math.round(actualSeekAmount)
        })

        // 触觉反馈
        if (navigator.vibrate) {
          navigator.vibrate(30)
        }
      }
      // 右侧区域：快进15秒
      else if (x > rightThird) {
        const seekAmount = 15
        let newTime = Math.min(duration, currentTime + seekAmount)

        if (newTime > duration) newTime = duration
        if (Math.abs(newTime - currentTime) < 0.1) return

        onSeek(newTime)

        const actualSeekAmount = Math.abs(newTime - currentTime)
        showIndicator({
          type: 'forward',
          amount: Math.round(actualSeekAmount)
        })

        // 触觉反馈
        if (navigator.vibrate) {
          navigator.vibrate(30)
        }
      }
      // 中间区域：播放/暂停
      else {
        if (playerState === PlayerState.PLAYING) {
          onPause()
          showIndicator(
            {
              type: 'play-pause',
              amount: 0 // 0表示暂停
            },
            800
          )
        } else {
          onPlay()
          showIndicator(
            {
              type: 'play-pause',
              amount: 1 // 1表示播放
            },
            800
          )
        }

        // 触觉反馈
        if (navigator.vibrate) {
          navigator.vibrate(50)
        }
      }
    },
    [currentTime, duration, playerState, onSeek, onPlay, onPause, showIndicator, isDrawerOpen]
  )

  // 获取音频元素 - 通过PlayerProvider创建的音频元素
  const getAudioElement = useCallback(() => {
    // PlayerProvider 在其内部创建了一个隐藏的 audio 元素
    // 尝试多种方式找到这个元素
    let audioElement = document.querySelector('audio') as HTMLAudioElement

    if (!audioElement) {
      // 尝试查找 video 元素（有些播放器可能使用 video 标签）
      audioElement = document.querySelector('video') as HTMLAudioElement
    }

    if (!audioElement) {
      // 尝试查找具有特定属性的元素
      audioElement = document.querySelector('[data-audio-player]') as HTMLAudioElement
    }

    // 如果找到了音频元素，验证它是否支持 playbackRate
    if (audioElement) {
      console.log('🔍 找到音频元素:', audioElement)
      console.log('🔍 音频元素属性:', {
        src: audioElement.src,
        currentTime: audioElement.currentTime,
        duration: audioElement.duration,
        playbackRate: audioElement.playbackRate,
        supportsPlaybackRate: typeof audioElement.playbackRate === 'number'
      })
    } else {
      console.warn('🔍 未找到音频元素')
    }

    return audioElement
  }, [])

  // 长按倍速播放处理
  const handleLongPressStart = useCallback(() => {
    if (duration <= 0 || currentTime >= duration || isDrawerOpen) return

    setIsLongPressing(true)
    setShowSpeedIndicator(true)
    playbackRateRef.current = 2

    // 通知父组件长按开始，阻止控制面板自动隐藏
    onLongPressChange(true)

    // 尝试多次查找音频元素，因为它可能还没有被创建
    const trySetPlaybackRate = (attempt = 1) => {
      const audioElement = getAudioElement()
      console.log(`🚀 第${attempt}次尝试启动2倍速播放，音频元素:`, audioElement)

      if (!audioElement && attempt < 5) {
        // 如果没找到音频元素，延迟重试
        setTimeout(() => trySetPlaybackRate(attempt + 1), 100)
        return
      }

      if (!audioElement) {
        console.warn('⚠️ 多次尝试后仍未找到音频元素，无法设置播放速度')
        return
      }

      try {
        // 实际设置音频播放速度
        audioElement.playbackRate = 2
        console.log('✅ 成功设置2倍速播放，当前播放速度:', audioElement.playbackRate)
      } catch (error) {
        console.error('❌ 设置播放速度失败:', error)
      }
    }

    // 开始尝试设置播放速度
    trySetPlaybackRate()

    // 触觉反馈
    if (navigator.vibrate) {
      navigator.vibrate([50, 50, 50]) // 三次短震动
    }
  }, [duration, currentTime, isDrawerOpen, getAudioElement, onLongPressChange])

  const handleLongPressEnd = useCallback(() => {
    setIsLongPressing(false)
    setShowSpeedIndicator(false)
    playbackRateRef.current = 1

    // 通知父组件长按结束，恢复控制面板自动隐藏
    onLongPressChange(false)

    // 尝试多次查找音频元素来恢复播放速度
    const tryResetPlaybackRate = (attempt = 1) => {
      const audioElement = getAudioElement()
      console.log(`🎯 第${attempt}次尝试恢复正常播放速度，音频元素:`, audioElement)

      if (!audioElement && attempt < 5) {
        // 如果没找到音频元素，延迟重试
        setTimeout(() => tryResetPlaybackRate(attempt + 1), 100)
        return
      }

      if (!audioElement) {
        console.warn('⚠️ 多次尝试后仍未找到音频元素，无法恢复播放速度')
        return
      }

      try {
        // 恢复正常播放速度
        audioElement.playbackRate = 1
        console.log('✅ 成功恢复正常播放速度，当前播放速度:', audioElement.playbackRate)
      } catch (error) {
        console.error('❌ 恢复播放速度失败:', error)
      }
    }

    // 开始尝试恢复播放速度
    tryResetPlaybackRate()
  }, [getAudioElement, onLongPressChange])

  // 水平拖拽进度条处理
  const handleProgressDrag = useCallback(
    (startX: number, currentX: number) => {
      if (!containerRef.current || duration <= 0 || isDrawerOpen) return

      const rect = containerRef.current.getBoundingClientRect()
      const deltaX = currentX - startX

      // 大幅降低拖动敏感度，让用户更精确控制
      let sensitivity = 0.1 // 基础敏感度大幅降低

      // 根据剧本长度进一步调整敏感度
      if (duration < 60) {
        sensitivity = 0.05 // 短剧本（1分钟内）- 极低敏感度
      } else if (duration < 300) {
        sensitivity = 0.08 // 中等长度剧本（5分钟内）- 很低敏感度
      } else if (duration < 1200) {
        sensitivity = 0.1 // 长剧本（20分钟内）- 低敏感度
      } else {
        sensitivity = 0.15 // 超长剧本（20分钟以上）- 稍高敏感度
      }

      const deltaRatio = (deltaX / rect.width) * sensitivity
      const deltaTime = deltaRatio * duration

      const newTime = Math.max(0, Math.min(duration, currentTime + deltaTime))
      onSeek(newTime)
    },
    [currentTime, duration, onSeek, isDrawerOpen]
  )

  // 触摸开始处理
  const handleTouchStart = useCallback(
    (e: React.TouchEvent) => {
      // 如果抽屉打开或点击在控制面板/按钮上，不处理手势
      const target = e.target as HTMLElement
      if (
        isDrawerOpen ||
        target.closest('[data-control-panel]') ||
        target.closest('[data-drawer]') ||
        target.closest('button') ||
        target.closest('[role="button"]')
      ) {
        return
      }

      const touch = e.touches[0]
      if (!touch || duration <= 0) return

      const touchInfo = {
        x: touch.clientX,
        y: touch.clientY,
        time: Date.now()
      }

      touchStartRef.current = touchInfo

      // 检查双击
      if (lastTapRef.current) {
        const timeDiff = touchInfo.time - lastTapRef.current.time
        const distanceDiff = Math.abs(touchInfo.x - lastTapRef.current.x)

        if (timeDiff < 300 && distanceDiff < 50) {
          // 双击检测成功 - 清除单击检测
          console.log('🖱️ 检测到双击')
          handleDoubleTap(touchInfo.x)
          lastTapRef.current = null
          touchStartRef.current = null
          return
        }
      }

      // 记录这次点击，用于双击检测
      lastTapRef.current = { x: touchInfo.x, time: touchInfo.time }

      // 启动长按检测
      longPressTimerRef.current = setTimeout(() => {
        handleLongPressStart()
      }, 500) // 500ms 长按
    },
    [handleDoubleTap, handleLongPressStart, isDrawerOpen, duration]
  )

  // 触摸移动处理
  const handleTouchMove = useCallback(
    (e: React.TouchEvent) => {
      const touch = e.touches[0]
      if (!touch || !touchStartRef.current || duration <= 0 || isDrawerOpen) return

      const deltaX = Math.abs(touch.clientX - touchStartRef.current.x)
      const deltaY = Math.abs(touch.clientY - touchStartRef.current.y)

      // 如果移动距离超过阈值，取消长按
      if (deltaX > 20 || deltaY > 20) {
        if (longPressTimerRef.current) {
          clearTimeout(longPressTimerRef.current)
          longPressTimerRef.current = null
        }
      }

      // 水平拖拽检测（进度控制）
      const minDragDistance = 40
      if (deltaX > minDragDistance && deltaX > deltaY * 2) {
        if (!isDraggingProgress) {
          setIsDraggingProgress(true)
        }
        handleProgressDrag(touchStartRef.current.x, touch.clientX)

        // 防止页面滚动
        e.preventDefault()
      }
    },
    [isDraggingProgress, handleProgressDrag, duration, isDrawerOpen]
  )

  // 触摸结束处理
  const handleTouchEnd = useCallback(() => {
    // 清理长按定时器
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current)
      longPressTimerRef.current = null
    }

    // 如果正在长按，结束长按
    if (isLongPressing) {
      handleLongPressEnd()
      setIsDraggingProgress(false)
      touchStartRef.current = null
      return
    }

    // 如果正在拖拽进度，不处理单击
    if (isDraggingProgress) {
      setIsDraggingProgress(false)
      touchStartRef.current = null
      return
    }

    // 检查是否为有效的单击（有触摸开始点且没有移动太多）
    if (touchStartRef.current) {
      // 延迟处理单击，给双击检测留出时间
      setTimeout(() => {
        // 检查是否仍然有lastTapRef，如果有说明没有发生双击
        if (lastTapRef.current) {
          console.log('🖱️ 检测到单击，切换控制面板状态')
          if (isControlsVisible) {
            onHideControls()
          } else {
            onShowControls()
          }
        }
      }, 350) // 延迟350ms，确保双击检测完成
    }

    // 重置状态
    setIsDraggingProgress(false)
    touchStartRef.current = null

    // 延迟清理双击检测引用
    setTimeout(() => {
      lastTapRef.current = null
    }, 400) // 稍微延长时间，确保单击处理完成
  }, [
    isLongPressing,
    handleLongPressEnd,
    isDraggingProgress,
    isControlsVisible,
    onShowControls,
    onHideControls
  ])

  // 格式化时间显示
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <div
      ref={containerRef}
      className={`relative ${className}`}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      onTouchCancel={handleTouchEnd}
      style={{ touchAction: 'pan-y' }} // 允许垂直滚动，禁用水平滚动
    >
      {children}

      {/* 手势操作提示 */}
      <AnimatePresence>
        {showGestureIndicator && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="absolute inset-0 flex items-center justify-center pointer-events-none z-40"
          >
            <div
              className="rounded-xl px-4 py-2 flex items-center gap-2 shadow-lg"
              style={{
                background:
                  'linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.5) 100%)',
                backdropFilter: 'blur(15px) saturate(180%)',
                WebkitBackdropFilter: 'blur(15px) saturate(180%)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
              }}
            >
              <Icon
                icon={
                  showGestureIndicator.type === 'forward'
                    ? 'solar:skip-next-bold'
                    : showGestureIndicator.type === 'backward'
                    ? 'solar:skip-previous-bold'
                    : showGestureIndicator.type === 'stage-next'
                    ? 'solar:arrow-right-bold'
                    : showGestureIndicator.type === 'stage-prev'
                    ? 'solar:arrow-left-bold'
                    : showGestureIndicator.amount === 1
                    ? 'solar:play-bold'
                    : 'solar:pause-bold'
                }
                width={20}
                className="text-white"
              />
              {showGestureIndicator.type === 'play-pause' ? (
                <span className="text-white text-sm font-medium">
                  {showGestureIndicator.amount === 1 ? t('player.play') : t('player.pause')}
                </span>
              ) : showGestureIndicator.type === 'stage-next' ||
                showGestureIndicator.type === 'stage-prev' ? (
                <span className="text-white text-sm font-medium">
                  {t('player.stage')} {showGestureIndicator.amount}
                </span>
              ) : (
                <span className="text-white text-sm font-medium">
                  {showGestureIndicator.type === 'forward' ? '+' : '-'}
                  {showGestureIndicator.amount}s
                </span>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 倍速播放提示 */}
      <AnimatePresence>
        {showSpeedIndicator && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className="absolute top-8 left-1/2 transform -translate-x-1/2 pointer-events-none z-40"
          >
            <div
              className="rounded-lg px-3 py-1.5 flex items-center gap-2 shadow-lg"
              style={{
                background:
                  'linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.5) 100%)',
                backdropFilter: 'blur(15px) saturate(180%)',
                WebkitBackdropFilter: 'blur(15px) saturate(180%)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
              }}
            >
              <Icon icon="solar:speed-bold" width={16} className="text-white" />
              <span className="text-white text-xs font-medium">2.0x</span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 进度拖拽提示 */}
      <AnimatePresence>
        {isDraggingProgress && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none z-40"
          >
            <div
              className="rounded-lg px-3 py-2 shadow-lg"
              style={{
                background:
                  'linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.5) 100%)',
                backdropFilter: 'blur(15px) saturate(180%)',
                WebkitBackdropFilter: 'blur(15px) saturate(180%)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
              }}
            >
              <div className="text-center">
                <div className="text-white text-sm font-medium">{formatTime(currentTime)}</div>
                <div className="text-white/60 text-xs">/ {formatTime(duration)}</div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
