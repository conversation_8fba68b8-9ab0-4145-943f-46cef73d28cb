import React, { createContext, useState, useContext, useEffect, useRef, useCallback } from 'react'
import type { PlayerContextType, Script, Device, Command } from '../types'
import { ControlMode, PlayerState } from '../types'
import { useAudioPlayer } from '../hooks/useAudioPlayer'
import { useDeviceControl } from '../hooks/useDeviceControl'
import { timeToSeconds } from '../utils/timeUtils'
import { NetworkUtils } from '../../../utils/networkUtils'
import { addToast } from '@heroui/react'
import i18n from '../../../i18n'

// 创建上下文
const PlayerContext = createContext<PlayerContextType | undefined>(undefined)

interface PlayerProviderProps {
  children: React.ReactNode
  initialScript?: Script
  initialCommands?: Command[]
  initialDevice?: Device | null | undefined
  audioSrc?: string
}

export const PlayerProvider: React.FC<PlayerProviderProps> = ({
  children,
  initialScript = [],
  initialCommands = [],
  initialDevice = null,
  audioSrc
}) => {
  // 剧本和设备状态
  const [script, setScript] = useState<Script>(initialScript)
  const [commands, setCommands] = useState<Command[]>(initialCommands)
  const [device, setDevice] = useState<Device | null>(initialDevice)

  // 调试信息
  useEffect(() => {
    console.log('🔍 PlayerProvider - 初始设备:', initialDevice)
    console.log('🔍 PlayerProvider - 当前设备状态:', device)
  }, [initialDevice, device])

  const [currentStageIndex, setCurrentStageIndex] = useState(0)
  const [controlMode, setControlMode] = useState<ControlMode>(ControlMode.AUTO)
  const [audioInitialized, setAudioInitialized] = useState(false)
  const [userInteracted, setUserInteracted] = useState(false)
  const [audioContext, setAudioContext] = useState<AudioContext | null>(null)
  const [playAttempts, setPlayAttempts] = useState(0)
  const maxPlayAttempts = 3

  // 音频元素引用
  const audioElementRef = useRef<HTMLAudioElement | null>(null)

  // 获取当前阶段
  const currentStage =
    script.length > 0 && currentStageIndex < script.length ? script[currentStageIndex] : null

  // 使用音频播放器Hook
  const {
    audioRef,
    currentTime,
    duration,
    playerState,
    play,
    pause,
    seek: audioSeek,
    setCurrentTime
  } = useAudioPlayer({
    audioSrc,
    onTimeUpdate: () => {
      // 可以在这里添加时间更新的回调处理
    }
  })

  // 创建音频上下文（仅在 Web 环境下）
  useEffect(() => {
    // 在 Capacitor 环境下不创建 Web Audio API 上下文，避免 CORS 问题
    if (!NetworkUtils.isCapacitor() && !audioContext && typeof window !== 'undefined') {
      try {
        const AudioContext = window.AudioContext || (window as any).webkitAudioContext
        if (AudioContext) {
          const newContext = new AudioContext()
          setAudioContext(newContext)
          console.log('🎵 Web 环境下创建音频上下文成功')
        }
      } catch (e) {
        console.error('创建音频上下文失败:', e)
      }
    } else if (NetworkUtils.isCapacitor()) {
      console.log('🎵 Capacitor 环境下跳过 Web Audio API 上下文创建')
    }

    return () => {
      if (audioContext) {
        audioContext.close().catch(e => console.error('关闭音频上下文失败:', e))
      }
    }
  }, [])

  // 解锁音频的函数
  const handleUnlockAudio = async () => {
    setUserInteracted(true)

    if (audioRef.current) {
      try {
        // 恢复音频上下文（仅在 Web 环境下）
        if (!NetworkUtils.isCapacitor() && audioContext && audioContext.state === 'suspended') {
          await audioContext.resume()
        }

        // 保存当前播放状态，避免解锁过程中的状态变化影响UI
        const wasPlaying = !audioRef.current.paused
        const currentTimeBackup = audioRef.current.currentTime

        // 尝试播放一个空片段并立即暂停，解锁音频元素
        audioRef.current.volume = 0.01 // 使用极小音量而不是静音
        audioRef.current.muted = false

        try {
          // 临时禁用事件监听器，防止状态变化
          const originalOnPlay = audioRef.current.onplay
          const originalOnPause = audioRef.current.onpause
          audioRef.current.onplay = null
          audioRef.current.onpause = null

          await audioRef.current.play()
          audioRef.current.pause()

          // 恢复原始状态
          audioRef.current.currentTime = currentTimeBackup
          audioRef.current.volume = 1.0

          // 恢复事件监听器
          audioRef.current.onplay = originalOnPlay
          audioRef.current.onpause = originalOnPause

          setAudioInitialized(true)
          console.log('🎵 音频解锁成功，未影响播放状态')
        } catch (err) {
          console.warn('音频解锁失败:', err)

          // 静音尝试播放
          audioRef.current.muted = true
          try {
            await audioRef.current.play()
            audioRef.current.pause()
            audioRef.current.muted = false
            audioRef.current.volume = 1.0
            audioRef.current.currentTime = currentTimeBackup
            setAudioInitialized(true)
            console.log('🎵 静音音频解锁成功')
          } catch (muteErr) {
            console.error('静音播放也失败:', muteErr)
          }
        }
      } catch (e) {
        console.error('播放尝试失败:', e)
      }
    }
  }

  // 监听用户交互和解锁音频
  useEffect(() => {
    // 监听用户交互
    const handleUserInteraction = () => {
      if (!userInteracted) {
        handleUnlockAudio()
      }
    }

    // 监听playbackBlocked自定义事件
    const handlePlaybackBlocked = () => {
      // 显示需要交互的提示
      const t = i18n.getFixedT(null, 'interactive')
      addToast({
        title: t('toast.audioBlocked.title'),
        description: t('toast.audioBlocked.description'),
        color: 'warning'
      })
    }

    document.addEventListener('click', handleUserInteraction)
    document.addEventListener('touchstart', handleUserInteraction)
    document.addEventListener('playbackBlocked', handlePlaybackBlocked)

    // 页面加载后自动尝试静音解锁
    setTimeout(handleUnlockAudio, 1000)

    return () => {
      document.removeEventListener('click', handleUserInteraction)
      document.removeEventListener('touchstart', handleUserInteraction)
      document.removeEventListener('playbackBlocked', handlePlaybackBlocked)
    }
  }, [userInteracted])

  // 初始化音频元素
  useEffect(() => {
    if (audioRef.current && audioSrc && (audioContext || NetworkUtils.isCapacitor())) {
      audioElementRef.current = audioRef.current

      try {
        // 设置音频属性
        audioRef.current.preload = 'auto'
        // 只在非 Capacitor 环境中设置 crossOrigin
        if (!NetworkUtils.isCapacitor()) {
          audioRef.current.crossOrigin = 'anonymous'
        }
        audioRef.current.autoplay = false
        audioRef.current.volume = 1.0
        audioRef.current.muted = false
        audioRef.current.setAttribute('playsinline', 'true')
        audioRef.current.setAttribute('webkit-playsinline', 'true')

        // 设置音频源并加载
        console.log('🎵 设置音频源:', audioSrc)
        audioRef.current.src = audioSrc
        audioRef.current.load()

        // 音频事件监听器
        const handleLoadStart = () => {
          // 音频开始加载
        }

        const handleLoadedData = () => {
          // 音频数据加载完成
        }

        const handleCanPlay = () => {
          // 音频可以播放
        }

        const handleCanPlayThrough = () => {
          // 音频可以完整播放
        }

        const handleError = (e: Event) => {
          console.error('音频加载错误:', e)
          const audio = e.target as HTMLAudioElement
          console.error('音频错误详情:', {
            error: audio.error,
            networkState: audio.networkState,
            readyState: audio.readyState,
            src: audio.src
          })
        }

        // 在 Capacitor 环境下不使用 Web Audio API，避免 CORS 问题
        if (!NetworkUtils.isCapacitor() && audioContext) {
          // 只在 Web 环境下尝试连接到音频上下文
          try {
            const source = audioContext.createMediaElementSource(audioRef.current)
            source.connect(audioContext.destination)
          } catch (err) {
            console.warn('音频上下文连接失败，可能已经连接:', err)
          }
        }

        // 音频加载元数据后设置初始化标志
        const handleLoadedMetadata = () => {
          setAudioInitialized(true)
        }

        // 添加所有事件监听器
        audioRef.current.addEventListener('loadstart', handleLoadStart)
        audioRef.current.addEventListener('loadeddata', handleLoadedData)
        audioRef.current.addEventListener('canplay', handleCanPlay)
        audioRef.current.addEventListener('canplaythrough', handleCanPlayThrough)
        audioRef.current.addEventListener('error', handleError)
        audioRef.current.addEventListener('loadedmetadata', handleLoadedMetadata)

        return () => {
          // 清理所有事件监听器
          audioRef.current?.removeEventListener('loadstart', handleLoadStart)
          audioRef.current?.removeEventListener('loadeddata', handleLoadedData)
          audioRef.current?.removeEventListener('canplay', handleCanPlay)
          audioRef.current?.removeEventListener('canplaythrough', handleCanPlayThrough)
          audioRef.current?.removeEventListener('error', handleError)
          audioRef.current?.removeEventListener('loadedmetadata', handleLoadedMetadata)
        }
      } catch (e) {
        console.error('初始化音频元素失败:', e)
      }
    }
  }, [audioRef, audioSrc, audioContext])

  // 设置阶段 - 增强版本，支持跳转音频时间
  const setStage = (index: number) => {
    if (index >= 0 && index < script.length) {
      const targetStage = script[index]
      console.log('🎯 切换到阶段:', index, targetStage?.stageTitle)

      // 计算目标阶段的时间
      let targetTime: number | undefined = undefined

      // 优先使用 stageTime
      if (
        typeof (targetStage as any).stageTime === 'string' &&
        (targetStage as any).stageTime.length > 0
      ) {
        targetTime = timeToSeconds((targetStage as any).stageTime)
      }
      // 如果没有 stageTime，使用 dialogues 数组的第一个时间
      else if (targetStage.dialogues && targetStage.dialogues.length > 0) {
        targetTime = timeToSeconds(targetStage.dialogues[0].time)
      }

      // 更新阶段索引
      setCurrentStageIndex(index)

      // 如果有目标时间，跳转到对应时间
      if (targetTime !== undefined) {
        console.log('🎯 跳转到阶段时间:', targetTime)
        seek(targetTime)
      }

      // 重置为自动模式
      setControlMode(ControlMode.AUTO)
    }
  }

  // 根据时间计算对应的阶段索引
  const getActiveStageByTime = useCallback(
    (time: number) => {
      if (!script || script.length === 0) return -1

      // 找到当前时间点应该对应的阶段
      for (let i = script.length - 1; i >= 0; i--) {
        const stage = script[i]
        let stageTime: number | undefined = undefined

        // 优先使用 stageTime
        if (typeof (stage as any).stageTime === 'string' && (stage as any).stageTime.length > 0) {
          stageTime = timeToSeconds((stage as any).stageTime)
        }
        // 如果没有 stageTime，使用 dialogues 数组的第一个时间
        else if (stage.dialogues && stage.dialogues.length > 0) {
          stageTime = timeToSeconds(stage.dialogues[0].time)
        }

        // 如果当前时间大于等于这个阶段的开始时间，说明应该对应这个阶段
        if (stageTime !== undefined && time >= stageTime) {
          return i
        }
      }

      return 0 // 默认返回第一个阶段
    },
    [script]
  )

  // 简化版seek函数 - 增强版，同步阶段状态
  const seek = (time: number) => {
    // 边界值处理：确保时间在有效范围内
    const maxTime = duration || Number.MAX_SAFE_INTEGER
    const safeTime = Math.max(0, Math.min(time, maxTime - 0.1))

    console.log('🎯 跳转到时间:', safeTime)

    // 调用原始seek函数
    audioSeek(safeTime)

    // 根据新的时间计算对应的阶段，并同步阶段状态
    const targetStageIndex = getActiveStageByTime(safeTime)
    if (targetStageIndex !== -1 && targetStageIndex !== currentStageIndex) {
      console.log('🎯 根据时间同步阶段:', currentStageIndex, '->', targetStageIndex)
      setCurrentStageIndex(targetStageIndex)
    }
  }

  // 增强版播放函数，确保时间同步
  const enhancedPlay = () => {
    // 恢复音频上下文（仅在 Web 环境下）
    if (!NetworkUtils.isCapacitor() && audioContext && audioContext.state === 'suspended') {
      audioContext.resume().catch(e => console.error('恢复音频上下文失败:', e))
    }

    // 确保音频元素时间与状态时间同步
    if (audioRef.current && currentTime >= 0) {
      const actualTime = audioRef.current.currentTime
      const statTime = currentTime
      const timeDiff = Math.abs(actualTime - statTime)

      // 如果时间差异超过0.05秒，强制同步
      if (timeDiff > 0.05) {
        try {
          audioRef.current.currentTime = statTime
        } catch (e) {
          console.error('时间同步失败:', e)
        }
      }
    }

    // 播放音频
    play()

    // 增加尝试计数
    setPlayAttempts(prev => prev + 1)

    // 如果多次尝试播放失败，显示提示
    if (playAttempts >= maxPlayAttempts) {
      console.warn(`已尝试播放${maxPlayAttempts}次，仍然失败`)
      const t = i18n.getFixedT(null, 'interactive')
      addToast({
        title: t('toast.audioPlaybackFailed.title'),
        description: t('toast.audioPlaybackFailed.description'),
        color: 'danger'
      })
      setPlayAttempts(0)
    }
  }

  // 检测播放状态变化
  useEffect(() => {
    if (playerState === PlayerState.PLAYING) {
      // 播放成功，重置尝试计数
      setPlayAttempts(0)
    }
  }, [playerState])

  // 获取当前时间点的活跃对话（用于设备控制）
  const getActiveDialogue = () => {
    if (!currentStage || !currentStage.dialogues || currentStage.dialogues.length === 0) {
      return null
    }

    // 找到当前时间点之前的最后一个对话
    let activeDialogue = null
    for (let i = currentStage.dialogues.length - 1; i >= 0; i--) {
      const dialogue = currentStage.dialogues[i]
      const dialogueTime = timeToSeconds(dialogue.time)
      if (dialogueTime <= currentTime) {
        activeDialogue = dialogue
        break
      }
    }

    return activeDialogue
  }

  // 使用设备控制Hook
  const { manualIntensity, setManualIntensity, sendCommand } = useDeviceControl({
    device,
    activeDialogue: getActiveDialogue(),
    controlMode,
    playerState
  })

  // 使用 ref 保存最新的设备和命令发送函数，避免依赖项变化
  const deviceRef = useRef(device)
  const sendCommandRef = useRef(sendCommand)

  // 更新 ref 的值
  useEffect(() => {
    deviceRef.current = device
    sendCommandRef.current = sendCommand
  }, [device, sendCommand])

  // 发送停止命令到所有设备功能
  const sendStopCommandsToAllDevices = useCallback(() => {
    const currentDevice = deviceRef.current
    const currentSendCommand = sendCommandRef.current

    if (!currentDevice) return

    console.log('🛑 页面退出 - 发送停止命令到所有设备功能')

    // 发送停止命令到所有功能
    currentDevice.func.forEach(func => {
      // 直接发送停止命令，不使用队列以确保立即执行
      currentSendCommand(func.key, -1) // -1 表示停止
    })
  }, []) // 空依赖项，函数不会重新创建

  // 页面退出时的清理逻辑
  useEffect(() => {
    const handleBeforeUnload = () => {
      sendStopCommandsToAllDevices()
    }

    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden') {
        // 页面隐藏时也停止设备
        sendStopCommandsToAllDevices()
      }
    }

    // 监听页面卸载事件
    window.addEventListener('beforeunload', handleBeforeUnload)
    document.addEventListener('visibilitychange', handleVisibilityChange)

    // 组件卸载时的清理
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
      document.removeEventListener('visibilitychange', handleVisibilityChange)

      // 组件卸载时也发送停止命令
      sendStopCommandsToAllDevices()
    }
  }, [sendStopCommandsToAllDevices])

  // 更新设备
  const updateDevice = (newDevice: Device) => {
    setDevice(newDevice)
  }

  // 更新剧本
  const updateScript = (newScript: Script) => {
    setScript(newScript)
    setCurrentStageIndex(0)
  }

  // 自动阶段切换功能 - 优化版本，只更新索引不跳转音频
  useEffect(() => {
    if (!script || script.length <= currentStageIndex + 1 || !currentStage) {
      return // 没有下一阶段可切换
    }

    // 获取下一个阶段
    const nextStage = script[currentStageIndex + 1]
    let nextStageTime: number | undefined = undefined

    if (nextStage) {
      if (
        typeof (nextStage as any).stageTime === 'string' &&
        (nextStage as any).stageTime.length > 0
      ) {
        nextStageTime = timeToSeconds((nextStage as any).stageTime)
      } else if (nextStage.dialogues.length > 0) {
        nextStageTime = timeToSeconds(nextStage.dialogues[0].time)
      }
    }

    // 只有到达下一个阶段的时间点时才切换阶段索引，不跳转音频
    if (
      nextStageTime !== undefined &&
      currentTime >= nextStageTime &&
      playerState === PlayerState.PLAYING
    ) {
      console.log('🎯 自动切换到下一阶段:', currentStageIndex + 1, nextStage?.stageTitle)
      setCurrentStageIndex(currentStageIndex + 1) // 只更新索引，不调用setStage
    }
  }, [currentTime, currentStage, currentStageIndex, script, playerState])

  // 提供上下文值
  const contextValue: PlayerContextType = {
    script,
    commands,
    device,
    currentTime,
    duration,
    currentStageIndex,
    currentDialogues: [], // 已不使用，保持空数组
    playerState,
    controlMode,
    manualIntensity,
    play: enhancedPlay,
    pause,
    seek,
    setStage,
    setControlMode,
    setManualIntensity,
    sendCommand,
    updateDevice,
    updateScript
  }

  return (
    <PlayerContext.Provider value={contextValue}>
      {/* 隐藏的音频元素 */}
      <audio
        ref={audioRef}
        style={{ display: 'none' }}
        controls={false}
        preload="auto"
        crossOrigin={NetworkUtils.isCapacitor() ? undefined : 'anonymous'}
        playsInline
        muted={false}
      />
      {children}
    </PlayerContext.Provider>
  )
}

// 创建使用上下文的Hook
export const usePlayer = (): PlayerContextType => {
  const context = useContext(PlayerContext)

  if (context === undefined) {
    throw new Error('usePlayer must be used within a PlayerProvider')
  }

  return context
}
