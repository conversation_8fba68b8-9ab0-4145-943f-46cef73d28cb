import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router'
import { useTranslation } from 'react-i18next'
import { useAuth } from '@/contexts/auth-context'
import ProfileSetup from '@/components/ProfileSetup'

// HeroUI 组件
import { Button } from '@heroui/react'

// Iconify 图标
import { Icon } from '@iconify/react'
import { LoadingOverlay } from '@/components/common/loading'

export default function ProfilePage() {
  const navigate = useNavigate()
  const { t } = useTranslation('profile')
  const { status } = useAuth()
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (status === 'authenticated') {
      setIsLoading(false)
    }
  }, [status, navigate])

  if (isLoading) {
    return <LoadingOverlay show={true} text={t('page.loading')} />
  }

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <div className="absolute top-0 left-0 w-screen">
        <img src="/images/login/img-bg.svg" className="w-full h-full object-cover" />
      </div>

      {/* 顶部导航 */}
      <div className="flex items-center justify-between p-4 pt-12 relative z-10">
        <Button
          isIconOnly
          variant="light"
          className="text-white hover:text-foreground"
          onPress={() => navigate(-1)}
        >
          <Icon icon="lucide:arrow-left" className="w-5 h-5" />
        </Button>
      </div>

      {/* 主内容区域 */}
      <div className="flex-1 flex flex-col px-6 pb-20">
        {/* ProfileSetup组件 - 不显示跳过按钮 */}
        <ProfileSetup showSkip={false} title={t('page.title')} subtitle={t('page.subtitle')} />
      </div>
    </div>
  )
}
