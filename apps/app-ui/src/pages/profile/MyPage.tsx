import { useState, useEffect } from 'react'
import { useLocation } from 'react-router'
import { useAuth } from '@/contexts/auth-context'
import { useUserProfileStore, useUserProfileData } from '@/stores/user-profile-store'
import { useMembershipData } from '@/stores/membership-cache-store'
import { useTranslation } from 'react-i18next'
import { isInternationalVersion } from '@/utils/check-version'
import { useSmartNavigation } from '@/lib/navigation'

// HeroUI 组件
import { Card, CardBody, Button, Avatar, addToast, useDisclosure, Skeleton } from '@heroui/react'

// Iconify 图标
import { Icon } from '@iconify/react'

// 语言切换组件
import LanguageSwitcher from '@/components/LanguageSwitcher'

// 版本检查组件
import CheckVersion from '@/components/CheckVersion'

// 通用模态框组件
import GradientModal from '@/components/common/gradient-modal'

export default function MyPage() {
  const { smartNavigate } = useSmartNavigation()
  const location = useLocation()
  const { user, status, logout } = useAuth()
  const { t } = useTranslation('my')

  // 使用新的会员缓存store
  const {
    membershipStatus,
    userPoints,
    isLoading: membershipLoading,
    isRefreshing: membershipRefreshing,
    isInitialized: membershipInitialized,
    fetchData: fetchMembershipData,
    updateInBackground: updateMembershipInBackground
  } = useMembershipData()

  // 使用Zustand store获取用户资料
  const { userProfile, isLoading: profileLoading } = useUserProfileData()
  const { fetchUserProfile } = useUserProfileStore()

  const [isLoading, setIsLoading] = useState(true)

  // 退出登录确认弹窗状态
  const {
    isOpen: isLogoutModalOpen,
    onOpen: onLogoutModalOpen,
    onClose: onLogoutModalClose
  } = useDisclosure()

  const isInternational = isInternationalVersion()

  useEffect(() => {
    if (status === 'authenticated') {
      // 初始化数据加载
      initializePageData()
    }
  }, [status, smartNavigate])

  // 页面可见性变化时的处理
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && status === 'authenticated') {
        // 页面变为可见时，进行异步无感更新
        console.log('📱 [MyPage] 页面可见，开始后台更新数据')
        updateMembershipInBackground()
        loadUserProfile()
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [status, updateMembershipInBackground])

  // 路径变化时的处理
  useEffect(() => {
    if (status === 'authenticated' && location.pathname === '/profile') {
      // 返回到个人中心页面时，进行异步更新
      console.log('🔄 [MyPage] 返回个人中心，开始后台更新')
      updateMembershipInBackground()
      loadUserProfile()
    }
  }, [location.pathname, status, updateMembershipInBackground])

  // 初始化页面数据
  const initializePageData = async () => {
    try {
      console.log('🚀 [MyPage] 开始初始化页面数据')

      // 并行加载用户资料和会员数据
      const promises = [
        loadUserProfile(),
        fetchMembershipData() // 优先使用缓存，如果缓存有效则立即返回
      ]

      await Promise.all(promises)

      console.log('✅ [MyPage] 页面数据初始化完成')
    } catch (error) {
      console.error('❌ [MyPage] 页面数据初始化失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const loadUserProfile = async () => {
    try {
      await fetchUserProfile()
    } catch (error) {
      console.error('获取用户资料失败:', error)
    }
  }

  // 获取用户数据
  const userData = {
    name: userProfile?.nickname || user?.email?.split('@')[0] || 'Welson',
    avatar: userProfile?.avatarUrl || '',
    membershipLevel: membershipStatus?.membership.isMember
      ? membershipStatus.membership.plan?.name || 'Pro'
      : null, // null 表示非会员
    points: userPoints?.availablePoints,
    maxPoints: userPoints?.totalPoints, // 可用积分上限，可以从配置中获取
    cycleEndDate: membershipStatus?.currentSubscription?.endDate || null
  }

  // 计算加载状态：只有在未初始化且正在加载时才显示加载界面
  const shouldShowLoading =
    isLoading || (membershipLoading && !membershipInitialized) || profileLoading

  // 事件处理函数
  const handleMembershipClick = () => {
    smartNavigate('/membership')
  }

  const handlePointsClick = () => {
    smartNavigate('/points')
  }

  const handleProfileClick = () => {
    smartNavigate('/profile/edit')
  }

  const handlePointsStoreClick = () => {
    smartNavigate('/points-store')
  }

  const handleNotificationClick = () => {
    // navigate('/profile/notifications')
  }

  const handlePrivacyClick = () => {
    // navigate('/profile/privacy')
  }

  const handleHelpClick = () => {
    // navigate('/profile/help')
  }

  const handleLogout = () => {
    onLogoutModalOpen()
  }

  const handleLogoutConfirm = async () => {
    try {
      onLogoutModalClose()
      await logout()
      addToast({
        title: t('page.logout_success'),
        color: 'success'
      })
      smartNavigate('/login')
    } catch (error) {
      addToast({
        title: t('page.logout_failed'),
        color: 'danger'
      })
    }
  }

  const handleLogoutCancel = () => {
    onLogoutModalClose()
  }

  // 只有在真正需要等待数据时才显示骨架屏
  if (shouldShowLoading) {
    return (
      <div className="min-h-screen relative overflow-hidden pt-16 pb-20">
        {/* 背景装饰SVG */}
        <div className="absolute inset-0 pointer-events-none">
          {/* 顶部青色渐变 */}
          <div className="absolute top-0 left-0">
            <img src="/images/profile/bg-1.svg" alt="" className="w-auto h-60" />
          </div>
          {/* 中部粉色渐变 */}
          <div className="absolute top-5 left-0">
            <img src="/images/profile/bg-2.svg" alt="" className="w-auto h-64" />
          </div>
          {/* 底部紫色渐变 */}
          <div className="absolute top-0 right-0">
            <img src="/images/profile/bg-3.svg" alt="" className="w-auto h-60" />
          </div>
        </div>

        {/* 内容区域骨架屏 */}
        <div className="relative z-10">
          {/* 用户信息区域骨架屏 */}
          <div className="px-6 mb-8">
            <div className="flex items-center space-x-4 mb-4">
              <Skeleton className="w-16 h-16 rounded-full border-2 border-white/20" />
              <div className="flex-1 space-y-2">
                <Skeleton className="h-8 w-32 rounded" />
                <Skeleton className="h-6 w-24 rounded-full" />
              </div>
              <div className="text-right space-y-2">
                <Skeleton className="h-4 w-16 rounded" />
                <Skeleton className="h-8 w-20 rounded-full" />
              </div>
            </div>
          </div>

          {/* 会员中心和积分商城卡片骨架屏 */}
          <div className="px-4 mb-12 relative z-10">
            <div className="grid grid-cols-2 space-x-4">
              <Skeleton className="h-24 rounded-2xl" />
              <Skeleton className="h-24 rounded-2xl" />
            </div>
          </div>

          {/* 功能菜单骨架屏 */}
          <div className="px-2 space-y-4">
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="flex items-center justify-between py-2 px-2">
                <div className="flex items-center space-x-4">
                  <Skeleton className="w-6 h-6 rounded" />
                  <Skeleton className="h-6 w-20 rounded" />
                </div>
                <Skeleton className="w-5 h-5 rounded" />
              </div>
            ))}
          </div>

          {/* 底部空间 */}
          <div className="h-16" />
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen relative overflow-hidden pt-16 pb-20">
      {/* 背景装饰SVG */}
      <div className="absolute inset-0 pointer-events-none">
        {/* 顶部青色渐变 */}
        <div className="absolute top-0 left-0">
          <img src="/images/profile/bg-1.svg" alt="" className="w-auto h-60" />
        </div>
        {/* 中部粉色渐变 */}
        <div className="absolute top-5 left-0">
          <img src="/images/profile/bg-2.svg" alt="" className="w-auto h-64" />
        </div>
        {/* 底部紫色渐变 */}
        <div className="absolute top-0 right-0">
          <img src="/images/profile/bg-3.svg" alt="" className="w-auto h-60" />
        </div>
      </div>

      {/* 内容区域 */}
      <div className="relative z-10">
        {/* 用户信息区域 */}
        <div className="px-6 mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <Avatar
              src={userData.avatar}
              name={userData.name}
              size="lg"
              className="w-16 h-16 border-2 border-white/20"
            />
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-white mb-1">{userData.name}</h1>
              <div className="flex items-center gap-2">
                {userData.membershipLevel ? (
                  <>
                    <Icon icon="lucide:crown" className="w-4 h-4 text-blue-300" />
                    <span className="text-blue-200 text-sm bg-blue-500/20 px-2 py-1 rounded-full">
                      {userData.membershipLevel}
                      {t('user.member_suffix')}
                    </span>
                  </>
                ) : (
                  <span className="text-white/70 text-sm">{t('user.regular_user')}</span>
                )}
              </div>
            </div>
            {/* 可用积分显示 */}
            <div className="text-right space-y-1">
              <div className="flex items-center gap-1 mb-1">
                <img src="/images/vip/point.svg" alt="积分" className="w-4 h-4" />
                <span className="text-yellow-200 text-sm">{t('user.available_points')}</span>
              </div>
              <div className="text-white font-bold text-center flex items-center justify-center text-lg bg-black/50 rounded-full px-2 py-1">
                {userData.points}
                <span className="text-white/60 text-sm">/{userData.maxPoints}</span>
              </div>
            </div>
          </div>
        </div>

        {/* 会员中心和积分商城卡片 */}
        <div className="px-4 mb-6 relative z-10">
          <div className="grid grid-cols-2 space-x-4">
            {/* 会员中心卡片 */}
            <Card
              className="bg-gradient-to-br from-pink-500 to-purple-600 border-none cursor-pointer hover:scale-105 transition-transform"
              isPressable
              onPress={handleMembershipClick}
            >
              <CardBody className="p-4 text-left relative">
                <img
                  src="/images/profile/card-bg-1.svg"
                  alt="VIP"
                  className="absolute bottom-0 left-0 w-full h-full"
                />
                <div className="absolute -top-1 -right-0">
                  <img src="/images/profile/vip.png" alt="VIP" className="w-16 h-16" />
                </div>
                <div className="relative z-10 flex flex-col gap-1">
                  <h3 className="text-white font-bold text-md">{t('cards.membership.title')}</h3>
                  <p className="text-white text-xs">{t('cards.membership.subtitle')}</p>
                </div>
              </CardBody>
            </Card>

            {/* 积分商城卡片 */}
            <Card
              className="bg-gradient-to-br from-purple-500 to-blue-600 border-none cursor-pointer hover:scale-105 transition-transform"
              isPressable
              onPress={handlePointsStoreClick}
            >
              <CardBody className="p-4 text-left relative">
                <img
                  src="/images/profile/card-bg-2.svg"
                  alt="积分"
                  className="absolute bottom-0 left-0 w-full h-full"
                />
                <div className="absolute -top-0 -right-0">
                  <img src="/images/profile/point.png" alt="积分" className="w-14 h-14" />
                </div>
                <div className="relative z-10 flex flex-col gap-1">
                  <h3 className="text-white font-bold text-md">{t('cards.points_store.title')}</h3>
                  <p className="text-white text-xs">{t('cards.points_store.subtitle')}</p>
                </div>
              </CardBody>
            </Card>
          </div>
        </div>

        {/* 功能菜单 - 直接在背景上，无卡片 */}
        <div className="px-2 space-y-4">
          {/* 个人资料 */}
          <div
            className="flex items-center justify-between cursor-pointer hover:bg-white/5 transition-colors py-2 px-2 rounded-lg"
            onClick={handleProfileClick}
          >
            <div className="flex items-center space-x-4">
              <img src="/images/profile/l-1.svg" alt={t('menu.profile')} className="w-6 h-6" />
              <span className="text-white font-medium text-lg">{t('menu.profile')}</span>
            </div>
            <Icon icon="lucide:chevron-right" className="w-5 h-5 text-white/60" />
          </div>

          {/* 积分记录 */}
          <div
            className="flex items-center justify-between cursor-pointer hover:bg-white/5 transition-colors py-2 px-2 rounded-lg"
            onClick={handlePointsClick}
          >
            <div className="flex items-center space-x-4">
              <img
                src="/images/profile/l-2.svg"
                alt={t('menu.points_record')}
                className="w-6 h-6"
              />
              <span className="text-white font-medium text-lg">{t('menu.points_record')}</span>
            </div>
            <Icon icon="lucide:chevron-right" className="w-5 h-5 text-white/60" />
          </div>

          {/* 通知设置 */}
          {/* <div
            className="flex items-center justify-between cursor-pointer hover:bg-white/5 transition-colors py-2 px-2 rounded-lg opacity-50"
            onClick={handleNotificationClick}
          >
            <div className="flex items-center space-x-4">
              <img src="/images/profile/l-3.svg" alt={t('menu.notification')} className="w-6 h-6" />
              <span className="text-white font-medium text-lg">{t('menu.notification')}</span>
            </div>
            <Icon icon="lucide:chevron-right" className="w-5 h-5 text-white/60" />
          </div> */}

          {/* 隐私设置 */}
          {/* <div
            className="flex items-center justify-between cursor-pointer hover:bg-white/5 transition-colors py-2 px-2 rounded-lg opacity-50"
            onClick={handlePrivacyClick}
          >
            <div className="flex items-center space-x-4">
              <img src="/images/profile/l-4.svg" alt={t('menu.privacy')} className="w-6 h-6" />
              <span className="text-white font-medium text-lg">{t('menu.privacy')}</span>
            </div>
            <Icon icon="lucide:chevron-right" className="w-5 h-5 text-white/60" />
          </div> */}

          {/* 帮助中心 */}
          {/* <div
            className="flex items-center justify-between cursor-pointer hover:bg-white/5 transition-colors py-2 px-2 rounded-lg opacity-50"
            onClick={handleHelpClick}
          >
            <div className="flex items-center space-x-4">
              <img src="/images/profile/l-5.svg" alt={t('menu.help')} className="w-6 h-6" />
              <span className="text-white font-medium text-lg">{t('menu.help')}</span>
            </div>
            <Icon icon="lucide:chevron-right" className="w-5 h-5 text-white/60" />
          </div> */}

          {/* 版本检查 */}
          <CheckVersion />

          {/* 语言设置 */}
          {isInternational && <LanguageSwitcher />}
          {/* <LanguageSwitcher /> */}

          {/* 退出登录 */}
          <div
            className="flex items-center justify-between cursor-pointer hover:bg-white/5 transition-colors py-2 px-2 rounded-lg"
            onClick={handleLogout}
          >
            <div className="flex items-center space-x-4">
              <img src="/images/profile/l-6.svg" alt={t('menu.logout')} className="w-6 h-6" />
              <span className="text-white font-medium text-lg">{t('menu.logout')}</span>
            </div>
            <Icon icon="lucide:chevron-right" className="w-5 h-5 text-white/60" />
          </div>
        </div>

        {/* 底部空间，避免被底部导航遮挡 */}
        <div className="h-16" />
      </div>

      {/* 退出登录确认弹窗 */}
      <GradientModal
        isOpen={isLogoutModalOpen}
        onClose={handleLogoutCancel}
        title={t('logout_modal.title')}
        cancelText={t('logout_modal.cancel')}
        confirmText={t('logout_modal.confirm_logout')}
        onCancel={handleLogoutCancel}
        onConfirm={handleLogoutConfirm}
      >
        <div className="flex items-center justify-center gap-3">
          <p className="text-white/50 text-sm">{t('logout_modal.step1')}</p>
        </div>
      </GradientModal>
    </div>
  )
}
