import { useState, useRef, useEffect } from 'react'
import { Link } from 'react-router'
import { useAuth } from '@/contexts/auth-context'
import { useTranslation } from 'react-i18next'
import { useSmartNavigation } from '@/lib/navigation'

import ProfileSetup from '@/components/ProfileSetup'

// HeroUI 组件
import { addToast, Button, Input } from '@heroui/react'

// Lucide 图标
import { ArrowLeft, Eye, EyeOff } from 'lucide-react'

// 注册步骤枚举
enum RegisterStep {
  EMAIL_INPUT = 'email_input',
  CODE_VERIFICATION = 'code_verification',
  PROFILE_SETUP = 'profile_setup'
}

// 统一邮箱清理函数，先 JSON.stringify 再去除所有空白和不可见字符
const cleanEmail = (raw: string) => {
  // 先转字符串，再去除所有空白和不可见字符
  const str = typeof raw === 'string' ? raw : String(raw)
  // JSON.stringify 主要用于调试，实际清理用 replace
  return str.replace(/["']+/g, '').replace(/[\s\u200B-\u200D\uFEFF]/g, '')
}

export default function RegisterPage() {
  const { smartNavigate } = useSmartNavigation()
  const { sendCode, verifyCode } = useAuth()
  const { t } = useTranslation('register')

  // 状态管理
  const [currentStep, setCurrentStep] = useState<RegisterStep>(RegisterStep.EMAIL_INPUT)
  const [email, setEmail] = useState('')
  const [name, setName] = useState('')
  const [password, setPassword] = useState('')
  const [isPasswordVisible, setIsPasswordVisible] = useState(false)
  const [inviteCode, setInviteCode] = useState('')
  const [verificationCode, setVerificationCode] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [countdown, setCountdown] = useState(0)
  const hasRedirected = useRef(false)

  // 倒计时效果
  useEffect(() => {
    let timer: NodeJS.Timeout
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000)
    }
    return () => clearTimeout(timer)
  }, [countdown])

  // 发送验证码
  const handleSendCode = async () => {
    if (!email) {
      addToast({
        title: t('input_email_required'),
        color: 'warning'
      })
      return
    }

    try {
      setIsLoading(true)
      const success = await sendCode(email)

      if (success) {
        addToast({
          title: t('code_sent_success'),
          color: 'success'
        })
        setCurrentStep(RegisterStep.CODE_VERIFICATION)
        setCountdown(60) // 60秒倒计时
      } else {
        addToast({
          title: t('code_send_failed'),
          color: 'danger'
        })
      }
    } catch (error) {
      addToast({
        title: t('code_send_error'),
        color: 'danger'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 重新发送验证码
  const handleResendCode = async () => {
    if (countdown > 0) return

    try {
      setIsLoading(true)
      const success = await sendCode(email)

      if (success) {
        addToast({
          title: t('code_resend_success'),
          color: 'success'
        })
        setCountdown(60)
      } else {
        addToast({
          title: t('code_resend_failed'),
          color: 'danger'
        })
      }
    } catch (error) {
      addToast({
        title: t('code_resend_error'),
        color: 'danger'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 验证验证码
  const handleVerifyCode = async () => {
    if (!verificationCode || verificationCode.length < 6) {
      addToast({
        title: t('input_code_required'),
        color: 'warning'
      })
      return
    }

    try {
      setIsLoading(true)
      const result = await verifyCode(email, verificationCode, name, password, inviteCode)

      if (result.success) {
        addToast({
          title: t('register_success'),
          color: 'success'
        })
        setCurrentStep(RegisterStep.PROFILE_SETUP)
      } else {
        addToast({
          title: t('code_verify_error'),
          color: 'danger'
        })
      }
    } catch (error) {
      addToast({
        title: t('verify_process_error'),
        color: 'danger'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 个人资料设置完成
  const handleProfileComplete = (data: any) => {
    console.log('个人资料设置完成:', data)
    addToast({
      title: t('welcome_message'),
      color: 'success'
    })
    setTimeout(() => {
      smartNavigate('/')
    }, 1000)
  }

  // 跳过个人资料设置
  const handleProfileSkip = () => {
    smartNavigate('/')
  }

  // 返回上一步
  const handleGoBack = () => {
    if (currentStep === RegisterStep.CODE_VERIFICATION) {
      setCurrentStep(RegisterStep.EMAIL_INPUT)
      setVerificationCode('')
    }
  }

  // 切换密码可见性
  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible)
  }

  // 渲染邮箱输入步骤
  const renderEmailStep = () => (
    <div className="space-y-6">
      {/* 标题区：logo+标题，居中 */}
      <div className="mb-12 flex flex-col items-center relative z-10">
        {/* <img src="/logo.svg" className="w-24 h-24 mb-3" /> */}
        <h1 className="text-2xl font-light text-foreground mb-2">{t('create_account')}</h1>
        <p className="text-default-500 text-sm">{t('create_account_subtitle')}</p>
      </div>

      <div className="space-y-6 relative z-10">
        <div className="space-y-2">
          <label className="text-sm text-default-600 font-medium">{t('email_label')}</label>
          <Input
            type="email"
            placeholder={t('email_placeholder')}
            value={email}
            onChange={e => setEmail(cleanEmail(e.target.value))}
            variant="flat"
            size="lg"
            isRequired
            autoFocus
            classNames={{
              input: 'text-foreground placeholder:text-default-400 text-center',
              inputWrapper: 'bg-content2/50 border-default-300 focus-within:border-primary'
            }}
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm text-default-600 font-medium">{t('name_label')}</label>
          <Input
            type="text"
            placeholder={t('name_placeholder')}
            value={name}
            onChange={e => setName(e.target.value)}
            variant="flat"
            size="lg"
            classNames={{
              input: 'text-foreground placeholder:text-default-400 text-center',
              inputWrapper: 'bg-content2/50 border-default-300 focus-within:border-primary'
            }}
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm text-default-600 font-medium">{t('password_label')}</label>
          <Input
            type={isPasswordVisible ? 'text' : 'password'}
            placeholder={t('password_placeholder')}
            value={password}
            onChange={e => setPassword(e.target.value)}
            endContent={
              <button
                className="focus:outline-none"
                type="button"
                onClick={togglePasswordVisibility}
              >
                {isPasswordVisible ? (
                  <EyeOff className="w-4 h-4 text-default-400" />
                ) : (
                  <Eye className="w-4 h-4 text-default-400" />
                )}
              </button>
            }
            variant="flat"
            size="lg"
            classNames={{
              input: 'text-foreground placeholder:text-default-400 text-center',
              inputWrapper: 'bg-content2/50 border-default-300 focus-within:border-primary'
            }}
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm text-default-600 font-medium">{t('invite_code_label')}</label>
          <Input
            type="text"
            placeholder={t('invite_code_placeholder')}
            value={inviteCode}
            onChange={e => setInviteCode(e.target.value.toUpperCase())}
            variant="flat"
            size="lg"
            classNames={{
              input: 'text-foreground placeholder:text-default-400 text-center',
              inputWrapper: 'bg-content2/50 border-default-300 focus-within:border-primary'
            }}
          />
          <p className="text-xs text-default-500">{t('invite_code_hint')}</p>
        </div>
      </div>

      <Button
        color="primary"
        size="lg"
        className="w-full bg-button-primary"
        onPress={handleSendCode}
        isLoading={isLoading}
        radius="full"
      >
        {isLoading ? t('send_code_loading') : t('send_code')}
      </Button>
    </div>
  )

  // 渲染验证码验证步骤
  const renderCodeStep = () => (
    <div className="space-y-6">
      <div className="mb-12 flex flex-col items-center relative z-10">
        <img src="/logo.svg" className="w-24 h-24 mb-3" />
        <h1 className="text-2xl font-light text-foreground mb-2">{t('verify_email')}</h1>
        <p className="text-default-500 text-sm">{t('verify_email_subtitle', { email })}</p>
      </div>

      <div className="space-y-6">
        <div className="space-y-2">
          <label className="text-sm text-default-600 font-medium">
            {t('verification_code_label')}
          </label>
          <Input
            type="text"
            placeholder={t('verification_code_placeholder')}
            value={verificationCode}
            onChange={e => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
            variant="flat"
            size="lg"
            isRequired
            autoFocus
            maxLength={6}
            classNames={{
              input:
                'text-foreground placeholder:text-default-400 text-center text-lg tracking-widest',
              inputWrapper:
                'bg-content2/50 border-default-300 hover:border-default-400 focus-within:border-primary backdrop-blur-sm'
            }}
          />
        </div>

        <div className="flex justify-between items-center">
          {countdown > 0 ? (
            <span className="text-xs text-default-500">
              {t('resend_countdown', { seconds: countdown })}
            </span>
          ) : (
            <Button
              variant="light"
              size="sm"
              onPress={handleResendCode}
              isLoading={isLoading}
              className="text-primary hover:text-primary-600 text-xs"
            >
              {t('resend_code')}
            </Button>
          )}
        </div>
      </div>

      <div className="space-y-4">
        <Button
          color="primary"
          size="lg"
          className="w-full bg-button-primary"
          onPress={handleVerifyCode}
          isLoading={isLoading}
          isDisabled={verificationCode.length < 6}
          radius="full"
        >
          {isLoading ? t('verify_loading') : t('verify_and_create')}
        </Button>

        <Button
          variant="light"
          size="lg"
          className="w-full text-default-500 hover:text-foreground"
          onPress={handleGoBack}
        >
          {t('back_to_email')}
        </Button>
      </div>
    </div>
  )

  // 渲染个人资料设置步骤
  const renderProfileStep = () => (
    <ProfileSetup
      showSkip={true}
      onComplete={handleProfileComplete}
      onSkip={handleProfileSkip}
      title={t('complete_profile')}
      subtitle={t('complete_profile_subtitle')}
    />
  )

  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* 顶部背景图 */}
      <div className="absolute top-0 left-0 w-screen z-10">
        <img src="/images/login/img-bg.svg" className="w-full h-full object-cover" />
      </div>

      {/* 主内容区域 */}
      <div className="flex-1 flex flex-col justify-center px-6 pb-20">
        {/* 步骤内容 */}
        {currentStep === RegisterStep.EMAIL_INPUT && renderEmailStep()}
        {currentStep === RegisterStep.CODE_VERIFICATION && renderCodeStep()}
        {currentStep === RegisterStep.PROFILE_SETUP && renderProfileStep()}

        {/* 登录链接 */}
        {currentStep === RegisterStep.EMAIL_INPUT && (
          <div className="text-center mt-12 text-sm flex items-center justify-center space-x-2">
            <p className="text-default-500">{t('already_have_account')}</p>
            <Link to="/login" className="text-[#892FFF] hover:text-foreground hover:underline">
              {t('login_now')}
            </Link>
          </div>
        )}
      </div>
    </div>
  )
}
