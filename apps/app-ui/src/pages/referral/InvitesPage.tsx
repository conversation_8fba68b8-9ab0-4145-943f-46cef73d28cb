import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router'
import { apiService } from '@/api'
import { useTranslation } from 'react-i18next'

// HeroUI 组件
import { Card, CardBody, Button, Chip, addToast } from '@heroui/react'

// Iconify 图标
import { Icon } from '@iconify/react'

// 类型定义
import type { InvitedUser, PaginatedResponse } from '@/api'

export default function InvitesPage() {
  const navigate = useNavigate()
  const { t } = useTranslation(['referral'])

  // 状态管理
  const [isLoading, setIsLoading] = useState(true)
  const [invites, setInvites] = useState<InvitedUser[]>([])
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  })
  const [isLoadingMore, setIsLoadingMore] = useState(false)

  useEffect(() => {
    loadInvites()
  }, [])

  const loadInvites = async (page: number = 1, append: boolean = false) => {
    try {
      if (page === 1) {
        setIsLoading(true)
      } else {
        setIsLoadingMore(true)
      }

      const response = await apiService.referral.getInvitedUsers(page, 10)

      if (response.success) {
        if (append) {
          setInvites(prev => [...prev, ...response.data.list])
        } else {
          setInvites(response.data.list)
        }
        setPagination(response.data.pagination)
      } else {
        addToast({
          title: t('referral:invites.get_invites_failed'),
          description: response.error,
          color: 'danger'
        })
      }
    } catch (error) {
      console.error('获取邀请列表失败:', error)
      addToast({
        title: t('referral:invites.get_invites_failed'),
        color: 'danger'
      })
    } finally {
      setIsLoading(false)
      setIsLoadingMore(false)
    }
  }

  const handleLoadMore = () => {
    if (pagination.page < pagination.totalPages && !isLoadingMore) {
      loadInvites(pagination.page + 1, true)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatAmount = (amount: string) => {
    return parseFloat(amount).toFixed(2)
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-background via-content1 to-content2 flex items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <Icon icon="lucide:loader-2" className="w-8 h-8 animate-spin text-primary" />
          <p className="text-default-500">{t('referral:common.loading')}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-background via-content1 to-content2 safe-area-top pb-20">
      {/* 顶部导航 */}
      <div className="flex items-center justify-between p-4 pt-12">
        <Button
          isIconOnly
          variant="light"
          className="text-default-500 hover:text-foreground"
          onPress={() => navigate('/referral')}
        >
          <Icon icon="lucide:arrow-left" className="w-5 h-5" />
        </Button>
        <h1 className="text-lg font-semibold text-foreground">{t('referral:invites.title')}</h1>
        <div className="w-10" /> {/* 占位符保持居中 */}
      </div>

      {/* 统计信息 */}
      <div className="px-6 mb-6">
        <Card className="bg-content1/50 backdrop-blur-sm">
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-foreground">{pagination.total}</p>
                <p className="text-sm text-default-500">{t('referral:invites.total_invites')}</p>
              </div>
              <Icon icon="lucide:users" className="w-8 h-8 text-primary" />
            </div>
          </CardBody>
        </Card>
      </div>

      {/* 邀请列表 */}
      <div className="px-6">
        {invites.length === 0 ? (
          <Card className="bg-content1/50 backdrop-blur-sm">
            <CardBody className="p-8 text-center">
              <Icon icon="lucide:user-plus" className="w-12 h-12 text-default-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">
                {t('referral:invites.no_invites')}
              </h3>
              <p className="text-default-500 mb-4">{t('referral:invites.no_invites_desc')}</p>
              <Button
                color="primary"
                variant="flat"
                onPress={() => navigate('/referral')}
                startContent={<Icon icon="lucide:share-2" className="w-4 h-4" />}
              >
                {t('referral:invites.share_code')}
              </Button>
            </CardBody>
          </Card>
        ) : (
          <div className="space-y-3">
            {invites.map(invite => (
              <Card key={invite.id} className="bg-content1/50 backdrop-blur-sm">
                <CardBody className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                        <Icon icon="lucide:user" className="w-5 h-5 text-primary" />
                      </div>
                      <div>
                        <p className="font-medium text-foreground">
                          {invite.email.replace(/(.{3}).*(@.*)/, '$1***$2')}
                        </p>
                        <p className="text-xs text-default-500">
                          {formatDate(invite.registeredAt)} {t('referral:invites.registered_at')}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex flex-col gap-1">
                        <Chip size="sm" color="success" variant="flat">
                          ¥{formatAmount(invite.commissionEarned)}
                        </Chip>
                        <p className="text-xs text-default-500">
                          {t('referral:invites.consumption')} ¥{formatAmount(invite.totalSpent)}
                        </p>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            ))}

            {/* 加载更多按钮 */}
            {pagination.page < pagination.totalPages && (
              <div className="text-center pt-4">
                <Button
                  variant="flat"
                  onPress={handleLoadMore}
                  isLoading={isLoadingMore}
                  startContent={
                    !isLoadingMore && <Icon icon="lucide:chevron-down" className="w-4 h-4" />
                  }
                >
                  {isLoadingMore ? t('referral:common.loading') : t('referral:invites.load_more')}
                </Button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* 底部空间 */}
      <div className="h-16" />
    </div>
  )
}
