import { useState, useEffect } from 'react'
import { useParams, useLocation, useSearchParams } from 'react-router'
import { Button, Skeleton, Chip } from '@heroui/react'
import { Icon } from '@iconify/react'
import { apiService } from '@/api'
import { getDescription } from '@/components/character-creator-v2/mapping'
import { generateUUID } from '@/lib/utils'
import { motion, AnimatePresence } from 'framer-motion'
import { useRoleStore } from '@/stores/role-store'
import { useUserCharactersStore } from '@/stores/user-characters-store'
import { useSystemCharactersStore } from '@/stores/system-characters-store'
import { getGlobalChatDatabase } from '@/lib/chat-database'
import type { CharacterType, DisplayRole } from '@/lib/types'
import { useTranslation } from 'react-i18next'
import { useSmartNavigation } from '@/lib/navigation'

// 根据角色特征生成标签
const generateRoleTags = (role: DisplayRole, characterData?: CharacterType) => {
  const tags: string[] = []

  // 如果是自定义角色，从角色数据中提取标签
  if (role.custom && characterData) {
    // 根据性格特征添加标签
    if (characterData.personality) {
      const personality = characterData.personality.toLowerCase()
      if (personality.includes('甜美') || personality.includes('可爱')) tags.push('sweet')
      if (personality.includes('清纯') || personality.includes('纯真')) tags.push('pure')
      if (personality.includes('性感') || personality.includes('魅惑')) tags.push('sexy')
      if (personality.includes('温柔') || personality.includes('温暖')) tags.push('gentle')
      if (personality.includes('活泼') || personality.includes('开朗')) tags.push('lively')
      if (personality.includes('冷酷') || personality.includes('高冷')) tags.push('cool')
      if (personality.includes('知性') || personality.includes('聪明')) tags.push('intelligent')
    }

    // 根据年龄添加标签
    const age =
      typeof characterData.age === 'number' ? characterData.age : parseInt(characterData.age || '0')
    if (age >= 18 && age <= 22) tags.push('youthful')
    if (age >= 25 && age <= 35) tags.push('mature')

    // 根据关系添加标签
    if (characterData.relationship) {
      const relationship = characterData.relationship.toLowerCase()
      if (relationship.includes('朋友')) tags.push('companion')
      if (relationship.includes('恋人')) tags.push('romantic')
      if (relationship.includes('同事')) tags.push('workplace')
    }
  }

  // 如果没有标签，添加默认标签
  if (tags.length === 0) {
    tags.push('companion', 'gentle')
  }

  // 最多显示3个标签
  return tags.slice(0, 3)
}

export default function RoleDetailPage() {
  const { roleId } = useParams<{ roleId: string }>()
  const { smartNavigate, goBack } = useSmartNavigation()
  const location = useLocation()
  const [searchParams] = useSearchParams()
  const [role, setRole] = useState<DisplayRole | null>(null)
  const [characterData, setCharacterData] = useState<CharacterType | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isChatLoading, setIsChatLoading] = useState(false)
  const [isInteractiveLoading, setIsInteractiveLoading] = useState(false)
  const [isNewChatLoading, setIsNewChatLoading] = useState(false)

  // 使用全局角色状态管理
  const { setRole: setGlobalRole } = useRoleStore()

  // 使用系统角色 store
  const { getSystemCharacterById, fetchSystemCharacters } = useSystemCharactersStore()

  // 从location state获取角色数据，用于立即显示
  useEffect(() => {
    // 如果有state中传递的角色数据，直接使用
    if (location.state?.role) {
      setRole(location.state.role)
      setIsLoading(false)
    }

    // 无论是否有预加载数据，都尝试获取完整数据
    fetchRoleData()
  }, [location.state, roleId])

  const fetchRoleData = async () => {
    if (!roleId) return

    try {
      // 确保系统角色数据已加载
      await fetchSystemCharacters()

      // 先从系统角色中查找
      const systemRole = getSystemCharacterById(roleId)

      if (systemRole) {
        setRole({
          role: systemRole.id,
          character: systemRole.name,
          description: systemRole.description || systemRole.personality || '',
          avatar: systemRole.imageUrl,
          age: systemRole.age || '?',
          isNew: false
        })
        setIsLoading(false)
        return
      }

      // 如果不是系统角色，使用Zustand store查询用户自定义角色
      const userCharactersStore = useUserCharactersStore.getState()
      const characters = await userCharactersStore.fetchUserCharacters()
      const userRole = characters.find((char: any) => char.id === roleId)
      if (userRole) {
        setCharacterData(userRole)
        setRole({
          role: userRole.id,
          character: userRole.name,
          description: getDescription(userRole as any),
          avatar: userRole.imageUrl || '/images/roles/default.jpg',
          age: userRole.age || '?',
          custom: true
        })
      }
    } catch (error) {
      console.error('获取角色详情失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // 处理聊天开始按钮 - 优先使用历史聊天，没有则创建新聊天
  const handleChatStart = async () => {
    if (!roleId || isChatLoading) return

    setIsChatLoading(true)
    try {
      await setGlobalRole(roleId)

      // 🎯 优化点：首先检查本地缓存中的历史聊天记录
      try {
        const chatDatabase = getGlobalChatDatabase()
        const latestSession = await chatDatabase.getLatestSessionByRole(roleId)

        if (latestSession) {
          console.log(`📱 [RoleDetail] 从缓存中找到历史聊天记录，跳转到: ${latestSession.id}`)
          smartNavigate(`/chat/${latestSession.id}`)
          return
        }

        console.log(`📱 [RoleDetail] 本地缓存中没有找到历史记录，尝试从服务器获取`)
      } catch (cacheError) {
        console.warn('检查本地缓存失败，尝试从服务器获取:', cacheError)
      }

      // 从服务器获取历史聊天记录（作为后备方案）
      try {
        const response = await apiService.history.getByRole(roleId, 1)

        // 如果存在历史聊天记录，跳转到最近一次聊天
        if (response.chats && response.chats.length > 0) {
          console.log(`📝 [RoleDetail] 从服务器找到历史聊天记录，跳转到: ${response.chats[0].id}`)
          smartNavigate(`/chat/${response.chats[0].id}`)
          return
        }
      } catch (apiError) {
        console.warn('从服务器获取历史聊天记录失败，将创建新聊天:', apiError)
      }

      // 如果本地和服务器都没有历史记录，生成新聊天的UUID
      const uuid = generateUUID()
      console.log(`✨ [RoleDetail] 创建新聊天，UUID: ${uuid}`)

      // 直接导航到聊天页面
      smartNavigate(`/chat/${uuid}`)
    } catch (error) {
      console.error('设置角色失败:', error)

      // 出错时也尝试设置角色并导航
      try {
        await setGlobalRole(roleId)
      } catch (setRoleError) {
        console.error('设置角色失败:', setRoleError)
      }

      const uuid = generateUUID()
      smartNavigate(`/chat/${uuid}`)
    } finally {
      setIsChatLoading(false)
    }
  }

  const handleInteractiveStart = async () => {
    if (!roleId || isInteractiveLoading) return

    setIsInteractiveLoading(true)
    try {
      await setGlobalRole(roleId)
      smartNavigate('/interactive')
    } catch (error) {
      console.error('设置角色失败:', error)
      // 即使设置失败也尝试导航
      smartNavigate('/interactive')
    } finally {
      setIsInteractiveLoading(false)
    }
  }

  // 处理新建对话按钮点击 - 始终创建新聊天，不检查历史记录
  const handleNewChat = async () => {
    if (!roleId || isNewChatLoading) return

    setIsNewChatLoading(true)
    try {
      await setGlobalRole(roleId)

      // 生成新的UUID作为聊天ID（强制创建新聊天）
      const newChatId = generateUUID()
      console.log(`🆕 [RoleDetail] 强制创建新聊天，UUID: ${newChatId}`)

      // 直接导航到新聊天页面，使用 replace 避免叠加路由
      smartNavigate(`/chat/${newChatId}`, { replace: true })
    } catch (error) {
      console.error('设置角色或创建新聊天失败:', error)

      // 出错时也尝试设置角色并导航
      try {
        await setGlobalRole(roleId)
      } catch (setRoleError) {
        console.error('设置角色失败:', setRoleError)
      }

      const newChatId = generateUUID()
      smartNavigate(`/chat/${newChatId}`, { replace: true })
    } finally {
      setIsNewChatLoading(false)
    }
  }

  // 处理写真集按钮点击
  const handlePhotoAlbum = async () => {
    if (roleId) {
      await setGlobalRole(roleId)
      smartNavigate(`/photo-album/${roleId}`)
    }
  }

  const handleClose = () => {
    // 获取来源信息和标签状态
    const from = searchParams.get('from')
    const tab = searchParams.get('tab')

    if (from === 'discover' && tab) {
      // 如果是从发现页来的，返回时保持标签状态
      smartNavigate(`/discover?tab=${tab}`)
    } else {
      // 默认返回发现页
      smartNavigate('/discover')
    }
  }

  const { t } = useTranslation('roleDetail')

  // 即使在加载中也显示页面，只是使用骨架屏
  return (
    <div className="h-screen bg-gradient-to-br from-purple-900/20 via-blue-900/10 to-purple-800/20 flex flex-col p-4 pt-8 pb-6">
      <AnimatePresence mode="wait">
        <motion.div
          className="relative flex-1 flex flex-col items-center justify-center"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{
            duration: 0.4,
            ease: [0.4, 0.0, 0.2, 1]
          }}
        >
          <motion.div
            className="relative w-[98%] max-w-sm bg-black rounded-[40px] border-gray-800 shadow-2xl overflow-hidden mx-auto"
            style={{ height: 'calc(100vh - 10rem)' }}
            layoutId={role ? `role-container-${role.role}` : undefined}
          >
            {/* 背景图片 */}
            <div className="absolute inset-0 w-full h-full">
              {role ? (
                <motion.div
                  initial={{ scale: 1.05 }}
                  animate={{ scale: 1 }}
                  transition={{
                    duration: 0.8,
                    ease: [0.4, 0.0, 0.2, 1]
                  }}
                  className="w-full h-full"
                >
                  <motion.img
                    src={role.avatar}
                    alt={role.character}
                    className="w-full h-full object-cover"
                    layoutId={`role-image-${role.role}`}
                  />
                </motion.div>
              ) : (
                <Skeleton className="w-full h-full">
                  <div className="w-full h-full bg-gradient-to-b from-default-300 to-default-400" />
                </Skeleton>
              )}
            </div>

            {/* 渐变覆盖层 - 确保在图片之上，内容之下 */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-t from-black/90 via-transparent to-black/40 z-5"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            />

            {/* 底部文字区域额外渐变 - 独立层级 */}
            <motion.div
              className="absolute bottom-0 left-0 w-full h-80 bg-gradient-to-t from-black/95 via-black/60 to-transparent z-5"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            />

            {/* 顶部按钮区域 */}
            <div className="absolute top-0 left-0 w-full z-20 pt-4 px-6">
              {/* 写真集按钮 */}
              <div className="flex justify-end">
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.6 }}
                >
                  <Button
                    className="bg-black/40 text-white backdrop-blur-md border-none px-4 py-2 rounded-full text-sm flex items-center space-x-2"
                    onPress={handlePhotoAlbum}
                  >
                    <Icon icon="solar:camera-linear" width={16} />
                    {t('photo_album')}
                  </Button>
                </motion.div>
              </div>
            </div>

            {/* 底部信息区域 */}
            <motion.div
              className="absolute bottom-0 left-0 w-full px-6 pb-6 z-30"
              style={{ backgroundColor: 'transparent' }}
              layoutId={role ? `role-info-${role.role}` : undefined}
            >
              {/* 确保底部渐变在内容后面但可见 */}
              <div className="absolute inset-0 -z-10 bg-gradient-to-t from-black/95 via-black/60 to-transparent" />

              {role ? (
                <>
                  {/* 角色名称和年龄 */}
                  <div className="flex items-center space-x-3 mb-3 relative z-10">
                    <motion.h1
                      className="text-white text-3xl font-bold"
                      layoutId={`role-name-${role.role}`}
                    >
                      {role.character}
                    </motion.h1>

                    {role.age && role.age !== '?' && (
                      <motion.div
                        className="bg-pink-500 text-white px-3 py-1 rounded-full text-sm font-medium"
                        layoutId={`role-age-${role.role}`}
                      >
                        {role.age}
                      </motion.div>
                    )}
                  </div>

                  {/* 描述文字 */}
                  <motion.p
                    className="text-white/90 text-sm mb-4 leading-relaxed relative z-10"
                    layoutId={role ? `role-description-${role.role}` : undefined}
                  >
                    {role.description || t('no_description')}
                  </motion.p>

                  {/* 标签 */}
                  <motion.div
                    className="flex space-x-2 mb-6 relative z-10"
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{
                      duration: 0.5,
                      delay: 0.5,
                      ease: [0.4, 0.0, 0.2, 1]
                    }}
                  >
                    {generateRoleTags(role, characterData || undefined).map((tag, index) => (
                      <Chip
                        key={index}
                        className="bg-white/20 text-white border-white/50 text-xs"
                        variant="bordered"
                        size="sm"
                      >
                        {t(`tags.${tag}`)}
                      </Chip>
                    ))}
                  </motion.div>

                  {/* 操作按钮 */}
                  <motion.div
                    className="flex space-x-3 relative z-10"
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{
                      duration: 0.5,
                      delay: 0.6,
                      ease: [0.4, 0.0, 0.2, 1]
                    }}
                  >
                    {/* 静谧私语按钮 */}
                    <Button
                      className="bg-primary text-white font-medium flex-1 py-3 rounded-2xl h-12"
                      startContent={<Icon icon="solar:chat-round-line-linear" width={20} />}
                      onPress={handleChatStart}
                      isDisabled={
                        !role || isChatLoading || isInteractiveLoading || isNewChatLoading
                      }
                      isLoading={isChatLoading}
                    >
                      {isChatLoading ? t('preparing') : t('chat_now')}
                    </Button>

                    {/* 剧情互动按钮 */}
                    {/* <Button
                      className="bg-purple-500 text-white font-medium flex-1 py-3 rounded-2xl h-12"
                      startContent={<Icon icon="solar:gameboy-linear" width={20} />}
                      onPress={handleInteractiveStart}
                      isDisabled={
                        !role || isInteractiveLoading || isChatLoading || isNewChatLoading
                      }
                      isLoading={isInteractiveLoading}
                    >
                      {isInteractiveLoading ? t('preparing') : t('interactive')}
                    </Button> */}
                  </motion.div>
                </>
              ) : (
                <>
                  {/* 加载状态 */}
                  <div className="flex items-center space-x-3 mb-3 relative z-10">
                    <Skeleton className="h-8 w-32 rounded-lg" />
                    <Skeleton className="h-6 w-12 rounded-full" />
                  </div>
                  <Skeleton className="h-16 w-full rounded-lg mb-4 relative z-10" />
                  <div className="flex space-x-2 mb-6 relative z-10">
                    <Skeleton className="h-6 w-16 rounded-full" />
                    <Skeleton className="h-6 w-16 rounded-full" />
                  </div>
                  <div className="flex space-x-3 relative z-10">
                    <Skeleton className="h-12 flex-1 rounded-2xl" />
                    <Skeleton className="h-12 flex-1 rounded-2xl" />
                  </div>
                </>
              )}
            </motion.div>

            {/* 底部Home指示器 */}
            <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2">
              <div className="w-32 h-1 bg-white/60 rounded-full"></div>
            </div>
          </motion.div>

          {/* 关闭按钮 */}
          <motion.div
            className="mt-4 mb-2"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{
              duration: 0.5,
              delay: 0.7,
              ease: [0.4, 0.0, 0.2, 1]
            }}
          >
            <Button
              isIconOnly
              variant="flat"
              className="bg-black/40 text-white backdrop-blur-md border border-white/20 rounded-full w-14 h-14"
              onPress={handleClose}
            >
              <Icon icon="material-symbols:close" width={28} />
            </Button>
          </motion.div>
        </motion.div>
      </AnimatePresence>
    </div>
  )
}
