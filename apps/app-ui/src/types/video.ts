export interface VideoItem {
  id: string
  title: string
  duration: number
  url: string
  poster?: string // 视频封面图
  isOnline: boolean // 区分在线视频和导入视频
}

export interface VideoPlayerState {
  currentVideo: any | null // 使用any类型以支持VideoItem和StoredVideoItem
  isPlaying: boolean
  currentTime: number
  duration: number
  isFullscreen: boolean
  showControls: boolean
  isLoading: boolean
  error: string | null
}

export interface VideoSource {
  url: string
  quality?: string
  type?: string
}

export interface VideoMetadata {
  title: string
  duration: number
  poster?: string
  description?: string
  tags?: string[]
}
