// 轨迹点数据结构
export interface TrackPoint {
  x: number
  y: number
  timestamp: number
  pressure?: number // 压感支持（如果设备支持）
}

// 轨迹数据结构
export interface Track {
  id: string
  name?: string
  points: TrackPoint[]
  startTime: number
  endTime: number
  duration: number // 持续时间（毫秒）
  maxIntensity: number
  averageIntensity: number
  totalDistance: number
  createdAt: number
}

// 波形库项目（扩展Track，添加社交功能）
export interface WaveformLibraryItem extends Track {
  // 作者信息
  authorId: string
  authorName: string
  authorAvatar?: string

  // 发布状态
  isPublic: boolean
  isOwner: boolean
  publishedAt?: number

  // 社交统计
  likeCount: number
  favoriteCount: number
  playCount: number

  // 用户状态
  isLiked: boolean
  isFavorited: boolean

  // 元数据
  description?: string
  tags: string[]
  thumbnail?: string // 波形预览图base64

  // 质量评估
  complexity: number // 复杂度评分 1-5
  smoothness: number // 平滑度评分 1-5

  // 新增：录制数据（新格式）
  recordingData?: import('@/types/recording').WaveformRecording
}

// 实时轨迹状态
export interface SwipeState {
  isDrawing: boolean
  currentTrack: TrackPoint[]
  savedTracks: Track[]
  hasUnsavedData: boolean
  currentIntensity: number
  maxIntensity: number
  averageSpeed: number
  totalDistance: number
  drawingDuration: number
  waveformData: number[]
}

// 波形数据点
export interface WaveformPoint {
  time: number // 时间戳
  intensity: number // 强度值 (1-9)
  speed: number // 速度值
}

// 波形播放状态
export interface WaveformPlaybackState {
  isPlaying: boolean
  currentTime: number
  duration: number
  playbackSpeed: number // 播放速度 0.5x - 2x
  currentIntensity: number
  progress: number // 播放进度 0-1
}

// 波形库筛选选项
export interface WaveformLibraryFilter {
  sortBy: 'latest' | 'popular' | 'duration' | 'complexity'
  showType: 'all' | 'mine' | 'favorites'
  searchQuery: string
  tags: string[]
}

// 波形库API响应
export interface WaveformLibraryResponse {
  items: WaveformLibraryItem[]
  total: number
  hasMore: boolean
}

// Canvas 绘制配置
export interface CanvasConfig {
  width: number
  height: number
  backgroundColor: string
  gridColor: string
  trackColors: {
    slow: string // 慢速轨迹颜色
    medium: string // 中速轨迹颜色
    fast: string // 快速轨迹颜色
  }
  lineWidth: number
  showGrid: boolean
}

// 强度计算配置
export interface IntensityConfig {
  minSpeed: number // 最小速度阈值
  maxSpeed: number // 最大速度阈值
  speedSmoothingFactor: number // 速度平滑系数
  intensityLevels: number // 强度等级数量 (1-9)
  updateInterval: number // 更新间隔（毫秒）
}
