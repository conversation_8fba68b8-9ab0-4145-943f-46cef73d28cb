// 音乐项目类型
export interface MusicItem {
  id: string
  title: string
  artist?: string
  duration?: number // 秒
  url: string
  coverUrl?: string
  isOnline: boolean // 是否为在线音乐
  createdAt: Date
}

// 播放状态
export interface PlaybackState {
  isPlaying: boolean
  currentTime: number
  duration: number
  volume: number
  playbackRate: number
  isLoading: boolean
  error?: string
}

// 音乐模式类型
export type MusicMode = 'initial' | 'online' | 'import' | 'playing'

// 播放模式
export type PlayMode = 'single' | 'repeat' | 'shuffle'

// 在线音乐配置
export interface OnlineMusicConfig {
  id: string
  title: string
  url: string
  duration?: number
}

// 音乐播放器状态
export interface MusicPlayerState {
  currentMode: MusicMode
  currentMusic?: MusicItem
  playlist: MusicItem[]
  playbackState: PlaybackState
  playMode: PlayMode
  deviceIntensity: number // 设备强度 0-9
  isDeviceConnected: boolean
}

// 音频可视化数据
export interface AudioVisualizationData {
  frequencies: number[] // 频率数据
  waveform: number[] // 波形数据
  volume: number // 音量
  beat: boolean // 是否有节拍
}

// 导入音乐选项
export interface ImportMusicOptions {
  acceptedFormats: string[]
  maxFileSize: number // MB
  maxFiles: number
}
