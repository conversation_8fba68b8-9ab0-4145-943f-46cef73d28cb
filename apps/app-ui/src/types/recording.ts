// 录制的原始数据点（压缩数组格式）
export type RecordingPoint = [
  number, // timestamp (相对于录制开始的时间ms)
  number, // intensity (0=无触摸, 1-9=有触摸)
  number?, // x (相对坐标 0-1)
  number?, // y (相对坐标 0-1)
  number? // pressure (可选)
]

// 最终存储/传输的数据格式
export interface WaveformRecording {
  id: string
  name: string
  startTime: number // 录制开始的绝对时间戳
  endTime: number // 录制结束的绝对时间戳
  duration: number // 总时长(ms)
  points: RecordingPoint[] // 录制的数据点
  canvasSize: [number, number] // [width, height]
  metadata: [number, number, number, number] // [maxIntensity, avgIntensity, touchCount, totalDistance]
}

// 录制状态
export interface RecordingState {
  isRecording: boolean
  hasStarted: boolean // 是否已经开始过录制（第一次触摸）
  currentIntensity: number
  duration: number
  hasUnsavedData: boolean
  touchCount: number
  maxIntensity: number
  totalDistance: number
  canvasSize: { width: number; height: number }
}

// 回放状态
export enum PlaybackStatus {
  LOADING = 'loading',
  READY = 'ready',
  PLAYING = 'playing',
  COMPLETED = 'completed',
  ERROR = 'error'
}

export interface PlaybackState {
  status: PlaybackStatus
  recording: WaveformRecording | null
  currentTime: number // 当前播放时间(ms)
  currentIntensity: number
  currentPosition: { x: number; y: number } | null
  progress: number // 播放进度 0-1
  error?: string
}

// Hook接口
export interface RecordingActions {
  startRecording: () => void
  stopRecording: () => Promise<WaveformRecording | null>
  resetRecording: () => void
  updateTouch: (x: number, y: number, pressure: number) => void
  updateIntensity: (intensity: number) => void
  endTouch: () => void
}

export interface PlaybackActions {
  start: () => void
  restart: () => void
  stop: () => void
}
