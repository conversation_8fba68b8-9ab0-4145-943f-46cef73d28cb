/**
 * 导航相关类型定义
 */

export interface NavigationOptions {
  replace?: boolean
  state?: any
  preventScrollReset?: boolean
}

export interface SmartNavigationOptions extends NavigationOptions {
  force?: boolean // 强制使用指定的导航方式，忽略智能判断
}

/**
 * 页面类型枚举
 */
export enum PageType {
  TAB = 'tab', // 底部标签页
  MAIN = 'main', // 主页面（从标签页进入的一级页面）
  ROLE = 'role', // 角色相关页面
  MODAL = 'modal', // 模态页面（全屏弹窗类）
  SPECIAL = 'special' // 特殊页面（如登录、注册）
}

/**
 * 导航策略枚举
 */
export enum NavigationStrategy {
  PUSH = 'push', // 正常跳转，产生历史记录
  REPLACE = 'replace', // 替换当前页面，不产生历史记录
  MODAL = 'modal', // 模态页面跳转
  BACK = 'back' // 返回操作
}

/**
 * 路由配置接口
 */
export interface RouteConfig {
  path: string
  type: PageType
  parent?: string // 父级路由路径
  layout: 'tab' | 'main' | 'role' | 'none'
}
