/**
 * 智能导航系统
 * 提供统一的路由管理和导航逻辑
 */

// 类型定义
export type { NavigationOptions, SmartNavigationOptions, RouteConfig } from './types'
export { PageType, NavigationStrategy } from './types'

// 路由配置
export { ROUTE_CONFIG, TAB_PATHS, MODAL_PATHS, SWIPE_DISABLED_PATHS } from './route-config'

// 工具函数
export {
  matchRouteConfig,
  getPageType,
  getParentPath,
  isTabPage,
  isModalPage,
  isSwipeDisabled,
  isTabToTabNavigation,
  isBackToParentTab,
  getNavigationStrategy,
  cleanPath,
  isValidPath,
  getBreadcrumbPaths
} from './utils'

// Hooks
export { useSmartNavigation } from '../../hooks/use-smart-navigation'
export { useSmartBack } from '../../hooks/use-smart-back'
