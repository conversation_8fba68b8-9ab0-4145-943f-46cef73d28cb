import { ROUTE_CONFIG, TAB_PATHS, MODAL_PATHS, SWIPE_DISABLED_PATHS } from './route-config'
import { PageType, NavigationStrategy } from './types'

/**
 * 路径匹配工具函数
 */

/**
 * 将动态路由路径转换为正则表达式
 * 例如: '/role-detail/:roleId' -> /^\/role-detail\/[^\/]+$/
 */
function pathToRegex(path: string): RegExp {
  const regexStr = path
    .replace(/:[^\/]+/g, '[^/]+') // 将 :param 替换为 [^/]+
    .replace(/\//g, '\\/') // 转义斜杠
  return new RegExp(`^${regexStr}$`)
}

/**
 * 匹配路径到配置
 * 支持动态路由匹配
 */
export function matchRouteConfig(pathname: string) {
  // 先尝试精确匹配
  if (ROUTE_CONFIG[pathname]) {
    return ROUTE_CONFIG[pathname]
  }

  // 尝试动态路由匹配
  for (const [configPath, config] of Object.entries(ROUTE_CONFIG)) {
    if (configPath.includes(':')) {
      const regex = pathToRegex(configPath)
      if (regex.test(pathname)) {
        return config
      }
    }
  }

  return null
}

/**
 * 获取路径的页面类型
 */
export function getPageType(pathname: string): PageType {
  const config = matchRouteConfig(pathname)
  return config?.type || PageType.MAIN
}

/**
 * 获取路径的父级路径
 */
export function getParentPath(pathname: string): string | undefined {
  const config = matchRouteConfig(pathname)
  return config?.parent
}

/**
 * 判断是否为标签页
 */
export function isTabPage(pathname: string): boolean {
  return TAB_PATHS.includes(pathname)
}

/**
 * 判断是否为模态页面
 */
export function isModalPage(pathname: string): boolean {
  return MODAL_PATHS.some(modalPath => {
    if (modalPath.includes(':')) {
      const regex = pathToRegex(modalPath)
      return regex.test(pathname)
    }
    return pathname === modalPath
  })
}

/**
 * 判断是否应该禁用左滑返回
 */
export function isSwipeDisabled(pathname: string): boolean {
  return SWIPE_DISABLED_PATHS.some(disabledPath => {
    if (disabledPath.includes(':')) {
      const regex = pathToRegex(disabledPath)
      return regex.test(pathname)
    }
    return pathname === disabledPath || pathname.startsWith(disabledPath + '/')
  })
}

/**
 * 判断是否为标签页之间的切换
 */
export function isTabToTabNavigation(currentPath: string, targetPath: string): boolean {
  return isTabPage(currentPath) && isTabPage(targetPath)
}

/**
 * 判断是否为返回到父级标签页的导航
 */
export function isBackToParentTab(currentPath: string, targetPath: string): boolean {
  const parentPath = getParentPath(currentPath)
  return parentPath === targetPath && isTabPage(targetPath)
}

/**
 * 获取导航策略
 */
export function getNavigationStrategy(
  currentPath: string,
  targetPath: string,
  options?: { force?: boolean }
): NavigationStrategy {
  // 如果强制使用特定策略，跳过智能判断
  if (options?.force) {
    return NavigationStrategy.PUSH
  }

  // 数字表示返回操作
  if (typeof targetPath === 'number' && targetPath < 0) {
    return NavigationStrategy.BACK
  }

  const targetPathStr = targetPath as string

  // 标签页之间切换使用 replace
  if (isTabToTabNavigation(currentPath, targetPathStr)) {
    return NavigationStrategy.REPLACE
  }

  // 返回到父级标签页使用 replace
  if (isBackToParentTab(currentPath, targetPathStr)) {
    return NavigationStrategy.REPLACE
  }

  // 模态页面使用特殊标记
  if (isModalPage(targetPathStr)) {
    return NavigationStrategy.MODAL
  }

  // 默认使用 push
  return NavigationStrategy.PUSH
}

/**
 * 清理路径（移除查询参数和哈希）
 */
export function cleanPath(pathname: string): string {
  return pathname.split('?')[0].split('#')[0]
}

/**
 * 检查路径是否有效
 */
export function isValidPath(pathname: string): boolean {
  const cleanedPath = cleanPath(pathname)
  return matchRouteConfig(cleanedPath) !== null
}

/**
 * 获取面包屑路径
 * 返回从根标签页到当前页面的完整路径
 */
export function getBreadcrumbPaths(pathname: string): string[] {
  const paths: string[] = []
  let currentPath = cleanPath(pathname)

  while (currentPath) {
    paths.unshift(currentPath)
    const parentPath = getParentPath(currentPath)
    if (!parentPath || parentPath === currentPath) {
      break
    }
    currentPath = parentPath
  }

  return paths
}
