import { PageType, type RouteConfig } from './types'

/**
 * 路由配置映射
 * 定义每个路由的类型、父级关系和布局
 */
export const ROUTE_CONFIG: Record<string, RouteConfig> = {
  // 根路径（重定向到发现页）
  '/': {
    path: '/',
    type: PageType.TAB,
    layout: 'tab'
  },

  // 底部标签页
  '/discover': {
    path: '/discover',
    type: PageType.TAB,
    layout: 'tab'
  },
  '/device-connection': {
    path: '/device-connection',
    type: PageType.TAB,
    layout: 'tab'
  },
  '/interactive': {
    path: '/interactive',
    type: PageType.TAB,
    layout: 'tab'
  },
  '/profile': {
    path: '/profile',
    type: PageType.TAB,
    layout: 'tab'
  },

  // 主页面（从标签页进入的一级页面）
  '/roles/custom': {
    path: '/roles/custom',
    type: PageType.MAIN,
    parent: '/discover', // 从发现页进入
    layout: 'main'
  },
  '/role-detail/:roleId': {
    path: '/role-detail/:roleId',
    type: PageType.MAIN,
    parent: '/discover',
    layout: 'main'
  },
  '/role-profile/:roleId': {
    path: '/role-profile/:roleId',
    type: PageType.MAIN,
    parent: '/discover',
    layout: 'main'
  },
  '/photo-album/:roleId': {
    path: '/photo-album/:roleId',
    type: PageType.MAIN,
    parent: '/discover',
    layout: 'main'
  },

  // 设备模式页面
  '/device/swipe-mode': {
    path: '/device/swipe-mode',
    type: PageType.MAIN,
    parent: '/device-connection',
    layout: 'main'
  },
  '/device/audio-mode': {
    path: '/device/audio-mode',
    type: PageType.MAIN,
    parent: '/device-connection',
    layout: 'main'
  },
  '/device/video-mode': {
    path: '/device/video-mode',
    type: PageType.MAIN,
    parent: '/device-connection',
    layout: 'main'
  },
  '/device/waveform-library': {
    path: '/device/waveform-library',
    type: PageType.MAIN,
    parent: '/device-connection',
    layout: 'main'
  },
  '/device/my-waveforms': {
    path: '/device/my-waveforms',
    type: PageType.MAIN,
    parent: '/device-connection',
    layout: 'main'
  },

  // 远程控制页面
  '/device/remote-host': {
    path: '/device/remote-host',
    type: PageType.MAIN,
    parent: '/device-connection',
    layout: 'main'
  },
  '/device/remote-host/waiting': {
    path: '/device/remote-host/waiting',
    type: PageType.MAIN,
    parent: '/device/remote-host',
    layout: 'main'
  },
  '/device/remote-host/connected': {
    path: '/device/remote-host/connected',
    type: PageType.MAIN,
    parent: '/device/remote-host',
    layout: 'main'
  },
  '/device/remote-controller': {
    path: '/device/remote-controller',
    type: PageType.MAIN,
    parent: '/device-connection',
    layout: 'main'
  },
  '/device/remote-controller/control': {
    path: '/device/remote-controller/control',
    type: PageType.MAIN,
    parent: '/device/remote-controller',
    layout: 'main'
  },

  // 角色页面
  '/chat': {
    path: '/chat',
    type: PageType.ROLE,
    parent: '/discover',
    layout: 'role'
  },
  '/chat/:chatId': {
    path: '/chat/:chatId',
    type: PageType.ROLE,
    parent: '/discover',
    layout: 'role'
  },
  '/chat-history': {
    path: '/chat-history',
    type: PageType.ROLE,
    parent: '/discover',
    layout: 'role'
  },

  // 特殊全屏页面
  '/interactive/player': {
    path: '/interactive/player',
    type: PageType.MAIN,
    parent: '/interactive',
    layout: 'main'
  },

  // 模态页面（从个人中心进入的页面）
  '/profile/edit': {
    path: '/profile/edit',
    type: PageType.MODAL,
    parent: '/profile',
    layout: 'main'
  },
  '/profile/activation-code': {
    path: '/profile/activation-code',
    type: PageType.MODAL,
    parent: '/profile',
    layout: 'main'
  },
  '/profile/activation-history': {
    path: '/profile/activation-history',
    type: PageType.MODAL,
    parent: '/profile',
    layout: 'main'
  },

  // 会员和支付相关
  '/membership': {
    path: '/membership',
    type: PageType.MAIN,
    parent: '/profile',
    layout: 'main'
  },
  '/payment': {
    path: '/payment',
    type: PageType.MODAL,
    parent: '/membership',
    layout: 'main'
  },

  // 积分相关
  '/points': {
    path: '/points',
    type: PageType.MAIN,
    parent: '/profile',
    layout: 'main'
  },
  '/points-store': {
    path: '/points-store',
    type: PageType.MAIN,
    parent: '/profile',
    layout: 'main'
  },

  // 邀请相关
  '/referral': {
    path: '/referral',
    type: PageType.MAIN,
    parent: '/profile',
    layout: 'main'
  },
  '/referral/invites': {
    path: '/referral/invites',
    type: PageType.MAIN,
    parent: '/referral',
    layout: 'main'
  },
  '/referral/commission': {
    path: '/referral/commission',
    type: PageType.MAIN,
    parent: '/referral',
    layout: 'main'
  },

  // 特殊页面
  '/login': {
    path: '/login',
    type: PageType.SPECIAL,
    layout: 'none'
  },
  '/register': {
    path: '/register',
    type: PageType.SPECIAL,
    layout: 'none'
  }
}

/**
 * 底部标签页路径列表
 */
export const TAB_PATHS = ['/', '/discover', '/device-connection', '/interactive', '/profile']

/**
 * 需要禁用左滑返回的页面路径
 */
export const SWIPE_DISABLED_PATHS = [
  '/interactive/player',
  '/device/remote-host/waiting',
  '/device/remote-host/connected',
  '/device/remote-controller/control'
]

/**
 * 模态页面路径列表
 */
export const MODAL_PATHS = [
  '/profile/edit',
  '/profile/activation-code',
  '/profile/activation-history',
  '/payment'
]
