import Dexie, { Table } from 'dexie'

// 剧本内容数据库接口
export interface ScriptContent {
  id: string // 剧本ID
  title: string
  content: any // 剧本JSON内容
  audioUrl: string // 音频文件URL
  audioBlob?: Blob // 本地存储的音频blob
  downloadedAt: Date
  lastAccessedAt: Date
  version: number // 版本号，用于内容更新
}

// 资源文件数据库接口
export interface ResourceFile {
  id: string // 资源ID (URL的hash或自定义ID)
  url: string // 原始URL
  blob?: Blob // 本地存储的文件blob（Web环境）
  filePath?: string // 文件系统路径（Capacitor环境）
  mimeType: string // 文件类型
  size: number // 文件大小
  scriptId: string // 所属剧本ID
  downloadedAt: Date
  lastAccessedAt: Date
}

// 下载任务数据库接口
export interface DownloadTask {
  id: string // 任务ID
  scriptId: string
  scriptTitle: string
  status: 'pending' | 'downloading' | 'completed' | 'failed' | 'paused'
  progress: number // 0-100
  totalFiles: number // 总文件数
  downloadedFiles: number // 已下载文件数
  totalSize: number // 总大小(字节)
  downloadedSize: number // 已下载大小(字节)
  error?: string // 错误信息
  createdAt: Date
  updatedAt: Date
  completedAt?: Date
}

// 波形数据库接口
export interface WaveformData {
  id: string // 波形ID
  name: string // 波形名称
  description?: string // 描述
  authorId: string // 作者ID
  authorName: string // 作者名称
  authorAvatar?: string // 作者头像
  points: any[] // 轨迹点数据 (JSON格式) - 兼容旧格式
  startTime: number // 开始时间戳
  endTime: number // 结束时间戳
  duration: number // 持续时间(毫秒)
  maxIntensity: number // 最大强度
  averageIntensity: number // 平均强度
  totalDistance: number // 总距离
  complexity: number // 复杂度评分 1-5
  smoothness: number // 平滑度评分 1-5
  isPublic: boolean // 是否公开
  isOwner: boolean // 是否为拥有者
  publishedAt?: Date // 发布时间
  likeCount: number // 点赞数
  favoriteCount: number // 收藏数
  playCount: number // 播放数
  tags: string[] // 标签数组 (JSON格式)
  thumbnail?: string // 缩略图base64
  createdAt: Date // 创建时间
  updatedAt: Date // 更新时间
  // 新增：录制数据（新格式）
  recordingData?: any // JSON格式存储WaveformRecording
}

// 用户行为数据库接口
export interface UserAction {
  id: string // 行为ID (格式: actionType_waveformId)
  waveformId: string // 波形ID
  actionType: 'like' | 'favorite' // 行为类型
  value: boolean // 行为值 (true=点赞/收藏, false=取消)
  timestamp: Date // 时间戳
  synced: boolean // 是否已同步到云端
}

// Dexie数据库类
export class ScriptDatabase extends Dexie {
  // 表定义
  scriptContents!: Table<ScriptContent>
  resourceFiles!: Table<ResourceFile>
  downloadTasks!: Table<DownloadTask>
  waveforms!: Table<WaveformData>
  userActions!: Table<UserAction>

  constructor() {
    super('ScriptDatabase')

    this.version(1).stores({
      scriptContents: 'id, title, downloadedAt, lastAccessedAt, version',
      resourceFiles: 'id, url, scriptId, downloadedAt, lastAccessedAt, mimeType, size, filePath',
      downloadTasks: 'id, scriptId, status, progress, createdAt, updatedAt'
    })

    // 版本2: 添加波形相关表
    this.version(2).stores({
      scriptContents: 'id, title, downloadedAt, lastAccessedAt, version',
      resourceFiles: 'id, url, scriptId, downloadedAt, lastAccessedAt, mimeType, size, filePath',
      downloadTasks: 'id, scriptId, status, progress, createdAt, updatedAt',
      waveforms: 'id, authorId, createdAt, updatedAt, name, likeCount, favoriteCount, playCount',
      userActions: 'id, waveformId, actionType, timestamp, synced'
    })

    // 版本3: 添加recordingData支持
    this.version(3).stores({
      scriptContents: 'id, title, downloadedAt, lastAccessedAt, version',
      resourceFiles: 'id, url, scriptId, downloadedAt, lastAccessedAt, mimeType, size, filePath',
      downloadTasks: 'id, scriptId, status, progress, createdAt, updatedAt',
      waveforms: 'id, authorId, createdAt, updatedAt, name, likeCount, favoriteCount, playCount',
      userActions: 'id, waveformId, actionType, timestamp, synced'
    })
  }

  // 清理过期内容
  async cleanupExpiredContent(daysToKeep: number = 30) {
    const expireDate = new Date()
    expireDate.setDate(expireDate.getDate() - daysToKeep)

    // 删除过期的剧本内容
    const expiredScripts = await this.scriptContents
      .where('lastAccessedAt')
      .below(expireDate)
      .toArray()

    if (expiredScripts.length > 0) {
      const expiredScriptIds = expiredScripts.map(s => s.id)

      // 删除相关的资源文件
      await this.resourceFiles.where('scriptId').anyOf(expiredScriptIds).delete()

      // 删除剧本内容
      await this.scriptContents.where('id').anyOf(expiredScriptIds).delete()

      console.log(`清理了 ${expiredScripts.length} 个过期剧本内容`)
    }

    // 清理过期的下载任务
    await this.downloadTasks.where('updatedAt').below(expireDate).delete()
  }

  // 获取数据库使用情况统计
  async getStorageStats() {
    const scriptCount = await this.scriptContents.count()
    const resourceCount = await this.resourceFiles.count()
    const taskCount = await this.downloadTasks.count()

    // 计算总存储大小
    const resources = await this.resourceFiles.toArray()
    const totalSize = resources.reduce((sum, resource) => sum + resource.size, 0)

    return {
      scriptCount,
      resourceCount,
      taskCount,
      totalSize,
      totalSizeMB: Math.round((totalSize / 1024 / 1024) * 100) / 100
    }
  }
}

// 创建数据库实例
export const scriptDB = new ScriptDatabase()

// 初始化数据库
export const initDatabase = async () => {
  try {
    await scriptDB.open()
    console.log('📦 剧本数据库初始化成功')

    // 执行清理过期内容
    await scriptDB.cleanupExpiredContent()

    // 输出存储统计
    const stats = await scriptDB.getStorageStats()
    console.log('📊 数据库统计:', stats)

    return true
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error)
    return false
  }
}
