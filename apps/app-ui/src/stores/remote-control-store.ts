import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import type {
  RemoteControlCode,
  RemoteConnection,
  EmojiReaction
} from '../api/services/remote-control'

// 远程控制状态类型
interface RemoteControlState {
  // 主控端（被控端）状态
  host: {
    isHosting: boolean
    controlCode: RemoteControlCode | null
    connectedController: RemoteConnection | null
    isGeneratingCode: boolean
    error: string | null
  }

  // 控制端状态
  controller: {
    isConnected: boolean
    isConnecting: boolean
    targetDevice: {
      name: string
      hostName: string
      hostAvatar?: string
    } | null
    currentMode: 'classic' | 'swipe'
    error: string | null
  }

  // Emoji交互
  emojiReactions: EmojiReaction[]
  lastReaction: EmojiReaction | null

  // 操作方法
  actions: {
    // 主控端操作
    generateControlCode: () => Promise<void>
    revokeControlCode: () => Promise<void>
    setConnectedController: (controller: RemoteConnection | null) => void
    updateControllerMode: (mode: 'classic' | 'swipe') => void

    // 控制端操作
    connectToDevice: (controlCode: string) => Promise<boolean>
    disconnectFromDevice: () => Promise<void>
    switchMode: (mode: 'classic' | 'swipe') => void

    // Emoji交互
    sendEmojiReaction: (emoji: string) => Promise<void>
    addEmojiReaction: (reaction: EmojiReaction) => void
    clearReactions: () => void

    // 通用操作
    clearError: () => void
    reset: () => void
  }
}

export const useRemoteControlStore = create<RemoteControlState>()(
  devtools(
    (set, get) => ({
      // 初始状态
      host: {
        isHosting: false,
        controlCode: null,
        connectedController: null,
        isGeneratingCode: false,
        error: null
      },

      controller: {
        isConnected: false,
        isConnecting: false,
        targetDevice: null,
        currentMode: 'classic',
        error: null
      },

      emojiReactions: [],
      lastReaction: null,

      actions: {
        // 生成控制码
        generateControlCode: async () => {
          const { remoteControlService } = await import('../api/services/remote-control')

          set(state => ({
            host: { ...state.host, isGeneratingCode: true, error: null }
          }))

          try {
            const controlCode = await remoteControlService.generateControlCode()
            set(state => ({
              host: {
                ...state.host,
                isHosting: true,
                controlCode,
                isGeneratingCode: false
              }
            }))
          } catch (error) {
            set(state => ({
              host: {
                ...state.host,
                isGeneratingCode: false,
                error: '生成控制码失败'
              }
            }))
          }
        },

        // 撤销控制码
        revokeControlCode: async () => {
          const { remoteControlService } = await import('../api/services/remote-control')
          const { host } = get()

          if (host.controlCode) {
            try {
              await remoteControlService.revokeControlCode(host.controlCode.code)
              set(state => ({
                host: {
                  ...state.host,
                  isHosting: false,
                  controlCode: null,
                  connectedController: null,
                  error: null
                }
              }))
            } catch (error) {
              set(state => ({
                host: { ...state.host, error: '撤销控制码失败' }
              }))
            }
          }
        },

        // 设置已连接的控制端
        setConnectedController: controller => {
          set(state => ({
            host: { ...state.host, connectedController: controller }
          }))
        },

        // 更新控制端模式
        updateControllerMode: mode => {
          set(state => ({
            host: {
              ...state.host,
              connectedController: state.host.connectedController
                ? { ...state.host.connectedController, currentMode: mode }
                : null
            }
          }))
        },

        // 连接到设备
        connectToDevice: async controlCode => {
          const { remoteControlService } = await import('../api/services/remote-control')

          set(state => ({
            controller: { ...state.controller, isConnecting: true, error: null }
          }))

          try {
            const result = await remoteControlService.connectToDevice(controlCode)

            if (result.success && result.deviceInfo) {
              set(state => ({
                controller: {
                  ...state.controller,
                  isConnected: true,
                  isConnecting: false,
                  targetDevice: result.deviceInfo!
                }
              }))
              return true
            } else {
              set(state => ({
                controller: {
                  ...state.controller,
                  isConnecting: false,
                  error: result.error || '连接失败'
                }
              }))
              return false
            }
          } catch (error) {
            set(state => ({
              controller: {
                ...state.controller,
                isConnecting: false,
                error: '连接失败'
              }
            }))
            return false
          }
        },

        // 断开连接
        disconnectFromDevice: async () => {
          const { remoteControlService } = await import('../api/services/remote-control')

          try {
            await remoteControlService.disconnect()
            set(state => ({
              controller: {
                ...state.controller,
                isConnected: false,
                targetDevice: null,
                currentMode: 'classic',
                error: null
              }
            }))
          } catch (error) {
            set(state => ({
              controller: { ...state.controller, error: '断开连接失败' }
            }))
          }
        },

        // 切换模式
        switchMode: mode => {
          set(state => ({
            controller: { ...state.controller, currentMode: mode }
          }))
        },

        // 发送Emoji反应
        sendEmojiReaction: async emoji => {
          const { remoteControlService } = await import('../api/services/remote-control')

          try {
            await remoteControlService.sendEmojiReaction(emoji)

            const reaction: EmojiReaction = {
              emoji,
              timestamp: Date.now(),
              from: 'host' // 根据当前角色确定
            }

            get().actions.addEmojiReaction(reaction)
          } catch (error) {
            console.error('发送Emoji失败:', error)
          }
        },

        // 添加Emoji反应
        addEmojiReaction: reaction => {
          set(state => ({
            emojiReactions: [...state.emojiReactions.slice(-9), reaction], // 只保留最近10条
            lastReaction: reaction
          }))
        },

        // 清空反应记录
        clearReactions: () => {
          set({ emojiReactions: [], lastReaction: null })
        },

        // 清除错误
        clearError: () => {
          set(state => ({
            host: { ...state.host, error: null },
            controller: { ...state.controller, error: null }
          }))
        },

        // 重置状态
        reset: () => {
          set({
            host: {
              isHosting: false,
              controlCode: null,
              connectedController: null,
              isGeneratingCode: false,
              error: null
            },
            controller: {
              isConnected: false,
              isConnecting: false,
              targetDevice: null,
              currentMode: 'classic',
              error: null
            },
            emojiReactions: [],
            lastReaction: null
          })
        }
      }
    }),
    {
      name: 'remote-control-store'
    }
  )
)
