import { create } from 'zustand'
import type {
  MusicPlayerState,
  MusicItem,
  MusicMode,
  PlayMode,
  PlaybackState,
  AudioVisualizationData
} from '@/types/music'
import { musicService } from '@/services/music'

// 初始播放状态
const initialPlaybackState: PlaybackState = {
  isPlaying: false,
  currentTime: 0,
  duration: 0,
  volume: 0.8,
  playbackRate: 1.0,
  isLoading: false
}

// 初始状态
const initialState: MusicPlayerState = {
  currentMode: 'initial',
  currentMusic: undefined,
  playlist: [],
  playbackState: initialPlaybackState,
  playMode: 'single',
  deviceIntensity: 0,
  isDeviceConnected: false
}

interface MusicPlayerActions {
  // 模式切换
  setMode: (mode: MusicMode) => void

  // 音乐管理
  loadOnlineMusic: () => Promise<void>
  loadImportedMusic: () => void
  setCurrentMusic: (music: MusicItem) => void
  clearCurrentMusic: () => void

  // 播放控制
  play: () => void
  pause: () => void
  stop: () => void
  seekTo: (time: number) => void
  setVolume: (volume: number) => void
  setPlaybackRate: (rate: number) => void

  // 播放列表管理
  addToPlaylist: (music: MusicItem) => void
  removeFromPlaylist: (musicId: string) => void
  clearPlaylist: () => void
  playNext: () => void
  playPrevious: () => void

  // 播放模式
  setPlayMode: (mode: PlayMode) => void

  // 设备控制
  setDeviceIntensity: (intensity: number) => void
  setDeviceConnected: (connected: boolean) => void

  // 播放状态更新
  updatePlaybackState: (state: Partial<PlaybackState>) => void

  // 可视化数据
  updateVisualizationData: (data: AudioVisualizationData) => void

  // 重置
  reset: () => void
}

interface MusicPlayerStore extends MusicPlayerState {
  // 音频元素引用
  audioElement: HTMLAudioElement | null
  setAudioElement: (element: HTMLAudioElement | null) => void

  // 可视化数据
  visualizationData: AudioVisualizationData | null

  // 计算属性
  currentIndex: number
  hasNext: boolean
  hasPrevious: boolean

  // Actions
  actions: MusicPlayerActions
}

export const useMusicPlayerStore = create<MusicPlayerStore>((set, get) => ({
  ...initialState,

  // 音频元素
  audioElement: null,
  setAudioElement: element => set({ audioElement: element }),

  // 可视化数据
  visualizationData: null,

  // 计算属性
  get currentIndex() {
    const { currentMusic, playlist } = get()
    if (!currentMusic) return -1
    return playlist.findIndex(item => item.id === currentMusic.id)
  },

  get hasNext() {
    const { currentIndex, playlist, playMode } = get()
    if (playMode === 'repeat' || playMode === 'shuffle') return true
    return currentIndex < playlist.length - 1
  },

  get hasPrevious() {
    const { currentIndex, playMode } = get()
    if (playMode === 'repeat' || playMode === 'shuffle') return true
    return currentIndex > 0
  },

  // Actions
  actions: {
    setMode: mode => set({ currentMode: mode }),

    loadOnlineMusic: async () => {
      try {
        const onlineMusic = await musicService.getOnlineMusic()
        set({ playlist: onlineMusic })
      } catch (error) {
        console.error('加载在线音乐失败:', error)
      }
    },

    loadImportedMusic: () => {
      const importedMusic = musicService.getImportedMusic()
      set({ playlist: importedMusic })
    },

    setCurrentMusic: music => {
      set(state => {
        // 如果当前播放列表为空，根据音乐类型加载对应的播放列表
        let newPlaylist = state.playlist
        if (newPlaylist.length === 0) {
          if (music.isOnline) {
            // 对于在线音乐，加载所有在线音乐作为播放列表
            musicService.getOnlineMusic().then(onlineMusic => {
              set(prevState => ({ ...prevState, playlist: onlineMusic }))
            })
          } else {
            // 对于导入音乐，加载所有导入音乐作为播放列表
            newPlaylist = musicService.getImportedMusic()
          }
        }

        return {
          currentMusic: music,
          playlist: newPlaylist,
          playbackState: { ...initialPlaybackState, isLoading: true }
        }
      })
    },

    clearCurrentMusic: () => {
      set({
        currentMusic: undefined,
        playbackState: initialPlaybackState
      })
    },

    play: () => {
      const { audioElement } = get()
      if (audioElement) {
        audioElement.play().catch(console.error)
      }
      set(state => ({
        playbackState: { ...state.playbackState, isPlaying: true }
      }))
    },

    pause: () => {
      const { audioElement } = get()
      if (audioElement) {
        audioElement.pause()
      }
      set(state => ({
        playbackState: { ...state.playbackState, isPlaying: false }
      }))
    },

    stop: () => {
      const { audioElement } = get()
      if (audioElement) {
        audioElement.pause()
        audioElement.currentTime = 0
      }
      set(state => ({
        playbackState: {
          ...state.playbackState,
          isPlaying: false,
          currentTime: 0
        }
      }))
    },

    seekTo: time => {
      const { audioElement } = get()
      if (audioElement) {
        audioElement.currentTime = time
      }
      set(state => ({
        playbackState: { ...state.playbackState, currentTime: time }
      }))
    },

    setVolume: volume => {
      const { audioElement } = get()
      if (audioElement) {
        audioElement.volume = volume
      }
      set(state => ({
        playbackState: { ...state.playbackState, volume }
      }))
    },

    setPlaybackRate: rate => {
      const { audioElement } = get()
      if (audioElement) {
        audioElement.playbackRate = rate
      }
      set(state => ({
        playbackState: { ...state.playbackState, playbackRate: rate }
      }))
    },

    addToPlaylist: music => {
      set(state => ({
        playlist: [...state.playlist, music]
      }))
    },

    removeFromPlaylist: musicId => {
      set(state => ({
        playlist: state.playlist.filter(item => item.id !== musicId)
      }))
    },

    clearPlaylist: () => {
      set({ playlist: [] })
    },

    playNext: () => {
      const { currentIndex, playlist, playMode } = get()
      if (playlist.length === 0) return

      let nextIndex: number

      switch (playMode) {
        case 'shuffle':
          nextIndex = Math.floor(Math.random() * playlist.length)
          break
        case 'repeat':
          nextIndex = (currentIndex + 1) % playlist.length
          break
        case 'single':
        default:
          nextIndex = currentIndex + 1
          if (nextIndex >= playlist.length) return
          break
      }

      const nextMusic = playlist[nextIndex]
      if (nextMusic) {
        get().actions.setCurrentMusic(nextMusic)
      }
    },

    playPrevious: () => {
      const { currentIndex, playlist, playMode } = get()
      if (playlist.length === 0) return

      let prevIndex: number

      switch (playMode) {
        case 'shuffle':
          prevIndex = Math.floor(Math.random() * playlist.length)
          break
        case 'repeat':
          prevIndex = currentIndex - 1 < 0 ? playlist.length - 1 : currentIndex - 1
          break
        case 'single':
        default:
          prevIndex = currentIndex - 1
          if (prevIndex < 0) return
          break
      }

      const prevMusic = playlist[prevIndex]
      if (prevMusic) {
        get().actions.setCurrentMusic(prevMusic)
      }
    },

    setPlayMode: mode => set({ playMode: mode }),

    setDeviceIntensity: intensity => set({ deviceIntensity: intensity }),

    setDeviceConnected: connected => set({ isDeviceConnected: connected }),

    updatePlaybackState: state => {
      set(currentState => ({
        playbackState: { ...currentState.playbackState, ...state }
      }))
    },

    updateVisualizationData: data => {
      set({ visualizationData: data })
    },

    reset: () => {
      const { audioElement } = get()
      if (audioElement) {
        audioElement.pause()
        audioElement.currentTime = 0
      }
      set(initialState)
    }
  }
}))

// 导出便捷的 hooks
export const useMusicPlayer = () => {
  const store = useMusicPlayerStore()
  return {
    // 状态
    currentMode: store.currentMode,
    currentMusic: store.currentMusic,
    playlist: store.playlist,
    playbackState: store.playbackState,
    playMode: store.playMode,
    deviceIntensity: store.deviceIntensity,
    isDeviceConnected: store.isDeviceConnected,
    visualizationData: store.visualizationData,

    // 计算属性
    currentIndex: store.currentIndex,
    hasNext: store.hasNext,
    hasPrevious: store.hasPrevious,

    // Actions
    ...store.actions,

    // 音频元素管理
    setAudioElement: store.setAudioElement
  }
}

export const useMusicPlaybackState = () => {
  return useMusicPlayerStore(state => state.playbackState)
}

export const useMusicVisualization = () => {
  return useMusicPlayerStore(state => state.visualizationData)
}
