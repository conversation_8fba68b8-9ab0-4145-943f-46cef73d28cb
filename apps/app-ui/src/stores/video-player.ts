import { create } from 'zustand'
import type { VideoItem, VideoPlayerState } from '@/types/video'
import type { StoredVideoItem } from '@/services/video-storage'

interface VideoPlayerStore extends VideoPlayerState {
  // 基础状态设置
  setCurrentVideo: (video: any | null) => void
  setIsPlaying: (playing: boolean) => void
  setCurrentTime: (time: number) => void
  setDuration: (duration: number) => void
  setIsFullscreen: (fullscreen: boolean) => void
  setShowControls: (show: boolean) => void
  setIsLoading: (loading: boolean) => void
  setError: (error: string | null) => void

  // 播放控制
  play: () => void
  pause: () => void
  togglePlay: () => void
  toggleFullscreen: () => void

  // 进度控制
  seekTo: (time: number) => void

  // 设备连接状态（复用音乐模式的逻辑）
  deviceConnected: boolean
  setDeviceConnected: (connected: boolean) => void

  // 重置状态
  reset: () => void
}

const initialState: VideoPlayerState = {
  currentVideo: null,
  isPlaying: false,
  currentTime: 0,
  duration: 0,
  isFullscreen: false,
  showControls: true,
  isLoading: false,
  error: null
}

export const useVideoPlayer = create<VideoPlayerStore>((set, get) => ({
  ...initialState,
  deviceConnected: false,

  // 基础状态设置
  setCurrentVideo: video => set({ currentVideo: video }),
  setIsPlaying: playing => set({ isPlaying: playing }),
  setCurrentTime: time => set({ currentTime: time }),
  setDuration: duration => set({ duration }),
  setIsFullscreen: fullscreen => set({ isFullscreen: fullscreen }),
  setShowControls: show => set({ showControls: show }),
  setIsLoading: loading => set({ isLoading: loading }),
  setError: error => set({ error }),

  // 播放控制
  play: () => set({ isPlaying: true, error: null }),
  pause: () => set({ isPlaying: false }),
  togglePlay: () => {
    const { isPlaying } = get()
    set({ isPlaying: !isPlaying })
  },
  toggleFullscreen: () => {
    const { isFullscreen } = get()
    set({ isFullscreen: !isFullscreen })
  },

  // 进度控制
  seekTo: time => {
    const { duration } = get()
    const clampedTime = Math.max(0, Math.min(duration, time))
    set({ currentTime: clampedTime })
  },

  // 设备连接状态
  setDeviceConnected: connected => set({ deviceConnected: connected }),

  // 重置状态
  reset: () => set(initialState)
}))
