import { createBrowserRouter, Navigate } from 'react-router'
import App from './App'
import { AuthedLayout } from './layouts/AuthedLayout'
import { MainLayout } from './layouts/MainLayout'
import { TabLayout } from './layouts/TabLayout'
import { RoleLayout } from './layouts/RoleLayout'

// 定义路由配置
const router = createBrowserRouter([
  {
    path: '/',
    element: <App />,
    children: [
      {
        // 根路径重定向到聊天历史页面
        index: true,
        element: <Navigate to="/discover" replace />
      },
      {
        // 移除 index: true，只保留 path
        path: '/',
        element: <AuthedLayout />,
        children: [
          // 带底部标签栏的页面
          {
            element: <TabLayout />,
            children: [
              // 导出页面
              {
                path: 'discover',
                lazy: async () => {
                  const { default: DiscoverPage } = await import('./pages/discover/DiscoverPage')
                  return { Component: DiscoverPage }
                }
              },
              // 聊天历史页面
              // {
              //   path: 'chat-history',
              //   lazy: async () => {
              //     const { default: ChatHistoryPage } = await import('./pages/chat/ChatHistoryPage')
              //     return { Component: ChatHistoryPage }
              //   }
              // },
              {
                path: 'interactive',
                lazy: async () => {
                  const { default: InteractivePage } = await import(
                    './pages/interactive/InteractivePage'
                  )
                  return { Component: InteractivePage }
                }
              },
              // 设备连接页面
              {
                path: 'device-connection',
                lazy: async () => {
                  const { default: DeviceConnectionPage } = await import(
                    './pages/device/DeviceConnectionPage'
                  )
                  return { Component: DeviceConnectionPage }
                }
              },

              // 我的页面
              {
                path: 'profile',
                lazy: async () => {
                  const { default: MyPage } = await import('./pages/profile/MyPage')
                  return { Component: MyPage }
                }
              }
            ]
          },
          {
            element: <MainLayout />,
            children: [
              // 设备模式页面
              {
                path: 'device/swipe-mode',
                lazy: async () => {
                  const { default: SwipeModeWrapper } = await import(
                    './pages/device/SwipeModeWrapper'
                  )
                  return { Component: SwipeModeWrapper }
                }
              },
              {
                path: 'device/audio-mode',
                lazy: async () => {
                  const { default: AudioModePage } = await import('./pages/device/AudioModePage')
                  return { Component: AudioModePage }
                }
              },
              {
                path: 'device/video-mode',
                lazy: async () => {
                  const { default: VideoModePage } = await import('./pages/device/VideoModePage')
                  return { Component: VideoModePage }
                }
              },
              // 远程控制端页面路由
              {
                path: 'device/remote-controller',
                lazy: async () => {
                  const { default: RemoteControllerPage } = await import(
                    './pages/device/remote/RemoteControllerPage'
                  )
                  return { Component: RemoteControllerPage }
                }
              },
              {
                path: 'device/remote-controller/control',
                lazy: async () => {
                  const { default: RemoteControllerControlPage } = await import(
                    './pages/device/remote/RemoteControllerControlPage'
                  )
                  return { Component: RemoteControllerControlPage }
                }
              },
              // 远程主机页面路由
              {
                path: 'device/remote-host',
                lazy: async () => {
                  const { default: RemoteHostPage } = await import(
                    './pages/device/remote/RemoteHostPage'
                  )
                  return { Component: RemoteHostPage }
                }
              },
              {
                path: 'device/remote-host/waiting',
                lazy: async () => {
                  const { default: RemoteHostWaitingPage } = await import(
                    './pages/device/remote/RemoteHostWaitingPage'
                  )
                  return { Component: RemoteHostWaitingPage }
                }
              },
              {
                path: 'device/remote-host/connected',
                lazy: async () => {
                  const { default: RemoteHostConnectedPage } = await import(
                    './pages/device/remote/RemoteHostConnectedPage'
                  )
                  return { Component: RemoteHostConnectedPage }
                }
              },
              {
                path: 'device/waveform-library',
                lazy: async () => {
                  const { default: WaveformLibraryPage } = await import(
                    './pages/device/WaveformLibraryPage'
                  )
                  return { Component: WaveformLibraryPage }
                }
              },
              {
                path: 'device/my-waveforms',
                lazy: async () => {
                  const { default: MyWaveformsPage } = await import(
                    './pages/device/MyWaveformsPage'
                  )
                  return { Component: MyWaveformsPage }
                }
              },
              // 自定义角色页面
              {
                path: 'roles/custom',
                lazy: async () => {
                  const { default: CustomRolePage } = await import('./pages/roles/CustomRolePage')
                  return { Component: CustomRolePage }
                }
              },
              // 角色详情页面
              {
                path: 'role-detail/:roleId',
                lazy: async () => {
                  const { default: RoleDetailPage } = await import('./pages/roles/RoleDetailPage')
                  return { Component: RoleDetailPage }
                }
              },
              // 角色资料页面（从聊天页面头像点击进入，无导航栏）
              {
                path: 'role-profile/:roleId',
                lazy: async () => {
                  const { default: RoleProfilePage } = await import('./pages/roles/RoleProfilePage')
                  return { Component: RoleProfilePage }
                }
              },
              // 写真集页面
              {
                path: 'photo-album/:roleId',
                lazy: async () => {
                  const { default: PhotoAlbumPage } = await import(
                    './pages/photo-album/PhotoAlbumPage'
                  )
                  return { Component: PhotoAlbumPage }
                }
              }
            ]
          },
          {
            element: <RoleLayout />,
            children: [
              // 新建聊天页面
              {
                path: 'chat',
                lazy: async () => {
                  const { default: ChatPage } = await import('./pages/chat/ChatV2Page')
                  return { Component: ChatPage }
                }
              },
              // 已有聊天记录ID的聊天页面
              {
                path: 'chat/:chatId',
                lazy: async () => {
                  const { default: ChatPage } = await import('./pages/chat/ChatV2Page')
                  return { Component: ChatPage }
                }
              },
              // 激情互动页面
              // {
              //   path: 'interactive',
              //   lazy: async () => {
              //     const { default: InteractivePage } = await import(
              //       './pages/interactive/InteractivePage'
              //     )
              //     return { Component: InteractivePage }
              //   }
              // }
              // 聊天历史页面
              {
                path: 'chat-history',
                lazy: async () => {
                  const { default: ChatHistoryPage } = await import('./pages/chat/ChatHistoryPage')
                  return { Component: ChatHistoryPage }
                }
              }
            ]
          },
          // 激情互动播放器页面
          {
            path: 'interactive/player',
            lazy: async () => {
              const { default: InteractivePlayerPage } = await import(
                './pages/interactive/InteractivePlayerPage'
              )
              return { Component: InteractivePlayerPage }
            }
          },
          // 个人资料编辑页面
          {
            path: 'profile/edit',
            lazy: async () => {
              const { default: ProfilePage } = await import('./pages/profile/ProfilePage')
              return { Component: ProfilePage }
            }
          },
          // 激活码页面
          {
            path: 'profile/activation-code',
            lazy: async () => {
              const { default: ActivationCodePage } = await import(
                './pages/profile/ActivationCodePage'
              )
              return { Component: ActivationCodePage }
            }
          },
          // 激活历史页面
          {
            path: 'profile/activation-history',
            lazy: async () => {
              const { default: ActivationHistoryPage } = await import(
                './pages/profile/ActivationHistoryPage'
              )
              return { Component: ActivationHistoryPage }
            }
          },
          // 会员中心页面
          {
            path: 'membership',
            lazy: async () => {
              const { default: MembershipPage } = await import('./pages/membership/MembershipPage')
              return { Component: MembershipPage }
            }
          },
          // 支付页面
          {
            path: 'payment',
            lazy: async () => {
              const { default: PaymentPage } = await import('./pages/membership/PaymentPage')
              return { Component: PaymentPage }
            }
          },
          // 积分明细页面
          {
            path: 'points',
            lazy: async () => {
              const { default: PointsDetailPage } = await import('./pages/points/PointsDetailPage')
              return { Component: PointsDetailPage }
            }
          },
          // 积分商城页面
          {
            path: 'points-store',
            lazy: async () => {
              const { default: PointsStorePage } = await import('./pages/points/PointsStorePage')
              return { Component: PointsStorePage }
            }
          },
          // 邀请码管理页面
          {
            path: 'referral',
            lazy: async () => {
              const { default: ReferralPage } = await import('./pages/referral/ReferralPage')
              return { Component: ReferralPage }
            }
          },
          // 邀请列表页面
          {
            path: 'referral/invites',
            lazy: async () => {
              const { default: InvitesPage } = await import('./pages/referral/InvitesPage')
              return { Component: InvitesPage }
            }
          },
          // 佣金管理页面
          {
            path: 'referral/commission',
            lazy: async () => {
              const { default: CommissionPage } = await import('./pages/referral/CommissionPage')
              return { Component: CommissionPage }
            }
          }
        ]
      }
    ]
  },
  // 登录页面路由
  {
    path: '/login',
    lazy: async () => {
      const { default: LoginPage } = await import('./pages/login/LoginPage')
      return { Component: LoginPage }
    }
  },
  // 注册页面路由
  {
    path: '/register',
    lazy: async () => {
      const { default: RegisterPage } = await import('./pages/register/RegisterPage')
      return { Component: RegisterPage }
    }
  }
])

export default router
