{"name": "pleasurehub-backend-service", "version": "1.0.0", "description": "基于 Cloudflare Workers + Hono + Supabase 的现代化 API 服务", "main": "src/index.ts", "type": "module", "scripts": {"dev": "wrangler dev --env dev", "dev:global": "wrangler dev --env global", "build": "tsc", "start": "tsx src/index.ts", "lint": "biome lint --write", "format": "biome format --write", "test": "vitest", "test:watch": "vitest --watch", "db:generate": "drizzle-kit generate", "db:migrate": "APP_VERSION=global && tsx src/lib/db/migrate.ts", "db:migrate:zh": "APP_VERSION=zh && tsx src/lib/db/migrate.ts", "db:studio": "drizzle-kit studio", "db:push": "drizzle-kit push", "db:seed": "tsx src/lib/db/seed.ts", "db:seed-devices": "tsx src/lib/db/seed-devices.ts", "db:validate-scripts": "tsx src/lib/db/seed-devices.ts validate", "wrangler": "wrangler", "deploy": "pnpm build && wrangler deploy", "deploy:dev": "pnpm build && wrangler deploy --env dev", "deploy:global": "pnpm build && wrangler deploy --env global"}, "keywords": ["cloudflare", "workers", "hono", "supabase", "api"], "packageManager": "pnpm@10.5.2", "dependencies": {"@ai-sdk/xai": "^1.2.16", "@aws-sdk/client-s3": "^3.817.0", "@aws-sdk/s3-request-presigner": "^3.817.0", "@elevenlabs/elevenlabs-js": "^2.2.0", "@hono/node-server": "^1.14.3", "@hono/swagger-ui": "^0.5.1", "@hono/zod-openapi": "^0.19.8", "@hono/zod-validator": "^0.7.0", "@langchain/core": "^0.3.57", "@langchain/langgraph": "^0.3.0", "@langchain/openai": "^0.5.11", "@msgpack/msgpack": "^3.1.2", "@supabase/supabase-js": "^2.49.9", "ai": "^4.3.16", "alipay-sdk": "^4.14.0", "bcrypt-ts": "^5.0.2", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.34.1", "hono": "^4.7.11", "nanoid": "^5.1.5", "postgres": "^3.4.4", "resend": "^4.5.1", "zod": "^3.25.49"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@cloudflare/workers-types": "^4.20250601.0", "@types/dotenv": "^8.2.3", "@types/node": "^22.15.29", "drizzle-kit": "^0.25.0", "tsx": "^4.19.1", "typescript": "^5.8.3", "vitest": "^3.2.0", "wrangler": "^4.18.0"}}