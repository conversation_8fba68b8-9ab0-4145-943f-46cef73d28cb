/**
 * 手机号登录注册控制器
 */

import type { Context } from 'hono'
import type { Env } from '@/types/env'
import type { 
  SendPhoneCodeRequest,
  VerifyPhoneCodeRequest,
  SendPhoneLoginCodeRequest,
  LoginPhoneCodeRequest,
  LoginPhonePasswordRequest
} from '../types'
import {
  sendPhoneRegistrationCode,
  verifyPhoneRegistration,
  sendPhoneLoginCode,
  verifyPhoneLogin,
  loginWithPhonePassword
} from '../services/phone-auth'

/**
 * 发送注册验证码
 */
export async function handleSendPhoneCode(c: Context<{ Bindings: Env }>) {
  try {
    const data: SendPhoneCodeRequest = await c.req.json()
    const { phone } = data

    const result = await sendPhoneRegistrationCode(c.env, phone)

    return c.json(result, result.success ? 200 : 400)
  } catch (error) {
    console.error('发送手机验证码失败:', error)
    return c.json({
      success: false,
      message: '发送验证码时出现错误'
    }, 500)
  }
}

/**
 * 验证码注册
 */
export async function handleVerifyPhoneCode(c: Context<{ Bindings: Env }>) {
  try {
    const data: VerifyPhoneCodeRequest = await c.req.json()
    const { phone, code, name, password, inviteCode } = data

    const result = await verifyPhoneRegistration(c.env, {
      phone,
      code,
      name,
      password,
      inviteCode
    })

    return c.json(result, result.success ? 201 : 400)
  } catch (error) {
    console.error('手机验证码注册失败:', error)
    return c.json({
      success: false,
      message: '注册过程中出现错误'
    }, 500)
  }
}

/**
 * 发送登录验证码
 */
export async function handleSendPhoneLoginCode(c: Context<{ Bindings: Env }>) {
  try {
    const data: SendPhoneLoginCodeRequest = await c.req.json()
    const { phone } = data

    const result = await sendPhoneLoginCode(c.env, phone)

    return c.json(result, result.success ? 200 : 400)
  } catch (error) {
    console.error('发送登录验证码失败:', error)
    return c.json({
      success: false,
      message: '发送验证码时出现错误'
    }, 500)
  }
}

/**
 * 验证码登录
 */
export async function handleLoginPhoneCode(c: Context<{ Bindings: Env }>) {
  try {
    const data: LoginPhoneCodeRequest = await c.req.json()
    const { phone, code } = data

    const result = await verifyPhoneLogin(c.env, phone, code)

    return c.json(result, result.success ? 200 : 400)
  } catch (error) {
    console.error('手机验证码登录失败:', error)
    return c.json({
      success: false,
      message: '登录过程中出现错误'
    }, 500)
  }
} 

/**
 * 手机号密码登录
 */
export async function handleLoginPhonePassword(c: Context<{ Bindings: Env }>) {
  try {
    const data: LoginPhonePasswordRequest = await c.req.json()
    const { phone, password } = data

    const result = await loginWithPhonePassword(c.env, phone, password)

    return c.json(result, result.success ? 200 : 400)
  } catch (error) {
    console.error('手机号密码登录失败:', error)
    return c.json({
      success: false,
      message: '登录过程中出现错误'
    }, 500)
  }
} 