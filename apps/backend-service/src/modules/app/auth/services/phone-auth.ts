/**
 * 手机号登录注册业务逻辑服务
 */

import type { Env } from '@/types/env'
import type { 
  PhoneAuthServiceParams,
  PhoneCodeResponse,
  PhoneRegisterResponse,
  PhoneLoginResponse
} from '../types'
import { 
  sendRegistrationCodeSMS, 
  sendLoginCodeSMS, 
  generateSMSVerificationCode,
  storeVerificationCode,
  verifyStoredCode
} from '../email/sms'
import { 
  getUserByPhone, 
  createUserByPhone, 
  getUserBySupabaseId, 
  createUserProfile, 
  initializeNewUserPointsAsync 
} from './user-db'
import { createSupabaseServiceClient } from '@/lib/supabase'
import { 
  validateInviteCode,
  createReferralRelation,
  incrementInviteCodeUsage
} from './referral'

/**
 * 发送注册验证码
 */
export async function sendPhoneRegistrationCode(
  env: Env,
  phone: string
): Promise<PhoneCodeResponse> {
  try {
    // 1. 检查手机号是否已注册
    const existingUsers = await getUserByPhone(env, phone)
    if (existingUsers && existingUsers.length > 0) {
      return {
        success: false,
        message: '手机号已注册，请直接登录'
      }
    }

    // 2. 生成和发送验证码
    const verificationCode = generateSMSVerificationCode()
    const smsResult = await sendRegistrationCodeSMS(env, phone, verificationCode)

    if (!smsResult.success) {
      return {
        success: false,
        message: '验证码发送失败，请稍后重试'
      }
    }

    // 3. 存储验证码（5分钟有效期）
    await storeVerificationCode(env, phone, verificationCode, 5)

    return {
      success: true,
      message: '验证码已发送',
      data: {
        messageId: smsResult.messageId
      }
    }
  } catch (error) {
    console.error('发送注册验证码失败:', error)
    return {
      success: false,
      message: '发送验证码时出现错误'
    }
  }
}

/**
 * 验证码注册
 */
export async function verifyPhoneRegistration(
  env: Env,
  params: PhoneAuthServiceParams
): Promise<PhoneRegisterResponse> {
  const { phone, code, name, password, inviteCode } = params

  try {
    // 1. 验证验证码
    const codeVerification = await verifyStoredCode(env, phone, code!)
    if (!codeVerification.valid) {
      return {
        success: false,
        message: codeVerification.error || '验证码验证失败'
      }
    }

    // 2. 创建Supabase用户（仅使用手机号）
    const supabase = createSupabaseServiceClient(env)
    const userPassword = password || 'temp_' + Math.random().toString(36).substring(2, 15)
    
    let authData: any
    let isNewUser = true

    const { data: createData, error: createError } = await supabase.auth.admin.createUser({
      phone: phone,
      password: userPassword,
      phone_confirm: true,
      user_metadata: {
        name: name || `用户${phone.slice(-4)}`,
        hasPassword: !!password,
        loginMethod: 'phone'
      }
    })

    if (createError) {
      if (
        createError.message.includes('already been registered') ||
        createError.message.includes('already exists')
      ) {
        isNewUser = false
        // 处理已存在用户的情况
        const { data: users, error: listError } = await supabase.auth.admin.listUsers()
        if (listError) {
          return {
            success: false,
            message: '用户状态异常，请联系客服'
          }
        }

        const existingUser = users?.users?.find(u => u.phone === phone)
        if (!existingUser) {
          return {
            success: false,
            message: '用户状态异常，请联系客服'
          }
        }

        authData = { user: existingUser }
      } else {
        console.error('Supabase 创建用户失败:', createError)
        return {
          success: false,
          message: '注册失败，请重试'
        }
      }
    } else {
      authData = createData
    }

    if (!authData.user) {
      return {
        success: false,
        message: '注册失败'
      }
    }

    // 3. 创建数据库用户记录
    let dbUser = await getUserBySupabaseId(env, authData.user.id)

    if (!dbUser && isNewUser) {
      const [newDbUser] = await createUserByPhone(env, phone, authData.user.id)
      dbUser = newDbUser

      // 创建用户配置文件
      if (dbUser) {
        await createUserProfile(env, {
          userId: dbUser.id,
          nickname: name || `用户${phone.slice(-4)}`,
          gender: 'other'
        })

        // 异步初始化新用户积分
        initializeNewUserPointsAsync(env, dbUser.id)

        // 处理邀请码
        if (inviteCode && inviteCode.trim()) {
          try {
            const validationResult = await validateInviteCode(env, inviteCode.trim())

            if (validationResult.valid && validationResult.inviteCode) {
              await createReferralRelation(
                env,
                validationResult.inviteCode.userId,
                dbUser.id,
                validationResult.inviteCode.id
              )

              await incrementInviteCodeUsage(env, validationResult.inviteCode.id)

              console.log(
                `用户 ${dbUser.id} 通过邀请码 ${inviteCode} 注册成功，邀请人: ${validationResult.inviteCode.userId}`
              )
            }
          } catch (error) {
            console.error('处理邀请码时出错:', error)
          }
        }
      }
    }

    // 4. 创建会话
    if (password) {
      // 用户设置了密码，使用手机号密码登录
      const { data: passwordSignIn, error: passwordError } = await supabase.auth.signInWithPassword({
        phone: phone,
        password: userPassword
      })

      if (!passwordError && passwordSignIn.session) {
        return {
          success: true,
          message: isNewUser ? '注册成功' : '验证成功',
          data: {
            session: {
              access_token: passwordSignIn.session.access_token,
              refresh_token: passwordSignIn.session.refresh_token,
              expires_at: passwordSignIn.session.expires_at || 0,
              user: {
                id: authData.user.id,
                phone: phone,
                phoneConfirmed: true,
                dbUserId: dbUser?.id || ''
              }
            }
          }
        }
      }
    }

    // 如果没有密码或密码登录失败
    return {
      success: true,
      message: isNewUser ? '注册成功' : '验证成功',
      data: {
        requireLogin: !password,
        hasPassword: !!password
      }
    }

  } catch (error) {
    console.error('验证码注册失败:', error)
    return {
      success: false,
      message: '注册过程中出现错误'
    }
  }
}

/**
 * 发送登录验证码
 */
export async function sendPhoneLoginCode(
  env: Env,
  phone: string
): Promise<PhoneCodeResponse> {
  try {
    // 1. 检查用户是否存在
    const existingUsers = await getUserByPhone(env, phone)
    if (!existingUsers || existingUsers.length === 0) {
      return {
        success: false,
        message: '用户不存在，请先注册'
      }
    }

    // 2. 生成和发送验证码
    const verificationCode = generateSMSVerificationCode()
    const smsResult = await sendLoginCodeSMS(env, phone, verificationCode)

    if (!smsResult.success) {
      return {
        success: false,
        message: '验证码发送失败，请稍后重试'
      }
    }

    // 3. 存储验证码（5分钟有效期）
    await storeVerificationCode(env, phone, verificationCode, 5)

    return {
      success: true,
      message: '登录验证码已发送',
      data: {
        messageId: smsResult.messageId
      }
    }
  } catch (error) {
    console.error('发送登录验证码失败:', error)
    return {
      success: false,
      message: '发送验证码时出现错误'
    }
  }
}

/**
 * 验证码登录
 */
export async function verifyPhoneLogin(
  env: Env,
  phone: string,
  code: string
): Promise<PhoneLoginResponse> {
  try {
    // 1. 验证验证码
    const codeVerification = await verifyStoredCode(env, phone, code)
    if (!codeVerification.valid) {
      return {
        success: false,
        message: codeVerification.error || '验证码验证失败'
      }
    }

    // 2. 检查用户是否存在
    const existingUsers = await getUserByPhone(env, phone)
    if (!existingUsers || existingUsers.length === 0) {
      return {
        success: false,
        message: '用户不存在，请先注册'
      }
    }

    const dbUser = existingUsers[0]

    // 3. 获取Supabase用户信息
    const supabase = createSupabaseServiceClient(env)
    const { data: users, error: listError } = await supabase.auth.admin.listUsers()

    if (listError) {
      console.error('获取用户列表失败:', listError)
      return {
        success: false,
        message: '登录失败，请重试'
      }
    }

    const authUser = users?.users?.find(u => u.id === dbUser.supabaseUserId)
    if (!authUser) {
      return {
        success: false,
        message: '用户状态异常，请联系客服'
      }
    }

    try {
      // 4. 生成临时密码并登录
      const tempPassword = 'TempPass_' + Math.random().toString(36).substring(2, 15) + Date.now()

      const { error: updateError } = await supabase.auth.admin.updateUserById(authUser.id, {
        password: tempPassword
      })

      if (updateError) {
        console.error('更新临时密码失败:', updateError)
        throw updateError
      }

      const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
        phone: phone,
        password: tempPassword
      })

      if (loginError || !loginData.session) {
        console.error('临时密码登录失败:', loginError)
        throw loginError
      }

      return {
        success: true,
        message: '登录成功',
        data: {
          session: {
            access_token: loginData.session.access_token,
            refresh_token: loginData.session.refresh_token,
            expires_at: loginData.session.expires_at || 0,
            user: {
              id: authUser.id,
              phone: phone,
              phoneConfirmed: true,
              dbUserId: dbUser.id
            }
          }
        }
      }
    } catch (fallbackError) {
      console.error('验证码登录失败:', fallbackError)
      return {
        success: false,
        message: '登录失败，请重试'
      }
    }

  } catch (error) {
    console.error('验证码登录过程出错:', error)
    return {
      success: false,
      message: '登录过程中出现错误'
    }
  }
} 

/**
 * 手机号密码登录
 */
export async function loginWithPhonePassword(
  env: Env,
  phone: string,
  password: string
): Promise<PhoneLoginResponse> {
  try {
    // 1. 检查用户是否存在
    const existingUsers = await getUserByPhone(env, phone)
    if (!existingUsers || existingUsers.length === 0) {
      return {
        success: false,
        message: '用户不存在，请先注册'
      }
    }

    const dbUser = existingUsers[0]

    // 2. 获取Supabase用户信息
    const supabase = createSupabaseServiceClient(env)
    const { data: users, error: listError } = await supabase.auth.admin.listUsers()

    if (listError) {
      console.error('获取用户列表失败:', listError)
      return {
        success: false,
        message: '登录失败，请重试'
      }
    }

    const authUser = users?.users?.find(u => u.id === dbUser.supabaseUserId)
    if (!authUser) {
      return {
        success: false,
        message: '用户状态异常，请联系客服'
      }
    }

    // 3. 使用手机号和密码登录
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      phone: phone,
      password: password
    })

    if (loginError || !loginData.session) {
      console.error('密码登录失败:', loginError)
      return {
        success: false,
        message: '手机号或密码错误'
      }
    }

    return {
      success: true,
      message: '登录成功',
      data: {
        session: {
          access_token: loginData.session.access_token,
          refresh_token: loginData.session.refresh_token,
          expires_at: loginData.session.expires_at || 0,
          user: {
            id: authUser.id,
            phone: phone,
            phoneConfirmed: true,
            dbUserId: dbUser.id
          }
        }
      }
    }

  } catch (error) {
    console.error('手机号密码登录失败:', error)
    return {
      success: false,
      message: '登录过程中出现错误'
    }
  }
} 