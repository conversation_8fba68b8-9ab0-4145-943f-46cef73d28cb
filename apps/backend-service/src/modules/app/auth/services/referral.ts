/**
 * Auth模块邀请码服务
 */

import type { Env } from '@/types/env'
import { getSupabase } from '@/lib/db/queries/base'
import { handleSupabaseResult, handleSupabaseSingleResult, TABLE_NAMES } from '@/lib/db/supabase-types'

// 邀请码接口
interface InviteCode {
  id: string
  userId: string
  code: string
  isActive: boolean
  expiresAt?: string
  maxUses?: number
  usedCount: number
}

/**
 * 验证邀请码
 */
export async function validateInviteCode(
  env: Env,
  code: string
): Promise<{
  valid: boolean
  inviteCode?: InviteCode & { inviterEmail: string }
  error?: string
}> {
  const supabase = getSupabase(env)

  const result = await supabase
    .from(TABLE_NAMES.inviteCode)
    .select(
      `
      *,
      user:user_id (
        email
      )
    `
    )
    .eq('code', code.toUpperCase())
    .single()

  const { data, error } = handleSupabaseSingleResult(result)
  if (error) {
    return { valid: false, error: '邀请码不存在' }
  }

  const inviteCodeData = data as any

  // 检查邀请码是否有效
  if (!inviteCodeData.is_active) {
    return { valid: false, error: '邀请码已失效' }
  }

  // 检查是否过期
  if (inviteCodeData.expires_at && new Date() > new Date(inviteCodeData.expires_at)) {
    return { valid: false, error: '邀请码已过期' }
  }

  // 检查使用次数限制
  if (inviteCodeData.max_uses && inviteCodeData.used_count >= inviteCodeData.max_uses) {
    return { valid: false, error: '邀请码使用次数已达上限' }
  }

  return {
    valid: true,
    inviteCode: {
      ...inviteCodeData,
      inviterEmail: inviteCodeData.user?.email || ''
    } as InviteCode & { inviterEmail: string }
  }
}

/**
 * 创建推荐关系
 */
export async function createReferralRelation(
  env: Env,
  inviterId: string,
  inviteeId: string,
  inviteCodeId: string
): Promise<void> {
  const supabase = getSupabase(env)

  const { error } = await supabase
    .from(TABLE_NAMES.referralRelation)
    .insert({
      inviter_id: inviterId,
      invitee_id: inviteeId,
      invite_code_id: inviteCodeId,
      status: 'active',
      created_at: new Date().toISOString()
    })

  if (error) {
    console.error('创建推荐关系失败:', error)
    throw error
  }
}

/**
 * 更新邀请码使用次数
 */
export async function incrementInviteCodeUsage(env: Env, inviteCodeId: string): Promise<void> {
  const supabase = getSupabase(env)

  // 先获取当前使用次数，然后增加1
  const { data: currentData, error: fetchError } = await supabase
    .from(TABLE_NAMES.inviteCode)
    .select('used_count')
    .eq('id', inviteCodeId)
    .single()

  if (fetchError) {
    console.error('获取邀请码当前使用次数失败:', fetchError)
    throw fetchError
  }

  const { error } = await supabase
    .from(TABLE_NAMES.inviteCode)
    .update({
      used_count: (currentData.used_count || 0) + 1,
      updated_at: new Date().toISOString()
    })
    .eq('id', inviteCodeId)

  if (error) {
    console.error('更新邀请码使用次数失败:', error)
    throw error
  }
} 