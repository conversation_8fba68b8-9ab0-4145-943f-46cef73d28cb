/**
 * Auth模块用户数据库操作服务
 */

import type { Env } from '@/types/env'
import { getSupabase } from '@/lib/db/queries/base'
import { handleSupabaseResult, handleSupabaseSingleResult, TABLE_NAMES } from '@/lib/db/supabase-types'
import type { User, UserProfile } from '@/lib/db/schema'

// ==================== 手机号用户操作 ====================

export async function getUserByPhone(env: Env, phone: string): Promise<User[]> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase.from(TABLE_NAMES.user).select('*').eq('phone', phone)

    const { data, error } = handleSupabaseResult(result)
    if (error) throw error
    return data || []
  } catch (error) {
    console.error('Failed to get user by phone from database', error)
    throw error
  }
}

export async function createUserByPhone(env: Env, phone: string, supabaseUserId: string): Promise<User[]> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.user)
      .insert({
        phone,
        supabase_user_id: supabaseUserId
      })
      .select()

    const { data, error } = handleSupabaseResult(result)
    if (error) throw error
    return data || []
  } catch (error) {
    console.error('Failed to create user by phone in database', error)
    throw error
  }
}

export async function getUserBySupabaseId(env: Env, supabaseUserId: string): Promise<User | null> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.user)
      .select('*')
      .eq('supabase_user_id', supabaseUserId)

    const { data, error } = handleSupabaseResult(result)
    if (error) throw error

    // 返回第一个匹配的用户，如果没有则返回 null
    return data && data.length > 0 ? data[0] : null
  } catch (error) {
    console.error('Failed to get user by supabase id from database', error)
    return null
  }
}

export async function createUserProfile(env: Env, profileData: {
  userId: string
  nickname: string
  gender: 'male' | 'female' | 'other'
}): Promise<UserProfile | null> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.userProfile)
      .insert({
        user_id: profileData.userId,
        nickname: profileData.nickname,
        gender: profileData.gender
      })
      .select()

    const { data, error } = handleSupabaseResult(result)
    if (error) throw error

    return data && data.length > 0 ? data[0] : null
  } catch (error) {
    console.error('Failed to create user profile', error)
    throw error
  }
}

// 异步初始化新用户积分（不阻塞主流程）
export function initializeNewUserPointsAsync(env: Env, userId: string): void {
  // 异步执行，不等待结果
  Promise.resolve()
    .then(async () => {
      try {
        const supabase = getSupabase(env)
        
        // 检查是否已经初始化过积分
        const existingPoints = await supabase
          .from(TABLE_NAMES.userPoints)
          .select('id')
          .eq('user_id', userId)
          .single()

        if (existingPoints.data) {
          console.log(`用户 ${userId} 积分已存在，跳过初始化`)
          return
        }

        // 初始化用户积分（默认赠送50积分）
        const initialPoints = 50
        const { error: pointsError } = await supabase
          .from(TABLE_NAMES.userPoints)
          .insert({
            user_id: userId,
            total_points: initialPoints,
            used_points: 0,
            available_points: initialPoints
          })

        if (pointsError) {
          console.error(`新用户 ${userId} 积分初始化失败:`, pointsError)
          return
        }

        // 记录积分交易
        const { error: transactionError } = await supabase
          .from(TABLE_NAMES.pointsTransaction)
          .insert({
            user_id: userId,
            amount: initialPoints,
            type: 'credit',
            description: '新用户注册赠送',
            reference_type: 'registration',
            reference_id: userId
          })

        if (transactionError) {
          console.error(`新用户 ${userId} 积分交易记录失败:`, transactionError)
        } else {
          console.log(`新用户 ${userId} 积分初始化成功: ${initialPoints} 积分`)
        }

      } catch (error) {
        console.error(`新用户 ${userId} 积分初始化过程中出错:`, error)
      }
    })
    .catch(error => {
      console.error(`新用户 ${userId} 积分初始化Promise失败:`, error)
    })
} 