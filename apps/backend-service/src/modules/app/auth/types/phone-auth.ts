/**
 * 手机号登录注册类型定义
 */

// 发送手机验证码请求
export interface SendPhoneCodeRequest {
  phone: string
}

// 手机验证码注册请求
export interface VerifyPhoneCodeRequest {
  phone: string
  code: string
  name?: string
  password?: string
  inviteCode?: string
}

// 发送登录验证码请求
export interface SendPhoneLoginCodeRequest {
  phone: string
}

// 手机验证码登录请求
export interface LoginPhoneCodeRequest {
  phone: string
  code: string
}

// 手机号密码登录请求
export interface LoginPhonePasswordRequest {
  phone: string
  password: string
}

// 手机验证码响应
export interface PhoneCodeResponse {
  success: boolean
  message: string
  data?: {
    messageId?: string
  }
}

// 手机注册响应
export interface PhoneRegisterResponse {
  success: boolean
  message: string
  data?: {
    session?: {
      access_token: string
      refresh_token: string
      expires_at: number
      user: {
        id: string
        phone: string
        phoneConfirmed: boolean
        dbUserId: string
      }
    }
    requireLogin?: boolean
    hasPassword?: boolean
  }
}

// 手机登录响应
export interface PhoneLoginResponse {
  success: boolean
  message: string
  data?: {
    session: {
      access_token: string
      refresh_token: string
      expires_at: number
      user: {
        id: string
        phone: string
        phoneConfirmed: boolean
        dbUserId: string
      }
    }
  }
}

// 业务逻辑参数
export interface PhoneAuthServiceParams {
  phone: string
  code?: string
  name?: string
  password?: string
  inviteCode?: string
} 