/**
 * 火山引擎SMS服务类型定义
 */

// SMS配置接口
export interface SMSConfig {
  accessKeyId: string
  accessKeySecret: string
  region: string
  accountId: string          // SMS消息组ID
  signName: string           // 短信签名内容
  registerTemplateId: string   // 注册验证码模板ID
  loginTemplateId: string      // 登录验证码模板ID
}

// SMS发送结果
export interface SMSResult {
  success: boolean
  error?: string
  messageId?: string
}

// 火山引擎API查询参数（GET参数）
export interface VolcengineAPIQueryParams {
  Version: string
  Action: string
}

// 火山引擎API请求体参数（POST body）- 按照指定顺序排列
export interface VolcengineAPIRequestData {
  SmsAccount: string      // SMS账号（通常是签名名称）
  Sign: string           // 签名名称
  TemplateID: string     // 模板ID
  TemplateParam: string  // 模板参数JSON字符串
  Tag: string           // 标签
  PhoneNumbers: string   // 手机号
}

// 火山引擎API响应结构
export interface VolcengineAPIResponse {
  ResponseMetadata?: {
    RequestId: string
    Action: string
    Version: string
    Service: string
    Region: string
    Error?: {
      Code: string
      Message: string
    }
  }
  Result?: {
    MessageId: string
    [key: string]: any
  }
}

// SMS验证码类型
export type SMSVerificationType = 'register' | 'login'

// SMS模板参数
export interface SMSTemplateParams {
  code: string
} 