import type { Env } from '@/types/env'
// 在 Cloudflare Worker 环境中使用 Web Crypto API
import type { 
  SMSConfig, 
  SMSResult, 
  VolcengineAPIQueryParams,
  VolcengineAPIRequestData,
  VolcengineAPIResponse,
  SMSVerificationType,
  SMSTemplateParams
} from '../types/sms'

const PRODUCT_NAME = 'PleasureHub'

/**
 * 不参与加签过程的 header key (参考 sign.js)
 */
const HEADER_KEYS_TO_IGNORE = new Set([
  "authorization",
  "content-type",
  "content-length",
  "user-agent",
  "presigned-expires",
  "expect",
])

/**
 * 生成4位数字验证码
 */
export function generateSMSVerificationCode(): string {
  return Math.floor(1000 + Math.random() * 9000).toString()
}

/**
 * 获取当前时间戳 (参考 sign.js 的格式)
 */
function getDateTimeNow(): string {
  const now = new Date()
  return now.toISOString().replace(/[:-]|\.\d{3}/g, '')
}

/**
 * 发送注册验证码短信
 */
export async function sendRegistrationCodeSMS(
  env: Env,
  phone: string,
  code: string
): Promise<SMSResult> {
  try {
    const config: SMSConfig = {
      accessKeyId: env.VOLCENGINE_SMS_ACCESS_KEY,
      accessKeySecret: env.VOLCENGINE_SMS_SECRET_KEY,
      region: env.VOLCENGINE_SMS_REGION || 'cn-north-1',
      accountId: env.VOLCENGINE_SMS_ACCOUNT_ID,
      signName: env.VOLCENGINE_SMS_SIGN_NAME,
      registerTemplateId: env.VOLCENGINE_SMS_REGISTER_TEMPLATE_ID,
      loginTemplateId: env.VOLCENGINE_SMS_LOGIN_TEMPLATE_ID
    }

    const result = await sendSMSWithVolcengine(config, phone, code, 'register')
    
    return result
  } catch (error) {
    return { success: false, error: String(error) }
  }
}

/**
 * 发送登录验证码短信
 */
export async function sendLoginCodeSMS(
  env: Env,
  phone: string,
  code: string
): Promise<SMSResult> {
  try {
    const config: SMSConfig = {
      accessKeyId: env.VOLCENGINE_SMS_ACCESS_KEY,
      accessKeySecret: env.VOLCENGINE_SMS_SECRET_KEY,
      region: env.VOLCENGINE_SMS_REGION,
      accountId: env.VOLCENGINE_SMS_ACCOUNT_ID,
      signName: env.VOLCENGINE_SMS_SIGN_NAME,
      registerTemplateId: env.VOLCENGINE_SMS_REGISTER_TEMPLATE_ID,
      loginTemplateId: env.VOLCENGINE_SMS_LOGIN_TEMPLATE_ID
    }

    const result = await sendSMSWithVolcengine(config, phone, code, 'login')
    
    return result
  } catch (error) {
    return { success: false, error: String(error) }
  }
}

/**
 * 调用火山引擎SMS API (参考 sign.js 的实现逻辑)
 */
async function sendSMSWithVolcengine(
  config: SMSConfig,
  phoneNumber: string,
  code: string,
  type: SMSVerificationType
): Promise<SMSResult> {
  try {
    // 根据类型选择对应的模板ID
    const templateId = type === 'register' ? config.registerTemplateId : config.loginTemplateId
    
    // 构建请求体数据（按照指定的顺序）
    const requestData: VolcengineAPIRequestData = {
      SmsAccount: config.accountId,
      Sign: config.signName,
      TemplateID: templateId,
      TemplateParam: JSON.stringify({
        code: code
      }),
      Tag: type === 'register' ? '1001' : '1002',
      PhoneNumbers: phoneNumber
    }

    // 打印请求体日志
    console.log(JSON.stringify(requestData))

    // 构建签名参数 (参考 sign.js)
    const signParams = {
      headers: {
        ["X-Date"]: getDateTimeNow(),
      },
      method: 'POST',
      query: {
        Version: '2020-01-01',
        Action: 'SendSms',
      },
      accessKeyId: config.accessKeyId,
      secretAccessKey: config.accessKeySecret,
      serviceName: 'volcSMS',
      region: config.region,
      pathName: '/',
      bodySha: await hashSHA256(JSON.stringify(requestData))
    }

    // 计算授权签名 (使用 sign.js 的逻辑)
    const authorization = await signRequest(signParams)

    // 构建查询字符串
    const queryString = queryParamsToString(signParams.query)
    
    // 发送HTTP请求
    const response = await fetch(`https://sms.volcengineapi.com/?${queryString}`, {
      headers: {
        ...signParams.headers,
        'Content-Type': 'application/json; charset=utf-8',
        'Authorization': authorization,
      },
      method: signParams.method,
      body: JSON.stringify(requestData)
    })

    const result = await response.json() as VolcengineAPIResponse

    // 打印响应体日志
    console.log(JSON.stringify(result))

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}, Details: ${JSON.stringify(result)}`)
    }

    // 检查响应错误
    if (result.ResponseMetadata?.Error) {
      return {
        success: false,
        error: result.ResponseMetadata.Error.Message || '短信发送失败'
      }
    }

    // 返回成功结果
    return {
      success: true,
      messageId: result.Result?.MessageId
    }
  } catch (error) {
    return {
      success: false,
      error: String(error)
    }
  }
}

/**
 * 签名函数 (参考 sign.js，但使用 Web Crypto API)
 */
async function signRequest(params: any): Promise<string> {
  const {
    headers = {},
    query = {},
    region = '',
    serviceName = '',
    method = '',
    pathName = '/',
    accessKeyId = '',
    secretAccessKey = '',
    needSignHeaderKeys = [],
    bodySha,
  } = params

  const datetime = headers["X-Date"]
  const date = datetime.substring(0, 8) // YYYYMMDD
  
  // 创建正规化请求 (参考 sign.js)
  const [signedHeaders, canonicalHeaders] = await getSignHeaders(headers, needSignHeaderKeys)
  const canonicalRequest = [
    method.toUpperCase(),
    pathName,
    queryParamsToString(query) || '',
    `${canonicalHeaders}\n`,
    signedHeaders,
    bodySha || await hashSHA256(''),
  ].join('\n')

  const credentialScope = [date, region, serviceName, "request"].join('/')
  
  // 创建签名字符串 (参考 sign.js)
  const stringToSign = ["HMAC-SHA256", datetime, credentialScope, await hashSHA256(canonicalRequest)].join('\n')
  
  // 计算签名 (使用 Web Crypto API)
  const kDate = await hmacSHA256(secretAccessKey, date)
  const kRegion = await hmacSHA256(kDate, region)
  const kService = await hmacSHA256(kRegion, serviceName)
  const kSigning = await hmacSHA256(kService, "request")
  const signature = await hmacSHA256(kSigning, stringToSign, 'hex')

  return [
    "HMAC-SHA256",
    `Credential=${accessKeyId}/${credentialScope},`,
    `SignedHeaders=${signedHeaders},`,
    `Signature=${signature}`,
  ].join(' ')
}

/**
 * Web Crypto API HMAC-SHA256 实现
 */
async function hmacSHA256(secret: string | ArrayBuffer, message: string, encoding?: 'hex'): Promise<string | ArrayBuffer> {
  const secretKey = typeof secret === 'string' ? new TextEncoder().encode(secret) : secret
  
  const key = await crypto.subtle.importKey(
    'raw',
    secretKey,
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  )
  
  const signature = await crypto.subtle.sign('HMAC', key, new TextEncoder().encode(message))
  
  if (encoding === 'hex') {
    return Array.from(new Uint8Array(signature))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('')
  }
  
  return signature
}

/**
 * Web Crypto API SHA256 哈希实现
 */
async function hashSHA256(message: string): Promise<string> {
  const data = new TextEncoder().encode(message)
  const hashBuffer = await crypto.subtle.digest('SHA-256', data)
  return Array.from(new Uint8Array(hashBuffer))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('')
}

/**
 * 查询参数转换为字符串 (参考 sign.js)
 */
function queryParamsToString(params: Record<string, any>): string {
  return Object.keys(params)
    .sort()
    .map((key) => {
      const val = params[key]
      if (typeof val === 'undefined' || val === null) {
        return undefined
      }
      const escapedKey = uriEscape(key)
      if (!escapedKey) {
        return undefined
      }
      if (Array.isArray(val)) {
        return `${escapedKey}=${val.map(uriEscape).sort().join(`&${escapedKey}=`)}`
      }
      return `${escapedKey}=${uriEscape(val)}`
    })
    .filter((v) => v)
    .join('&')
}

/**
 * 获取签名头部 (参考 sign.js)
 */
async function getSignHeaders(originHeaders: Record<string, any>, needSignHeaders: string[]): Promise<[string, string]> {
  function trimHeaderValue(header: any): string {
    return header?.toString?.().trim().replace(/\s+/g, ' ') ?? ''
  }

  let h = Object.keys(originHeaders)
  
  // 根据 needSignHeaders 过滤
  if (Array.isArray(needSignHeaders)) {
    const needSignSet = new Set([...needSignHeaders, 'x-date', 'host'].map((k) => k.toLowerCase()))
    h = h.filter((k) => needSignSet.has(k.toLowerCase()))
  }
  
  // 根据 ignore headers 过滤 (参考 sign.js)
  h = h.filter((k) => !HEADER_KEYS_TO_IGNORE.has(k.toLowerCase()))
  
  const signedHeaderKeys = h
    .slice()
    .map((k) => k.toLowerCase())
    .sort()
    .join(';')
    
  const canonicalHeaders = h
    .sort((a, b) => (a.toLowerCase() < b.toLowerCase() ? -1 : 1))
    .map((k) => `${k.toLowerCase()}:${trimHeaderValue(originHeaders[k])}`)
    .join('\n')
    
  return [signedHeaderKeys, canonicalHeaders]
}

/**
 * URI 转义 (参考 sign.js)
 */
function uriEscape(str: any): string {
  try {
    return encodeURIComponent(str)
      .replace(/[^A-Za-z0-9_.~\-%]+/g, escape)
      .replace(/[*]/g, (ch) => `%${ch.charCodeAt(0).toString(16).toUpperCase()}`)
  } catch (e) {
    return ''
  }
}

// 复用现有的验证码存储和验证逻辑
export { storeVerificationCode, verifyStoredCode } from '@/lib/email' 