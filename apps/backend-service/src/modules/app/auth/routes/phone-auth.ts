/**
 * 手机号登录注册路由
 */

import { Hono } from 'hono'
import type { Env } from '@/types/env'
import {
  handleSendPhoneCode,
  handleVerifyPhoneCode,
  handleSendPhoneLoginCode,
  handleLoginPhoneCode,
  handleLoginPhonePassword
} from '../controller/phone-auth'

const phoneAuthRoutes = new Hono<{ Bindings: Env }>()

// 发送注册验证码
phoneAuthRoutes.post('/send-phone-code', handleSendPhoneCode)

// 验证码注册
phoneAuthRoutes.post('/verify-phone-code', handleVerifyPhoneCode)

// 手机号密码登录
phoneAuthRoutes.post('/login-phone-password', handleLoginPhonePassword)

// 发送登录验证码
phoneAuthRoutes.post('/send-phone-login-code', handleSendPhoneLoginCode)

// 验证码登录
phoneAuthRoutes.post('/login-phone-code', handleLoginPhoneCode)

export default phoneAuthRoutes 