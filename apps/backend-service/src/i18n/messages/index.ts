import { commonMessages } from './common'
import { userMessages } from './user'
import { membershipMessages } from './membership'
import { pointsMessages } from './points'
import { validationMessages } from './validation'
import { authMessages } from './auth'
import { chatMessages } from './chat'
import { backgroundMessages } from './background'
import { scriptMessages } from './script'
import { referralMessages } from './referral'
import { deviceMessages } from './devices'
import { characterMediaMessages } from './character-media'
import { photoAlbumMessages } from './photo-album'
import { videoGenerationMessages } from './video-generation'
import { historyMessages } from './history'
import { templateMessages } from './templates'
import { tts3Messages } from './tts3'
import { imageGenerationMessages } from './image-generation'
import { charactersMessages } from './characters'
import { uploadMessages } from './upload'
import { speechToTextMessages } from './speech-to-text'
import { paymentMessages } from './payment'
import { waveformMessages } from './waveform'
import {
  SupportedLanguage,
  isLanguageImplemented,
  getFallbackLanguage,
  getImplementedLanguages,
  DEFAULT_LANGUAGE
} from '../config'

// 消息模块接口
export interface MessageModule {
  [key: string]: Record<string, string>
}

// 所有消息模块
const messageModules: MessageModule[] = [
  commonMessages,
  userMessages,
  membershipMessages,
  pointsMessages,
  validationMessages,
  authMessages,
  chatMessages,
  backgroundMessages,
  scriptMessages,
  referralMessages,
  deviceMessages,
  characterMediaMessages,
  photoAlbumMessages,
  videoGenerationMessages,
  historyMessages,
  templateMessages,
  tts3Messages,
  imageGenerationMessages,
  charactersMessages,
  uploadMessages,
  speechToTextMessages,
  paymentMessages,
  waveformMessages
]

// 合并所有消息
export function getAllMessages(language: SupportedLanguage): Record<string, string> {
  // 如果语言未实现，使用回退语言
  const targetLanguage = isLanguageImplemented(language) ? language : getFallbackLanguage(language)

  // 使用 reduce 方法逐个合并消息对象
  return messageModules.reduce((result, module) => {
    const languageMessages = module[targetLanguage] || module[DEFAULT_LANGUAGE] || {}
    return Object.assign(result, languageMessages)
  }, {} as Record<string, string>)
}

// 获取特定模块的消息
export function getModuleMessages(
  moduleName: string,
  language: SupportedLanguage
): Record<string, string> {
  const targetLanguage = isLanguageImplemented(language) ? language : getFallbackLanguage(language)

  switch (moduleName) {
    case 'common':
      return (commonMessages as any)[targetLanguage] || {}
    case 'user':
      return (userMessages as any)[targetLanguage] || {}
    case 'membership':
      return (membershipMessages as any)[targetLanguage] || {}
    case 'points':
      return (pointsMessages as any)[targetLanguage] || {}
    case 'validation':
      return (validationMessages as any)[targetLanguage] || {}
    case 'auth':
      return (authMessages as any)[targetLanguage] || {}
    case 'chat':
      return (chatMessages as any)[targetLanguage] || {}
    case 'background':
      return (backgroundMessages as any)[targetLanguage] || {}
    case 'script':
      return (scriptMessages as any)[targetLanguage] || {}
    case 'referral':
      return (referralMessages as any)[targetLanguage] || {}
    case 'device':
      return (deviceMessages as any)[targetLanguage] || {}
    case 'character-media':
      return (characterMediaMessages as any)[targetLanguage] || {}
    case 'photo-album':
      return (photoAlbumMessages as any)[targetLanguage] || {}
    case 'video-generation':
      return (videoGenerationMessages as any)[targetLanguage] || {}
    case 'history':
      return (historyMessages as any)[targetLanguage] || {}
    case 'templates':
      return (templateMessages as any)[targetLanguage] || {}
    case 'tts3':
      return (tts3Messages as any)[targetLanguage] || {}
    case 'image-generation':
      return (imageGenerationMessages as any)[targetLanguage] || {}
    case 'characters':
      return (charactersMessages as any)[targetLanguage] || {}
    case 'upload':
      return (uploadMessages as any)[targetLanguage] || {}
    case 'speech-to-text':
      return (speechToTextMessages as any)[targetLanguage] || {}
    case 'payment':
      return (paymentMessages as any)[targetLanguage] || {}
    default:
      return {}
  }
}

// 获取可用的语言列表
export function getAvailableLanguages(): string[] {
  return getImplementedLanguages()
}

// 检查消息键是否存在
export function hasMessage(key: string, language: SupportedLanguage): boolean {
  const messages = getAllMessages(language)
  return key in messages
}

// 获取消息（带回退机制）
export function getMessage(
  key: string,
  language: SupportedLanguage,
  params?: Record<string, string>
): string {
  const messages = getAllMessages(language)
  let message = messages[key]

  // 如果消息不存在，尝试使用回退语言
  if (!message) {
    const fallbackLanguage = getFallbackLanguage(language) as SupportedLanguage
    if (fallbackLanguage !== language) {
      const fallbackMessages = getAllMessages(fallbackLanguage)
      message = fallbackMessages[key]
    }
  }

  // 如果仍然没有消息，返回键名
  if (!message) {
    return key
  }

  // 替换参数
  if (params) {
    Object.entries(params).forEach(([param, value]) => {
      message = message.replace(new RegExp(`\\{${param}\\}`, 'g'), value)
    })
  }

  return message
}

// 导出所有消息模块
export {
  commonMessages,
  userMessages,
  membershipMessages,
  pointsMessages,
  validationMessages,
  authMessages,
  chatMessages,
  backgroundMessages,
  scriptMessages,
  referralMessages,
  deviceMessages,
  characterMediaMessages,
  photoAlbumMessages,
  videoGenerationMessages,
  historyMessages,
  templateMessages,
  tts3Messages,
  imageGenerationMessages,
  charactersMessages,
  uploadMessages,
  speechToTextMessages,
  paymentMessages
}

// 重新导出语言配置
export type { SupportedLanguage } from '../config'
