// 认证相关消息
export const authMessages = {
  zh: {
    // 验证模式错误消息
    'auth.email_invalid': '邮箱格式不正确',
    'auth.password_min': '密码至少需要6个字符',
    'auth.code_min': '验证码至少需要6位',
    'auth.refresh_token_required': '刷新令牌不能为空',

    // 发送验证码
    'auth.email_registered': '该邮箱已被注册',
    'auth.send_code_failed': '发送验证码失败，请稍后重试',
    'auth.code_sent': '验证码已发送到您的邮箱，请查收',
    'auth.send_code_error': '发送验证码过程中出现错误',

    // 验证码验证
    'auth.code_verification_failed': '验证码验证失败',
    'auth.email_already_registered': '该邮箱已被注册，请直接登录',
    'auth.user_not_exist': '用户不存在，请先注册',
    'auth.user_status_abnormal': '用户状态异常，请联系客服',
    'auth.registration_failed': '注册失败，请重试',
    'auth.registration_failed_general': '注册失败',
    'auth.registration_failed_try_again': '注册失败，请稍后重试',
    'auth.database_error_creating_user': '创建用户时发生数据库错误，请检查邮件配置或联系管理员',
    'auth.registration_failed_unknown_error': '注册失败，发生未知错误，请联系客服',
    'auth.email_already_registered_login_directly': '该邮箱已注册，请直接登录',
    'auth.user_not_exist_register_first': '用户不存在，请先注册',
    'auth.registration_success': '注册成功',
    'auth.verification_success': '验证成功',
    'auth.registration_success_but_init_failed': '注册成功但初始化用户信息失败',
    'auth.verification_error': '验证过程中出现错误',
    'auth.registration_success_check_email': '注册成功，请检查邮箱进行验证',
    'auth.registration_error': '注册过程中出现错误',

    // 登录
    'auth.login_failed': '登录失败，请重试',
    'auth.login_success': '登录成功',
    'auth.login_error': '登录过程中出现错误',
    'auth.login_code_failed': '登录失败，请稍后重试或使用密码登录',
    'auth.login_code_error': '登录过程中出现错误',

    // 登出
    'auth.logout_success': '登出成功',
    'auth.logout_error': '登出过程中出现错误',

    // 用户信息
    'auth.user_not_found': '未找到用户信息',
    'auth.get_user_info_failed': '获取用户信息失败',
    'auth.token_refresh_hint': 'Token 将在 1 小时后过期，建议前端在 50 分钟后自动刷新',

    // 刷新令牌
    'auth.refresh_token_failed': '刷新令牌失败或已过期',
    'auth.refresh_token_success': '令牌刷新成功',
    'auth.refresh_token_error': '刷新令牌过程中出现错误',
    'auth.token_refresh_detail': 'Token 有效期为 1 小时，建议在 50 分钟后自动刷新',

    // 管理员
    'auth.admin_login_failed': '邮箱或密码不正确',
    'auth.admin_no_permission': '没有管理员权限',
    'auth.admin_login_success': '管理员登录成功',
    'auth.admin_login_error': '登录过程中出现错误',
    'auth.admin_not_logged_in': '未登录',
    'auth.admin_get_info_failed': '获取管理员信息失败',
    'auth.admin_logout_success': '管理员登出成功',
    'auth.admin_logout_failed': '登出失败'
  },
  'zh-TW': {
    // 驗證模式錯誤訊息
    'auth.email_invalid': '電子郵件格式不正確',
    'auth.password_min': '密碼至少需要6個字元',
    'auth.code_min': '驗證碼至少需要6位數',
    'auth.refresh_token_required': '重新整理權杖不能為空',

    // 發送驗證碼
    'auth.email_registered': '該電子郵件已被註冊',
    'auth.send_code_failed': '發送驗證碼失敗，請稍後重試',
    'auth.code_sent': '驗證碼已發送到您的電子郵件，請查收',
    'auth.send_code_error': '發送驗證碼過程中出現錯誤',

    // 驗證碼驗證
    'auth.code_verification_failed': '驗證碼驗證失敗',
    'auth.email_already_registered': '該電子郵件已被註冊，請直接登入',
    'auth.user_not_exist': '使用者不存在，請先註冊',
    'auth.user_status_abnormal': '使用者狀態異常，請聯絡客服',
    'auth.registration_failed': '註冊失敗，請重試',
    'auth.registration_failed_general': '註冊失敗',
    'auth.registration_failed_try_again': '註冊失敗，請稍後重試',
    'auth.database_error_creating_user': '創建使用者時發生資料庫錯誤，請檢查郵件設定或聯絡管理員',
    'auth.registration_failed_unknown_error': '註冊失敗，發生未知錯誤，請聯絡客服',
    'auth.email_already_registered_login_directly': '該電子郵件已註冊，請直接登入',
    'auth.user_not_exist_register_first': '使用者不存在，請先註冊',
    'auth.registration_success': '註冊成功',
    'auth.verification_success': '驗證成功',
    'auth.registration_success_but_init_failed': '註冊成功但初始化使用者資訊失敗',
    'auth.verification_error': '驗證過程中出現錯誤',
    'auth.registration_success_check_email': '註冊成功，請檢查電子郵件進行驗證',
    'auth.registration_error': '註冊過程中出現錯誤',

    // 登入
    'auth.login_failed': '登入失敗，請重試',
    'auth.login_success': '登入成功',
    'auth.login_error': '登入過程中出現錯誤',
    'auth.login_code_failed': '登入失敗，請稍後重試或使用密碼登入',
    'auth.login_code_error': '登入過程中出現錯誤',

    // 登出
    'auth.logout_success': '登出成功',
    'auth.logout_error': '登出過程中出現錯誤',

    // 使用者資訊
    'auth.user_not_found': '未找到使用者資訊',
    'auth.get_user_info_failed': '取得使用者資訊失敗',
    'auth.token_refresh_hint': '您的工作階段將在 1 小時後過期，建議在 50 分鐘後重新整理',

    // 重新整理權杖
    'auth.refresh_token_failed': '重新整理權杖失敗或已過期',
    'auth.refresh_token_success': '權杖重新整理成功',
    'auth.refresh_token_error': '重新整理權杖過程中出現錯誤',
    'auth.token_refresh_detail': '您的工作階段有效期為 1 小時，建議在 50 分鐘後自動重新整理',

    // 管理員
    'auth.admin_login_failed': '電子郵件或密碼不正確',
    'auth.admin_no_permission': '沒有管理員權限',
    'auth.admin_login_success': '管理員登入成功',
    'auth.admin_login_error': '登入過程中出現錯誤',
    'auth.admin_not_logged_in': '未登入',
    'auth.admin_get_info_failed': '取得管理員資訊失敗',
    'auth.admin_logout_success': '管理員登出成功',
    'auth.admin_logout_failed': '登出失敗'
  },
  es: {
    // Mensajes de error de validación
    'auth.email_invalid': 'Formato de correo electrónico inválido',
    'auth.password_min': 'La contraseña debe tener al menos 6 caracteres',
    'auth.code_min': 'El código de verificación debe tener al menos 6 dígitos',
    'auth.refresh_token_required': 'El token de actualización no puede estar vacío',

    // Envío de código de verificación
    'auth.email_registered': 'Este correo electrónico ya está registrado',
    'auth.send_code_failed':
      'Error al enviar el código de verificación, por favor intente más tarde',
    'auth.code_sent': 'El código de verificación ha sido enviado a su correo electrónico',
    'auth.send_code_error': 'Error al enviar el código de verificación',

    // Verificación de código
    'auth.code_verification_failed': 'Error en la verificación del código',
    'auth.email_already_registered':
      'Este correo electrónico ya está registrado, por favor inicie sesión',
    'auth.user_not_exist': 'El usuario no existe, por favor regístrese primero',
    'auth.user_status_abnormal': 'Estado de cuenta anormal, por favor contacte al soporte',
    'auth.registration_failed': 'Error en el registro, por favor intente de nuevo',
    'auth.registration_failed_general': 'Error en el registro',
    'auth.registration_failed_try_again': 'Error en el registro, por favor intente de nuevo',
    'auth.database_error_creating_user':
      'Error de base de datos al crear el usuario, por favor verifique la configuración de correo o póngase en contacto con el administrador',
    'auth.registration_failed_unknown_error':
      'Error en el registro, se produjo un error desconocido, póngase en contacto con el soporte',
    'auth.email_already_registered_login_directly':
      'Este correo electrónico ya está registrado, por favor inicie sesión',
    'auth.user_not_exist_register_first': 'El usuario no existe, por favor regístrese primero',
    'auth.registration_success': 'Registro exitoso',
    'auth.verification_success': 'Verificación exitosa',
    'auth.registration_success_but_init_failed':
      'Registro exitoso pero error al configurar su cuenta',
    'auth.verification_error': 'Error en la verificación',
    'auth.registration_success_check_email':
      'Registro exitoso, por favor verifique su correo electrónico',
    'auth.registration_error': 'Error durante el registro',

    // Inicio de sesión
    'auth.login_failed': 'Error al iniciar sesión, por favor intente de nuevo',
    'auth.login_success': 'Inicio de sesión exitoso',
    'auth.login_error': 'Error durante el inicio de sesión',
    'auth.login_code_failed':
      'Error al iniciar sesión, por favor intente más tarde o use la contraseña',
    'auth.login_code_error': 'Error durante el inicio de sesión',

    // Cerrar sesión
    'auth.logout_success': 'Sesión cerrada exitosamente',
    'auth.logout_error': 'Error al cerrar sesión',

    // Información de usuario
    'auth.user_not_found': 'Información de usuario no encontrada',
    'auth.get_user_info_failed': 'Error al obtener información del usuario',
    'auth.token_refresh_hint':
      'Su sesión expirará en 1 hora. Recomendamos actualizar después de 50 minutos',

    // Actualización de token
    'auth.refresh_token_failed': 'Error al actualizar el token o ha expirado',
    'auth.refresh_token_success': 'Token actualizado exitosamente',
    'auth.refresh_token_error': 'Error al actualizar el token',
    'auth.token_refresh_detail':
      'Su sesión es válida por 1 hora. Recomendamos actualizar después de 50 minutos',

    // Administrador
    'auth.admin_login_failed': 'Correo electrónico o contraseña incorrectos',
    'auth.admin_no_permission': 'Sin permisos de administrador',
    'auth.admin_login_success': 'Inicio de sesión de administrador exitoso',
    'auth.admin_login_error': 'Error durante el inicio de sesión',
    'auth.admin_not_logged_in': 'No ha iniciado sesión',
    'auth.admin_get_info_failed': 'Error al obtener información del administrador',
    'auth.admin_logout_success': 'Sesión de administrador cerrada exitosamente',
    'auth.admin_logout_failed': 'Error al cerrar sesión'
  },
  ja: {
    // バリデーションエラーメッセージ
    'auth.email_invalid': 'メールアドレスの形式が無効です',
    'auth.password_min': 'パスワードは6文字以上である必要があります',
    'auth.code_min': '認証コードは6桁以上である必要があります',
    'auth.refresh_token_required': 'リフレッシュトークンは空にできません',

    // 認証コード送信
    'auth.email_registered': 'このメールアドレスは既に登録されています',
    'auth.send_code_failed': '認証コードの送信に失敗しました。後でもう一度お試しください',
    'auth.code_sent': '認証コードがメールアドレスに送信されました',
    'auth.send_code_error': '認証コードの送信中にエラーが発生しました',

    // コード認証
    'auth.code_verification_failed': 'コード認証に失敗しました',
    'auth.email_already_registered':
      'このメールアドレスは既に登録されています。ログインしてください',
    'auth.user_not_exist': 'ユーザーが存在しません。まず登録してください',
    'auth.user_status_abnormal': 'アカウントの状態に異常があります。サポートにお問い合わせください',
    'auth.registration_failed': '登録に失敗しました。もう一度お試しください',
    'auth.registration_failed_general': '登録に失敗しました',
    'auth.registration_failed_try_again': '登録に失敗しました。もう一度お試しください',
    'auth.database_error_creating_user':
      'ユーザー作成中にデータベースエラーが発生しました。メール設定を確認するか、管理者にお問い合わせください',
    'auth.registration_failed_unknown_error':
      '登録に失敗しました。発生した未知のエラーです。サポートにお問い合わせください',
    'auth.email_already_registered_login_directly':
      'このメールアドレスは既に登録されています。ログインしてください',
    'auth.user_not_exist_register_first': 'ユーザーが存在しません。まず登録してください',
    'auth.registration_success': '登録が完了しました',
    'auth.verification_success': '認証が完了しました',
    'auth.registration_success_but_init_failed':
      '登録は完了しましたが、アカウントの設定に失敗しました',
    'auth.verification_error': '認証中にエラーが発生しました',
    'auth.registration_success_check_email':
      '登録が完了しました。メールを確認して認証を行ってください',
    'auth.registration_error': '登録中にエラーが発生しました',

    // ログイン
    'auth.login_failed': 'ログインに失敗しました。もう一度お試しください',
    'auth.login_success': 'ログインが完了しました',
    'auth.login_error': 'ログイン中にエラーが発生しました',
    'auth.login_code_failed':
      'ログインに失敗しました。後でもう一度お試しいただくか、パスワードでログインしてください',
    'auth.login_code_error': 'ログイン中にエラーが発生しました',

    // ログアウト
    'auth.logout_success': 'ログアウトが完了しました',
    'auth.logout_error': 'ログアウト中にエラーが発生しました',

    // ユーザー情報
    'auth.user_not_found': 'ユーザー情報が見つかりません',
    'auth.get_user_info_failed': 'ユーザー情報の取得に失敗しました',
    'auth.token_refresh_hint':
      'セッションは1時間後に期限切れになります。50分後の更新をお勧めします',

    // トークンの更新
    'auth.refresh_token_failed': 'トークンの更新に失敗したか、期限切れです',
    'auth.refresh_token_success': 'トークンの更新が完了しました',
    'auth.refresh_token_error': 'トークンの更新中にエラーが発生しました',
    'auth.token_refresh_detail': 'セッションの有効期限は1時間です。50分後の自動更新をお勧めします',

    // 管理者
    'auth.admin_login_failed': 'メールアドレスまたはパスワードが正しくありません',
    'auth.admin_no_permission': '管理者権限がありません',
    'auth.admin_login_success': '管理者ログインが完了しました',
    'auth.admin_login_error': 'ログイン中にエラーが発生しました',
    'auth.admin_not_logged_in': 'ログインしていません',
    'auth.admin_get_info_failed': '管理者情報の取得に失敗しました',
    'auth.admin_logout_success': '管理者ログアウトが完了しました',
    'auth.admin_logout_failed': 'ログアウトに失敗しました'
  },
  en: {
    // Validation schema error messages
    'auth.email_invalid': 'Invalid email format',
    'auth.password_min': 'Password must be at least 6 characters',
    'auth.code_min': 'Verification code must be at least 6 digits',
    'auth.refresh_token_required': 'Refresh token cannot be empty',

    // Send verification code
    'auth.email_registered': 'This email has already been registered',
    'auth.send_code_failed': 'Failed to send verification code, please try again later',
    'auth.code_sent': 'Verification code has been sent to your email',
    'auth.send_code_error': 'Error occurred while sending verification code',

    // Verify code
    'auth.code_verification_failed': 'Code verification failed',
    'auth.email_already_registered': 'This email is already registered, please sign in instead',
    'auth.user_not_exist': 'User not found, please register first',
    'auth.user_status_abnormal': 'Account status error, please contact support',
    'auth.registration_failed': 'Registration failed, please try again',
    'auth.registration_failed_general': 'Registration failed',
    'auth.registration_failed_try_again': 'Registration failed, please try again later',
    'auth.database_error_creating_user':
      'Database error occurred while creating user, please check email configuration or contact administrator',
    'auth.registration_failed_unknown_error':
      'Registration failed, unknown error occurred, please contact support',
    'auth.email_already_registered_login_directly':
      'This email is already registered, please sign in directly',
    'auth.user_not_exist_register_first': 'User not found, please register first',
    'auth.registration_success': 'Registration successful',
    'auth.verification_success': 'Verification successful',
    'auth.registration_success_but_init_failed':
      'Registration successful but failed to set up your account',
    'auth.verification_error': 'Verification error occurred',
    'auth.registration_success_check_email':
      'Registration successful, please check your email to verify',
    'auth.registration_error': 'Registration error occurred',

    // Login
    'auth.login_failed': 'Login failed, please try again',
    'auth.login_success': 'Login successful',
    'auth.login_error': 'Error occurred during login',
    'auth.login_code_failed': 'Login failed, please try again later or use password login',
    'auth.login_code_error': 'Error occurred during login',

    // Logout
    'auth.logout_success': 'Logout successful',
    'auth.logout_error': 'Error occurred during logout',

    // User info
    'auth.user_not_found': 'User information not found',
    'auth.get_user_info_failed': 'Failed to get user information',
    'auth.token_refresh_hint':
      'Your session will expire in 1 hour. We recommend refreshing after 50 minutes',

    // Refresh token
    'auth.refresh_token_failed': 'Refresh token failed or expired',
    'auth.refresh_token_success': 'Token refresh successful',
    'auth.refresh_token_error': 'Error occurred during token refresh',
    'auth.token_refresh_detail':
      'Your session is valid for 1 hour. We recommend refreshing after 50 minutes',

    // Admin
    'auth.admin_login_failed': 'Incorrect email or password',
    'auth.admin_no_permission': 'No administrator permission',
    'auth.admin_login_success': 'Administrator login successful',
    'auth.admin_login_error': 'Error occurred during login',
    'auth.admin_not_logged_in': 'Not logged in',
    'auth.admin_get_info_failed': 'Failed to get administrator information',
    'auth.admin_logout_success': 'Administrator logout successful',
    'auth.admin_logout_failed': 'Logout failed'
  }
}
