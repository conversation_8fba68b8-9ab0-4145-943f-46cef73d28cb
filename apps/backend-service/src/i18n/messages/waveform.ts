export const waveformMessages = {
  zh: {
    // 成功消息
    created_successfully: '波形数据创建成功',
    liked_successfully: '点赞成功',
    unliked_successfully: '取消点赞成功',
    favorited_successfully: '收藏成功',
    unfavorited_successfully: '取消收藏成功',
    play_recorded_successfully: '播放记录成功',
    deleted_successfully: '波形删除成功',

    // 错误消息
    create_failed: '创建波形数据失败',
    not_found: '波形不存在',
    get_failed: '获取波形数据失败',
    get_my_list_failed: '获取我的波形列表失败',
    get_public_list_failed: '获取公共波形库失败',
    get_favorites_failed: '获取收藏波形失败',
    like_failed: '点赞操作失败',
    favorite_failed: '收藏操作失败',
    play_record_failed: '记录播放失败',
    delete_failed: '删除波形失败',
    access_denied: '无权访问此波形',
    already_exists: '波形名称已存在',

    // 验证消息
    name_required: '波形名称不能为空',
    name_too_long: '波形名称不能超过100个字符',
    description_too_long: '波形描述不能超过500个字符',
    invalid_recording_data: '无效的录制数据',
    invalid_duration: '无效的时长',
    invalid_intensity: '无效的强度值',
    invalid_tags: '无效的标签格式',

    // 列表和筛选
    sort_by_latest: '按最新排序',
    sort_by_popular: '按热门排序',
    sort_by_most_played: '按播放量排序',
    no_waveforms_found: '未找到波形数据',
    load_more: '加载更多',

    // 统计信息
    duration_label: '时长',
    intensity_label: '强度',
    complexity_label: '复杂度',
    like_count: '点赞数',
    favorite_count: '收藏数',
    play_count: '播放数',

    // 复杂度等级
    complexity_simple: '简单',
    complexity_medium: '中等',
    complexity_complex: '复杂',

    // 强度等级
    intensity_low: '低',
    intensity_medium: '中',
    intensity_high: '高',

    // 操作按钮
    create_waveform: '创建波形',
    edit_waveform: '编辑波形',
    delete_waveform: '删除波形',
    share_waveform: '分享波形',
    download_waveform: '下载波形',
    play_waveform: '播放波形',
    stop_waveform: '停止播放',

    // 标签相关
    add_tag: '添加标签',
    remove_tag: '移除标签',
    popular_tags: '热门标签',
    custom_tags: '自定义标签',

    // 权限相关
    public_waveform: '公开波形',
    private_waveform: '私有波形',
    make_public: '设为公开',
    make_private: '设为私有'
  },

  en: {
    // Success messages
    created_successfully: 'Waveform created successfully',
    liked_successfully: 'Liked successfully',
    unliked_successfully: 'Unliked successfully',
    favorited_successfully: 'Added to favorites',
    unfavorited_successfully: 'Removed from favorites',
    play_recorded_successfully: 'Play recorded successfully',
    deleted_successfully: 'Waveform deleted successfully',

    // Error messages
    create_failed: 'Failed to create waveform',
    not_found: 'Waveform not found',
    get_failed: 'Failed to get waveform data',
    get_my_list_failed: 'Failed to get my waveforms',
    get_public_list_failed: 'Failed to get public waveforms',
    get_favorites_failed: 'Failed to get favorite waveforms',
    like_failed: 'Failed to like waveform',
    favorite_failed: 'Failed to favorite waveform',
    play_record_failed: 'Failed to record play',
    delete_failed: 'Failed to delete waveform',
    access_denied: 'Access denied to this waveform',
    already_exists: 'Waveform name already exists',

    // Validation messages
    name_required: 'Waveform name is required',
    name_too_long: 'Waveform name cannot exceed 100 characters',
    description_too_long: 'Description cannot exceed 500 characters',
    invalid_recording_data: 'Invalid recording data',
    invalid_duration: 'Invalid duration',
    invalid_intensity: 'Invalid intensity value',
    invalid_tags: 'Invalid tags format',

    // List and filtering
    sort_by_latest: 'Sort by latest',
    sort_by_popular: 'Sort by popular',
    sort_by_most_played: 'Sort by most played',
    no_waveforms_found: 'No waveforms found',
    load_more: 'Load more',

    // Statistics
    duration_label: 'Duration',
    intensity_label: 'Intensity',
    complexity_label: 'Complexity',
    like_count: 'Likes',
    favorite_count: 'Favorites',
    play_count: 'Plays',

    // Complexity levels
    complexity_simple: 'Simple',
    complexity_medium: 'Medium',
    complexity_complex: 'Complex',

    // Intensity levels
    intensity_low: 'Low',
    intensity_medium: 'Medium',
    intensity_high: 'High',

    // Action buttons
    create_waveform: 'Create Waveform',
    edit_waveform: 'Edit Waveform',
    delete_waveform: 'Delete Waveform',
    share_waveform: 'Share Waveform',
    download_waveform: 'Download Waveform',
    play_waveform: 'Play Waveform',
    stop_waveform: 'Stop Playing',

    // Tags related
    add_tag: 'Add Tag',
    remove_tag: 'Remove Tag',
    popular_tags: 'Popular Tags',
    custom_tags: 'Custom Tags',

    // Permission related
    public_waveform: 'Public Waveform',
    private_waveform: 'Private Waveform',
    make_public: 'Make Public',
    make_private: 'Make Private'
  },

  ja: {
    // 成功メッセージ
    created_successfully: '波形データが正常に作成されました',
    liked_successfully: 'いいねしました',
    unliked_successfully: 'いいねを取り消しました',
    favorited_successfully: 'お気に入りに追加しました',
    unfavorited_successfully: 'お気に入りから削除しました',
    play_recorded_successfully: '再生を記録しました',
    deleted_successfully: '波形データを削除しました',

    // エラーメッセージ
    create_failed: '波形データの作成に失敗しました',
    not_found: '波形データが見つかりません',
    get_failed: '波形データの取得に失敗しました',
    get_my_list_failed: 'マイ波形リストの取得に失敗しました',
    get_public_list_failed: '公開波形ライブラリの取得に失敗しました',
    get_favorites_failed: 'お気に入り波形の取得に失敗しました',
    like_failed: 'いいね操作に失敗しました',
    favorite_failed: 'お気に入り操作に失敗しました',
    play_record_failed: '再生記録に失敗しました',
    delete_failed: '波形データの削除に失敗しました',
    access_denied: 'この波形データにアクセスする権限がありません',
    already_exists: '波形名が既に存在します',

    // バリデーションメッセージ
    name_required: '波形名は必須です',
    name_too_long: '波形名は100文字以内で入力してください',
    description_too_long: '説明は500文字以内で入力してください',
    invalid_recording_data: '無効な録画データです',
    invalid_duration: '無効な長さです',
    invalid_intensity: '無効な強度値です',
    invalid_tags: '無効なタグ形式です',

    // リストとフィルタリング
    sort_by_latest: '最新順',
    sort_by_popular: '人気順',
    sort_by_most_played: '再生回数順',
    no_waveforms_found: '波形データが見つかりません',
    load_more: 'もっと読み込む',

    // 統計情報
    duration_label: '長さ',
    intensity_label: '強度',
    complexity_label: '複雑さ',
    like_count: 'いいね数',
    favorite_count: 'お気に入り数',
    play_count: '再生回数',

    // 複雑さレベル
    complexity_simple: 'シンプル',
    complexity_medium: '中程度',
    complexity_complex: '複雑',

    // 強度レベル
    intensity_low: '低',
    intensity_medium: '中',
    intensity_high: '高',

    // アクションボタン
    create_waveform: '波形を作成',
    edit_waveform: '波形を編集',
    delete_waveform: '波形を削除',
    share_waveform: '波形を共有',
    download_waveform: '波形をダウンロード',
    play_waveform: '波形を再生',
    stop_waveform: '再生停止',

    // タグ関連
    add_tag: 'タグを追加',
    remove_tag: 'タグを削除',
    popular_tags: '人気タグ',
    custom_tags: 'カスタムタグ',

    // 権限関連
    public_waveform: '公開波形',
    private_waveform: '非公開波形',
    make_public: '公開にする',
    make_private: '非公開にする'
  }
}
