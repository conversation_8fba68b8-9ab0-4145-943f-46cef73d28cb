import type { Env } from '@/types/env'

// 根据环境获取数据库 URL
function getDatabaseUrl(env: Env) {
  if (env.WORKER_ENV === 'global') {
    return env.DATABASE_URL
  }
  return env.DATABASE_ZH_URL
}

// 根据环境获取 Supabase URL
function getSupabaseUrl(env: Env) {
  console.log('🔍 [CONFIG] 获取 Supabase URL:', {
    WORKER_ENV: env.WORKER_ENV,
    SUPABASE_URL: env.SUPABASE_URL,
    SUPABASE_ZH_URL: env.SUPABASE_ZH_URL
  })
  if (env.WORKER_ENV === 'global') {
    return env.SUPABASE_URL
  }
  return env.SUPABASE_ZH_URL
}

function getSupabaseAnonKey(env: Env) {
  if (env.WORKER_ENV === 'global') {
    return env.SUPABASE_ANON_KEY
  }
  return env.SUPABASE_ANON_KEY_ZH
}

function getSupabaseServiceRoleKey(env: Env) {
  if (env.WORKER_ENV === 'global') {
    return env.SUPABASE_SERVICE_ROLE_KEY
  }
  return env.SUPABASE_SERVICE_ROLE_KEY_ZH
}

export { getDatabaseUrl, getSupabaseUrl, getSupabaseAnonKey, getSupabaseServiceRoleKey }
