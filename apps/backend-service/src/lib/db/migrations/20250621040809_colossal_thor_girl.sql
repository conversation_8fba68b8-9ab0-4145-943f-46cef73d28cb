ALTER TABLE "UserPoints" ADD COLUMN IF NOT EXISTS "cycle_start_date" timestamp;--> statement-breakpoint
ALTER TABLE "UserPoints" ADD COLUMN IF NOT EXISTS "cycle_end_date" timestamp;--> statement-breakpoint
ALTER TABLE "UserPoints" ADD COLUMN IF NOT EXISTS "membership_level" varchar(20);--> statement-breakpoint
ALTER TABLE "UserPoints" ADD COLUMN IF NOT EXISTS "monthly_allocation" integer DEFAULT 0;--> statement-breakpoint
ALTER TABLE "UserPoints" ADD COLUMN IF NOT EXISTS "cycle_consumed" integer DEFAULT 0;--> statement-breakpoint
ALTER TABLE "UserPoints" ADD COLUMN IF NOT EXISTS "cycle_gifted" integer DEFAULT 0;--> statement-breakpoint
ALTER TABLE "UserPoints" ADD COLUMN IF NOT EXISTS "cycle_received" integer DEFAULT 0;--> statement-breakpoint
ALTER TABLE "UserPoints" ADD COLUMN IF NOT EXISTS "last_cycle_check" timestamp;--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_user_points_cycle_end" ON "UserPoints" USING btree ("cycle_end_date");