CREATE TABLE IF NOT EXISTS "VoiceModel" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"model_id" varchar(100) NOT NULL,
	"name" varchar(50) NOT NULL,
	"display_name" varchar(100) NOT NULL,
	"description" text,
	"gender" varchar NOT NULL,
	"language" varchar(10) DEFAULT 'zh-CN' NOT NULL,
	"supported_languages" json,
	"category" varchar(50),
	"tags" json,
	"is_active" boolean DEFAULT true NOT NULL,
	"is_premium" boolean DEFAULT false NOT NULL,
	"sort_order" integer DEFAULT 0,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "VoiceModel_model_id_unique" UNIQUE("model_id")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "VoiceSample" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"voice_model_id" uuid NOT NULL,
	"language" varchar(10) NOT NULL,
	"sample_text" text NOT NULL,
	"audio_url" text,
	"duration" integer,
	"file_size" integer,
	"is_default" boolean DEFAULT false NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "Character" ADD COLUMN IF NOT EXISTS "voice_model_id" uuid;--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "VoiceSample" ADD CONSTRAINT "VoiceSample_voice_model_id_VoiceModel_id_fk" FOREIGN KEY ("voice_model_id") REFERENCES "public"."VoiceModel"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_voice_model_id" ON "VoiceModel" USING btree ("model_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_voice_active" ON "VoiceModel" USING btree ("is_active") WHERE "VoiceModel"."is_active" = true;--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_voice_gender" ON "VoiceModel" USING btree ("gender");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_voice_sample_model" ON "VoiceSample" USING btree ("voice_model_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_voice_sample_language" ON "VoiceSample" USING btree ("language");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_voice_sample_default" ON "VoiceSample" USING btree ("is_default") WHERE "VoiceSample"."is_default" = true;--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "Character" ADD CONSTRAINT "Character_voice_model_id_VoiceModel_id_fk" FOREIGN KEY ("voice_model_id") REFERENCES "public"."VoiceModel"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
