CREATE TABLE IF NOT EXISTS "ActivationCode" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"code" varchar(32) NOT NULL,
	"type" varchar NOT NULL,
	"membership_plan_id" uuid,
	"points_package_id" uuid,
	"is_used" boolean DEFAULT false NOT NULL,
	"used_at" timestamp,
	"used_by" uuid,
	"expires_at" timestamp,
	"is_active" boolean DEFAULT true NOT NULL,
	"description" text,
	"batch_id" varchar(32),
	"created_by" uuid NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "ActivationCode_code_unique" UNIQUE("code")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "ActivationCodeUsage" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"activation_code_id" uuid NOT NULL,
	"user_id" uuid NOT NULL,
	"result_type" varchar NOT NULL,
	"result_id" uuid,
	"original_membership_id" uuid,
	"conflict_resolution" varchar,
	"metadata" json DEFAULT '{}',
	"ip_address" varchar(45),
	"user_agent" text,
	"used_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "ScriptPurchase" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"script_id" uuid NOT NULL,
	"points_cost" integer NOT NULL,
	"transaction_id" uuid,
	"status" varchar DEFAULT 'completed' NOT NULL,
	"expires_at" timestamp NOT NULL,
	"is_downloaded" boolean DEFAULT false NOT NULL,
	"downloaded_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "Script" ADD COLUMN IF NOT EXISTS "points_cost" integer DEFAULT 0 NOT NULL;--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ActivationCode" ADD CONSTRAINT "ActivationCode_membership_plan_id_MembershipPlan_id_fk" FOREIGN KEY ("membership_plan_id") REFERENCES "public"."MembershipPlan"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ActivationCode" ADD CONSTRAINT "ActivationCode_points_package_id_PointsPackage_id_fk" FOREIGN KEY ("points_package_id") REFERENCES "public"."PointsPackage"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ActivationCode" ADD CONSTRAINT "ActivationCode_used_by_User_id_fk" FOREIGN KEY ("used_by") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ActivationCode" ADD CONSTRAINT "ActivationCode_created_by_User_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ActivationCodeUsage" ADD CONSTRAINT "ActivationCodeUsage_activation_code_id_ActivationCode_id_fk" FOREIGN KEY ("activation_code_id") REFERENCES "public"."ActivationCode"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ActivationCodeUsage" ADD CONSTRAINT "ActivationCodeUsage_user_id_User_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ScriptPurchase" ADD CONSTRAINT "ScriptPurchase_user_id_User_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ScriptPurchase" ADD CONSTRAINT "ScriptPurchase_script_id_Script_id_fk" FOREIGN KEY ("script_id") REFERENCES "public"."Script"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ScriptPurchase" ADD CONSTRAINT "ScriptPurchase_transaction_id_PointsTransaction_id_fk" FOREIGN KEY ("transaction_id") REFERENCES "public"."PointsTransaction"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_activation_code_code" ON "ActivationCode" USING btree ("code");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_activation_code_type" ON "ActivationCode" USING btree ("type");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_activation_code_active" ON "ActivationCode" USING btree ("is_active") WHERE "ActivationCode"."is_active" = true;--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_activation_code_used" ON "ActivationCode" USING btree ("is_used");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_activation_code_expires_at" ON "ActivationCode" USING btree ("expires_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_activation_code_batch_id" ON "ActivationCode" USING btree ("batch_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_activation_code_created_by" ON "ActivationCode" USING btree ("created_by");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_activation_code_created_at" ON "ActivationCode" USING btree ("created_at" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_activation_code_used_by" ON "ActivationCode" USING btree ("used_by");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_activation_code_membership_plan" ON "ActivationCode" USING btree ("membership_plan_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_activation_code_points_package" ON "ActivationCode" USING btree ("points_package_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_activation_code_usage_code_id" ON "ActivationCodeUsage" USING btree ("activation_code_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_activation_code_usage_user_id" ON "ActivationCodeUsage" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_activation_code_usage_result_type" ON "ActivationCodeUsage" USING btree ("result_type");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_activation_code_usage_used_at" ON "ActivationCodeUsage" USING btree ("used_at" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_activation_code_usage_code_user" ON "ActivationCodeUsage" USING btree ("activation_code_id","user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_activation_code_usage_user_used_at" ON "ActivationCodeUsage" USING btree ("user_id","used_at" DESC NULLS LAST);--> statement-breakpoint
CREATE UNIQUE INDEX IF NOT EXISTS "idx_activation_code_usage_unique_user_code" ON "ActivationCodeUsage" USING btree ("user_id","activation_code_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_script_purchase_user_id" ON "ScriptPurchase" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_script_purchase_script_id" ON "ScriptPurchase" USING btree ("script_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_script_purchase_status" ON "ScriptPurchase" USING btree ("status");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_script_purchase_expires_at" ON "ScriptPurchase" USING btree ("expires_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_script_purchase_created_at" ON "ScriptPurchase" USING btree ("created_at" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_script_purchase_user_script" ON "ScriptPurchase" USING btree ("user_id","script_id");--> statement-breakpoint
CREATE UNIQUE INDEX IF NOT EXISTS "idx_script_purchase_unique_user_script" ON "ScriptPurchase" USING btree ("user_id","script_id");