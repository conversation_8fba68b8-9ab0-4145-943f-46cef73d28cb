ALTER TABLE "DeviceMode" ADD COLUMN IF NOT EXISTS "command_set_id" uuid NOT NULL;--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "DeviceMode" ADD CONSTRAINT "DeviceMode_command_set_id_DeviceCommandSet_id_fk" FOREIGN KEY ("command_set_id") REFERENCES "public"."DeviceCommandSet"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_mode_command_set" ON "DeviceMode" USING btree ("command_set_id");--> statement-breakpoint
ALTER TABLE "DeviceMode" DROP COLUMN IF EXISTS "pattern";