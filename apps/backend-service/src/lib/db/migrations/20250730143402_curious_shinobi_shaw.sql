CREATE TABLE IF NOT EXISTS "WaveformData" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"name" varchar(100) NOT NULL,
	"description" text,
	"recording_data" json NOT NULL,
	"thumbnail" text,
	"duration" integer NOT NULL,
	"max_intensity" numeric(5, 2),
	"average_intensity" numeric(5, 2),
	"total_distance" numeric(10, 2),
	"complexity" numeric(5, 2),
	"smoothness" numeric(5, 2),
	"is_public" boolean DEFAULT false NOT NULL,
	"like_count" integer DEFAULT 0 NOT NULL,
	"favorite_count" integer DEFAULT 0 NOT NULL,
	"play_count" integer DEFAULT 0 NOT NULL,
	"tags" json DEFAULT '[]' NOT NULL,
	"published_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "WaveformInteraction" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"waveform_id" uuid NOT NULL,
	"interaction_type" varchar NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "WaveformData" ADD CONSTRAINT "WaveformData_user_id_User_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "WaveformInteraction" ADD CONSTRAINT "WaveformInteraction_user_id_User_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "WaveformInteraction" ADD CONSTRAINT "WaveformInteraction_waveform_id_WaveformData_id_fk" FOREIGN KEY ("waveform_id") REFERENCES "public"."WaveformData"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_waveform_data_user_id" ON "WaveformData" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_waveform_data_public" ON "WaveformData" USING btree ("is_public") WHERE "WaveformData"."is_public" = true;--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_waveform_data_created_at" ON "WaveformData" USING btree ("created_at" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_waveform_data_user_created" ON "WaveformData" USING btree ("user_id","created_at" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_waveform_data_public_created" ON "WaveformData" USING btree ("is_public","created_at" DESC NULLS LAST) WHERE "WaveformData"."is_public" = true;--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_waveform_data_like_count" ON "WaveformData" USING btree ("like_count" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_waveform_data_play_count" ON "WaveformData" USING btree ("play_count" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_waveform_interaction_user_id" ON "WaveformInteraction" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_waveform_interaction_waveform_id" ON "WaveformInteraction" USING btree ("waveform_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_waveform_interaction_type" ON "WaveformInteraction" USING btree ("interaction_type");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_waveform_interaction_user_waveform" ON "WaveformInteraction" USING btree ("user_id","waveform_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_waveform_interaction_created_at" ON "WaveformInteraction" USING btree ("created_at" DESC NULLS LAST);--> statement-breakpoint
CREATE UNIQUE INDEX IF NOT EXISTS "idx_waveform_interaction_unique" ON "WaveformInteraction" USING btree ("user_id","waveform_id","interaction_type");