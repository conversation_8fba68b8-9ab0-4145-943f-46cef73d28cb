CREATE TABLE IF NOT EXISTS "DeviceFunctionCommand" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"function_id" uuid NOT NULL,
	"command_set_id" uuid NOT NULL,
	"intensity" integer NOT NULL,
	"description" text,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "DeviceMode" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(100) NOT NULL,
	"description" text,
	"pattern" json NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "DeviceSupportedFunction" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"device_id" uuid NOT NULL,
	"function_id" uuid NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "DeviceSupportedMode" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"device_id" uuid NOT NULL,
	"mode_id" uuid NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
DROP TABLE "archive_metadata";--> statement-breakpoint
-- 只有在源表存在且目标表不存在时才执行重命名
DO $$ BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'DeviceCommand') 
     AND NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'DeviceCommandSet') THEN
    ALTER TABLE "DeviceCommand" RENAME TO "DeviceCommandSet";
  END IF;
END $$;--> statement-breakpoint
ALTER TABLE "DeviceCommandSet" DROP CONSTRAINT IF EXISTS "DeviceCommand_function_id_DeviceFunction_id_fk";
--> statement-breakpoint
ALTER TABLE "DeviceFunction" DROP CONSTRAINT IF EXISTS "DeviceFunction_device_id_Device_id_fk";
--> statement-breakpoint
DROP INDEX IF EXISTS "idx_device_command_function_id";--> statement-breakpoint
DROP INDEX IF EXISTS "idx_device_command_intensity";--> statement-breakpoint
DROP INDEX IF EXISTS "idx_device_command_function_intensity";--> statement-breakpoint
DROP INDEX IF EXISTS "idx_device_function_device_id";--> statement-breakpoint
DROP INDEX IF EXISTS "idx_device_function_device_key";--> statement-breakpoint
ALTER TABLE "DeviceCommandSet" ALTER COLUMN "command" SET DATA TYPE varchar(200);--> statement-breakpoint
ALTER TABLE "DeviceCommandSet" ADD COLUMN IF NOT EXISTS "name" varchar(100);
UPDATE "DeviceCommandSet" SET "name" = COALESCE("command", 'Unknown Command') WHERE "name" IS NULL;
ALTER TABLE "DeviceCommandSet" ALTER COLUMN "name" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "DeviceCommandSet" ADD COLUMN IF NOT EXISTS "broadcast" varchar(200);--> statement-breakpoint
ALTER TABLE "DeviceCommandSet" ADD COLUMN IF NOT EXISTS "is_active" boolean DEFAULT true NOT NULL;--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "DeviceFunctionCommand" ADD CONSTRAINT "DeviceFunctionCommand_function_id_DeviceFunction_id_fk" FOREIGN KEY ("function_id") REFERENCES "public"."DeviceFunction"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "DeviceFunctionCommand" ADD CONSTRAINT "DeviceFunctionCommand_command_set_id_DeviceCommandSet_id_fk" FOREIGN KEY ("command_set_id") REFERENCES "public"."DeviceCommandSet"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "DeviceSupportedFunction" ADD CONSTRAINT "DeviceSupportedFunction_device_id_Device_id_fk" FOREIGN KEY ("device_id") REFERENCES "public"."Device"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "DeviceSupportedFunction" ADD CONSTRAINT "DeviceSupportedFunction_function_id_DeviceFunction_id_fk" FOREIGN KEY ("function_id") REFERENCES "public"."DeviceFunction"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "DeviceSupportedMode" ADD CONSTRAINT "DeviceSupportedMode_device_id_Device_id_fk" FOREIGN KEY ("device_id") REFERENCES "public"."Device"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "DeviceSupportedMode" ADD CONSTRAINT "DeviceSupportedMode_mode_id_DeviceMode_id_fk" FOREIGN KEY ("mode_id") REFERENCES "public"."DeviceMode"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_function_command_function_id" ON "DeviceFunctionCommand" USING btree ("function_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_function_command_command_set_id" ON "DeviceFunctionCommand" USING btree ("command_set_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_function_command_function_intensity" ON "DeviceFunctionCommand" USING btree ("function_id","intensity");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_mode_name" ON "DeviceMode" USING btree ("name");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_mode_active" ON "DeviceMode" USING btree ("is_active");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_supported_function_device_id" ON "DeviceSupportedFunction" USING btree ("device_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_supported_function_function_id" ON "DeviceSupportedFunction" USING btree ("function_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_supported_function_device_function" ON "DeviceSupportedFunction" USING btree ("device_id","function_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_supported_mode_device_id" ON "DeviceSupportedMode" USING btree ("device_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_supported_mode_mode_id" ON "DeviceSupportedMode" USING btree ("mode_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_supported_mode_device_mode" ON "DeviceSupportedMode" USING btree ("device_id","mode_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_command_set_name" ON "DeviceCommandSet" USING btree ("name");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_command_set_active" ON "DeviceCommandSet" USING btree ("is_active");--> statement-breakpoint
ALTER TABLE "DeviceCommandSet" DROP COLUMN IF EXISTS "function_id";--> statement-breakpoint
ALTER TABLE "DeviceCommandSet" DROP COLUMN IF EXISTS "intensity";--> statement-breakpoint
ALTER TABLE "DeviceFunction" DROP COLUMN IF EXISTS "device_id";--> statement-breakpoint
-- Remove duplicates before adding unique constraint
DELETE FROM "DeviceFunction" 
WHERE id NOT IN (
  SELECT DISTINCT ON (key) id
  FROM "DeviceFunction"
  ORDER BY key, created_at ASC
);

-- 只有在约束不存在时才创建
DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'DeviceFunction_key_unique') THEN
    ALTER TABLE "DeviceFunction" ADD CONSTRAINT "DeviceFunction_key_unique" UNIQUE("key");
  END IF;
END $$;