import { drizzle } from 'drizzle-orm/postgres-js'
import { migrate } from 'drizzle-orm/postgres-js/migrator'
import postgres from 'postgres'
import { config } from 'dotenv'

// 尝试加载多个可能的环境文件
config({ path: '.env.local' })
config({ path: '.dev.vars' })

// 数据库连接
const connectionString =
  process.env.APP_VERSION === 'global' ? process.env.DATABASE_URL! : process.env.DATABASE_ZH_URL!

if (!connectionString) {
  console.error('❌ DATABASE_URL 环境变量未设置')
  process.exit(1)
}

console.log('🔗 连接数据库...')
const client = postgres(connectionString, { max: 1 })
const db = drizzle(client)

async function runMigrations() {
  console.log('🚀 开始执行数据库迁移...')

  try {
    await migrate(db, { migrationsFolder: './src/lib/db/migrations' })
    console.log('✅ 数据库迁移完成！')
  } catch (error) {
    console.error('❌ 数据库迁移失败:', error)
    throw error
  } finally {
    await client.end()
  }
}

// 如果直接运行此文件，则执行迁移
if (import.meta.url === `file://${process.argv[1]}`) {
  runMigrations().catch(error => {
    console.error('迁移失败:', error)
    process.exit(1)
  })
}

export { runMigrations }
