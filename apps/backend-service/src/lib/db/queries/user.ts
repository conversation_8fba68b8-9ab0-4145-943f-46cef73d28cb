import { getSupabase } from './base'
import { handleSupabaseResult, handleSupabaseSingleResult, TABLE_NAMES } from '../supabase-types'
import type { User, UserProfile } from '../schema'
import type { Env } from '@/types/env'
import { getSystemConfig } from './system-config'
import { PointsService } from '@/lib/membership/points'

// ==================== 用户基础操作 ====================

export async function getUser(env: Env, email: string): Promise<User[]> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase.from(TABLE_NAMES.user).select('*').eq('email', email)

    const { data, error } = handleSupabaseResult(result)
    if (error) throw error
    return data || []
  } catch (error) {
    console.error('Failed to get user from database', error)
    throw error
  }
}

export async function createUser(env: Env, email: string, supabaseUserId: string): Promise<User[]> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.user)
      .insert({
        email,
        supabase_user_id: supabaseUserId
      })
      .select()

    const { data, error } = handleSupabaseResult(result)
    if (error) throw error
    return data || []
  } catch (error) {
    console.error('Failed to create user in database', error)
    throw error
  }
}

export async function getUserById(env: Env, id: string): Promise<User | null> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase.from(TABLE_NAMES.user).select('*').eq('id', id).single()

    const { data, error } = handleSupabaseSingleResult(result)
    if (error) throw error
    return data
  } catch (error) {
    console.error('Failed to get user by id from database', error)
    throw error
  }
}

export async function getUserBySupabaseId(env: Env, supabaseUserId: string): Promise<User | null> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.user)
      .select('*')
      .eq('supabase_user_id', supabaseUserId)

    const { data, error } = handleSupabaseResult(result)
    if (error) throw error

    // 返回第一个匹配的用户，如果没有则返回 null
    return data && data.length > 0 ? data[0] : null
  } catch (error) {
    console.error('Failed to get user by supabase id from database', error)
    return null // 改为返回 null 而不是抛出错误
  }
}

// ==================== 用户配置文件操作 ====================

export async function getUserProfile(env: Env, userId: string): Promise<UserProfile | null> {
  try {
    // 简单验证参数
    if (!userId || typeof userId !== 'string') {
      console.log('getUserProfile: 无效的用户ID')
      return null
    }

    console.log(`查询用户 ${userId} 的配置文件`)

    const supabase = getSupabase(env)
    const result = await supabase.from(TABLE_NAMES.userProfile).select('*').eq('user_id', userId)

    const { data, error } = handleSupabaseResult(result)
    if (error) {
      console.log(`查询用户 ${userId} 配置文件时发生错误:`, error)
      return null
    }

    // 返回第一个匹配的配置文件，如果没有则返回 null
    const profile = data && data.length > 0 ? data[0] : null
    console.log(`用户 ${userId} 的配置文件查询结果:`, profile ? '找到配置文件' : '无配置文件')
    return profile
  } catch (error) {
    console.log(`获取用户 ${userId} 配置文件时发生异常:`, (error as any)?.message || error)
    return null // 改为返回 null 而不是抛出错误
  }
}

export async function createUserProfile(
  env: Env,
  data: {
    userId: string
    nickname?: string
    gender?: 'male' | 'female' | 'other'
    avatarUrl?: string
  }
): Promise<UserProfile[]> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.userProfile)
      .insert({
        user_id: data.userId,
        nickname: data.nickname,
        gender: data.gender,
        avatar_url: data.avatarUrl
      })
      .select()

    const { data: insertedData, error } = handleSupabaseResult(result)
    if (error) throw error
    return insertedData || []
  } catch (error) {
    console.error('创建用户配置文件失败', error)
    throw error
  }
}

export async function updateUserProfile(
  env: Env,
  userId: string,
  data: {
    nickname?: string
    gender?: 'male' | 'female' | 'other'
    avatarUrl?: string
  }
): Promise<UserProfile[]> {
  try {
    const supabase = getSupabase(env)

    // 转换字段名为 snake_case
    const updateData: any = {}
    if (data.nickname !== undefined) updateData.nickname = data.nickname
    if (data.gender !== undefined) updateData.gender = data.gender
    if (data.avatarUrl !== undefined) updateData.avatar_url = data.avatarUrl
    updateData.updated_at = new Date().toISOString()

    const result = await supabase
      .from(TABLE_NAMES.userProfile)
      .update(updateData)
      .eq('user_id', userId)
      .select()

    const { data: updatedData, error } = handleSupabaseResult(result)
    if (error) throw error
    return updatedData || []
  } catch (error) {
    console.error('更新用户配置文件失败', error)
    throw error
  }
}

// ==================== 新用户积分初始化 ====================

/**
 * 为新用户异步初始化积分
 * 使用Promise.resolve()确保不阻塞主流程
 */
export function initializeNewUserPointsAsync(env: Env, userId: string): void {
  // 使用Promise.resolve()在下一个事件循环中执行
  Promise.resolve()
    .then(async () => {
      try {
        console.log(`开始为新用户 ${userId} 初始化积分...`)

        // 1. 获取新用户初始积分配置
        const newUserPointsConfig = await getSystemConfig(env, 'NEW_USER_POINTS')
        const initialPoints = newUserPointsConfig ? parseInt(newUserPointsConfig, 10) : 50 // 默认50积分

        if (initialPoints <= 0) {
          console.log(`新用户初始积分为 ${initialPoints}，跳过积分初始化`)
          return
        }

        // 2. 使用积分服务添加积分
        const pointsService = new PointsService(env)
        const result = await pointsService.addPoints(
          userId,
          initialPoints,
          'bonus', // 使用bonus作为来源
          undefined, // sourceId
          `新用户注册奖励${initialPoints}积分`
        )

        if (result.success) {
          console.log(`✅ 新用户 ${userId} 成功获得初始积分: ${initialPoints}`)
        } else {
          console.error(`❌ 新用户 ${userId} 积分初始化失败: ${result.error}`)
        }
      } catch (error) {
        console.error(`新用户 ${userId} 积分初始化过程中出错:`, error)
        // 不抛出错误，避免影响其他流程
      }
    })
    .catch(error => {
      // 捕获Promise中的任何错误
      console.error(`新用户 ${userId} 积分初始化Promise失败:`, error)
    })
}

// ==================== 手机号相关操作 ====================

export async function getUserByPhone(env: Env, phone: string): Promise<User[]> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase.from(TABLE_NAMES.user).select('*').eq('phone', phone)

    const { data, error } = handleSupabaseResult(result)
    if (error) throw error
    return data || []
  } catch (error) {
    console.error('Failed to get user by phone from database', error)
    throw error
  }
}

export async function createUserByPhone(env: Env, phone: string, supabaseUserId: string): Promise<User[]> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.user)
      .insert({
        phone,
        supabase_user_id: supabaseUserId
      })
      .select()

    const { data, error } = handleSupabaseResult(result)
    if (error) throw error
    return data || []
  } catch (error) {
    console.error('Failed to create user by phone in database', error)
    throw error
  }
}
