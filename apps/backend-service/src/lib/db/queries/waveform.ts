import { getSupabase } from './base'
import { handleSupabaseResult, handleSupabaseSingleResult, TABLE_NAMES } from '../supabase-types'
import type { WaveformData, WaveformInteraction } from '../schema'
import type { Env } from '@/types/env'

// 波形数据类型定义
export interface WaveformRecording {
  id: string
  name: string
  startTime: number
  endTime: number
  duration: number
  points: number[][]
  canvasSize: [number, number]
  metadata: [number, number, number, number] // [maxIntensity, avgIntensity, touchCount, totalDistance]
}

export interface WaveformLibraryItem {
  id: string
  name: string
  description?: string
  authorId: string
  authorName: string
  authorAvatar?: string
  duration: number
  maxIntensity?: number
  averageIntensity?: number
  totalDistance?: number
  complexity?: number
  smoothness?: number
  isPublic: boolean
  isOwner: boolean
  likeCount: number
  favoriteCount: number
  playCount: number
  tags: string[]
  thumbnail?: string
  publishedAt?: Date
  createdAt: Date
  updatedAt: Date
  // 用户互动状态
  isLiked?: boolean
  isFavorited?: boolean
  // 轨迹点数据（用于预览）
  points: number[][]
}

export interface WaveformListResponse {
  items: WaveformLibraryItem[]
  total: number
  hasMore: boolean
}

export interface WaveformFilter {
  sortBy?: 'latest' | 'popular' | 'most_played'
  search?: string
  tags?: string[]
  page?: number
  limit?: number
}

// 辅助函数：提取轨迹点数据
function extractPoints(recordingData: any): number[][] {
  if (!recordingData?.points) return []

  // 只返回前100个点用于预览（避免数据过大）
  const points = Array.isArray(recordingData.points) ? recordingData.points : []
  return points.slice(0, 100)
}

// 辅助函数：转换数据库记录为WaveformLibraryItem
function mapToWaveformLibraryItem(
  waveform: any,
  userId?: string,
  interactions: any[] = []
): WaveformLibraryItem {
  const isLiked = interactions.some((i: any) => i.interaction_type === 'like')
  const isFavorited = interactions.some((i: any) => i.interaction_type === 'favorite')

  return {
    id: waveform.id,
    name: waveform.name,
    description: waveform.description,
    authorId: waveform.user_id,
    authorName: waveform.author_name || 'Unknown User',
    authorAvatar: waveform.author_avatar,
    duration: waveform.duration,
    maxIntensity: waveform.max_intensity ? parseFloat(waveform.max_intensity) : undefined,
    averageIntensity: waveform.average_intensity
      ? parseFloat(waveform.average_intensity)
      : undefined,
    totalDistance: waveform.total_distance ? parseFloat(waveform.total_distance) : undefined,
    complexity: waveform.complexity ? parseFloat(waveform.complexity) : undefined,
    smoothness: waveform.smoothness ? parseFloat(waveform.smoothness) : undefined,
    isPublic: waveform.is_public,
    isOwner: userId === waveform.user_id,
    likeCount: waveform.like_count,
    favoriteCount: waveform.favorite_count,
    playCount: waveform.play_count,
    tags: Array.isArray(waveform.tags) ? waveform.tags : [],
    thumbnail: waveform.thumbnail,
    publishedAt: waveform.published_at ? new Date(waveform.published_at) : undefined,
    createdAt: new Date(waveform.created_at),
    updatedAt: new Date(waveform.updated_at),
    isLiked,
    isFavorited,
    points: extractPoints(waveform.recording_data)
  }
}

/**
 * 创建波形数据
 */
export async function createWaveform(
  env: Env,
  userId: string,
  data: {
    name: string
    description?: string
    recordingData: WaveformRecording
    isPublic?: boolean
    tags?: string[]
    thumbnail?: string
    maxIntensity?: number
    averageIntensity?: number
    totalDistance?: number
    complexity?: number
    smoothness?: number
  }
): Promise<WaveformData[]> {
  try {
    const supabase = getSupabase(env)

    // 获取用户信息用于作者字段
    const userResult = await supabase
      .from(TABLE_NAMES.userProfile)
      .select('nickname, avatar_url')
      .eq('user_id', userId)
      .single()

    const { data: profile } = handleSupabaseSingleResult(userResult)

    const result = await supabase
      .from(TABLE_NAMES.waveformData)
      .insert({
        user_id: userId,
        name: data.name,
        description: data.description,
        recording_data: data.recordingData,
        thumbnail: data.thumbnail,
        duration: data.recordingData.duration,
        max_intensity: data.maxIntensity?.toString(),
        average_intensity: data.averageIntensity?.toString(),
        total_distance: data.totalDistance?.toString(),
        complexity: data.complexity?.toString(),
        smoothness: data.smoothness?.toString(),
        is_public: data.isPublic || false,
        tags: data.tags || [],
        author_name: profile?.nickname || 'Unknown User',
        author_avatar: profile?.avatar_url,
        published_at: data.isPublic ? new Date().toISOString() : null
      })
      .select()

    const { data: waveforms, error } = handleSupabaseResult(result)
    if (error) throw error
    return waveforms || []
  } catch (error) {
    console.error('创建波形数据失败', error)
    throw error
  }
}

/**
 * 根据ID获取波形数据
 */
export async function getWaveformById(
  env: Env,
  waveformId: string,
  userId?: string
): Promise<(WaveformLibraryItem & { recordingData: WaveformRecording }) | null> {
  try {
    const supabase = getSupabase(env)

    // 查询波形数据
    const waveformResult = await supabase
      .from(TABLE_NAMES.waveformData)
      .select('*')
      .eq('id', waveformId)
      .single()

    const { data: waveform, error } = handleSupabaseSingleResult(waveformResult)
    if (error || !waveform) return null

    // 如果有用户ID，查询用户的互动状态
    let interactions: any[] = []
    if (userId) {
      const interactionResult = await supabase
        .from(TABLE_NAMES.waveformInteraction)
        .select('interaction_type')
        .eq('user_id', userId)
        .eq('waveform_id', waveformId)

      const { data: interactionData } = handleSupabaseResult(interactionResult)
      interactions = interactionData || []
    }

    const libraryItem = mapToWaveformLibraryItem(waveform, userId, interactions)

    return {
      ...libraryItem,
      recordingData: waveform.recording_data as WaveformRecording
    }
  } catch (error) {
    console.error('获取波形数据失败', error)
    return null
  }
}

/**
 * 获取用户的波形列表
 */
export async function getUserWaveforms(
  env: Env,
  userId: string,
  filter: WaveformFilter = {}
): Promise<WaveformListResponse> {
  try {
    const supabase = getSupabase(env)
    const { page = 1, limit = 20, sortBy = 'latest', search, tags } = filter

    let query = supabase
      .from(TABLE_NAMES.waveformData)
      .select('*', { count: 'exact' })
      .eq('user_id', userId)

    // 搜索条件
    if (search) {
      query = query.ilike('name', `%${search}%`)
    }

    // 标签筛选
    if (tags && tags.length > 0) {
      query = query.overlaps('tags', tags)
    }

    // 排序
    switch (sortBy) {
      case 'popular':
        query = query.order('like_count', { ascending: false })
        break
      case 'most_played':
        query = query.order('play_count', { ascending: false })
        break
      default:
        query = query.order('created_at', { ascending: false })
    }

    // 分页
    const from = (page - 1) * limit
    query = query.range(from, from + limit - 1)

    const { data: waveforms, error, count } = await query

    if (error) throw error

    // 查询用户的互动状态
    const waveformIds = (waveforms || []).map((w: any) => w.id)
    let interactions: any[] = []

    if (waveformIds.length > 0) {
      const interactionResult = await supabase
        .from(TABLE_NAMES.waveformInteraction)
        .select('waveform_id, interaction_type')
        .eq('user_id', userId)
        .in('waveform_id', waveformIds)

      const { data: interactionData } = handleSupabaseResult(interactionResult)
      interactions = interactionData || []
    }

    const items = (waveforms || []).map((waveform: any) => {
      const waveformInteractions = interactions.filter((i: any) => i.waveform_id === waveform.id)
      return mapToWaveformLibraryItem(waveform, userId, waveformInteractions)
    })

    return {
      items,
      total: count || 0,
      hasMore: (count || 0) > page * limit
    }
  } catch (error) {
    console.error('获取用户波形列表失败', error)
    throw error
  }
}

/**
 * 获取公共波形库
 */
export async function getPublicWaveforms(
  env: Env,
  userId?: string,
  filter: WaveformFilter = {}
): Promise<WaveformListResponse> {
  try {
    const supabase = getSupabase(env)
    const { page = 1, limit = 20, sortBy = 'latest', search, tags } = filter

    let query = supabase
      .from(TABLE_NAMES.waveformData)
      .select('*', { count: 'exact' })
      .eq('is_public', true)

    // 搜索条件
    if (search) {
      query = query.ilike('name', `%${search}%`)
    }

    // 标签筛选
    if (tags && tags.length > 0) {
      query = query.overlaps('tags', tags)
    }

    // 排序
    switch (sortBy) {
      case 'popular':
        query = query.order('like_count', { ascending: false })
        break
      case 'most_played':
        query = query.order('play_count', { ascending: false })
        break
      default:
        query = query.order('created_at', { ascending: false })
    }

    // 分页
    const from = (page - 1) * limit
    query = query.range(from, from + limit - 1)

    const { data: waveforms, error, count } = await query

    if (error) throw error

    // 查询用户的互动状态
    let interactions: any[] = []
    if (userId) {
      const waveformIds = (waveforms || []).map((w: any) => w.id)

      if (waveformIds.length > 0) {
        const interactionResult = await supabase
          .from(TABLE_NAMES.waveformInteraction)
          .select('waveform_id, interaction_type')
          .eq('user_id', userId)
          .in('waveform_id', waveformIds)

        const { data: interactionData } = handleSupabaseResult(interactionResult)
        interactions = interactionData || []
      }
    }

    const items = (waveforms || []).map((waveform: any) => {
      const waveformInteractions = interactions.filter((i: any) => i.waveform_id === waveform.id)
      return mapToWaveformLibraryItem(waveform, userId, waveformInteractions)
    })

    return {
      items,
      total: count || 0,
      hasMore: (count || 0) > page * limit
    }
  } catch (error) {
    console.error('获取公共波形库失败', error)
    throw error
  }
}

/**
 * 获取收藏的波形
 */
export async function getFavoriteWaveforms(
  env: Env,
  userId: string,
  filter: WaveformFilter = {}
): Promise<WaveformListResponse> {
  try {
    const supabase = getSupabase(env)
    const { page = 1, limit = 20 } = filter

    // 先获取用户收藏的波形ID
    const favoriteResult = await supabase
      .from(TABLE_NAMES.waveformInteraction)
      .select('waveform_id')
      .eq('user_id', userId)
      .eq('interaction_type', 'favorite')

    const { data: favorites } = handleSupabaseResult(favoriteResult)
    if (!favorites || favorites.length === 0) {
      return { items: [], total: 0, hasMore: false }
    }

    const waveformIds = favorites.map((f: any) => f.waveform_id)

    // 查询波形数据
    let query = supabase
      .from(TABLE_NAMES.waveformData)
      .select('*', { count: 'exact' })
      .in('id', waveformIds)
      .order('created_at', { ascending: false })

    // 分页
    const from = (page - 1) * limit
    query = query.range(from, from + limit - 1)

    const { data: waveforms, error, count } = await query

    if (error) throw error

    // 查询用户的互动状态
    const interactionResult = await supabase
      .from(TABLE_NAMES.waveformInteraction)
      .select('waveform_id, interaction_type')
      .eq('user_id', userId)
      .in('waveform_id', waveformIds)

    const { data: interactionData } = handleSupabaseResult(interactionResult)
    const interactions = interactionData || []

    const items = (waveforms || []).map((waveform: any) => {
      const waveformInteractions = interactions.filter((i: any) => i.waveform_id === waveform.id)
      return mapToWaveformLibraryItem(waveform, userId, waveformInteractions)
    })

    return {
      items,
      total: count || 0,
      hasMore: (count || 0) > page * limit
    }
  } catch (error) {
    console.error('获取收藏波形失败', error)
    throw error
  }
}

/**
 * 切换波形互动状态（点赞/收藏）
 */
export async function toggleWaveformInteraction(
  env: Env,
  userId: string,
  waveformId: string,
  type: 'like' | 'favorite'
): Promise<{ success: boolean; isActive: boolean; count: number }> {
  try {
    const supabase = getSupabase(env)

    // 检查是否已存在互动记录
    const existingResult = await supabase
      .from(TABLE_NAMES.waveformInteraction)
      .select('id')
      .eq('user_id', userId)
      .eq('waveform_id', waveformId)
      .eq('interaction_type', type)
      .single()

    const { data: existing } = handleSupabaseSingleResult(existingResult)

    let isActive: boolean
    const countField = type === 'like' ? 'like_count' : 'favorite_count'

    if (existing) {
      // 删除互动记录
      await supabase.from(TABLE_NAMES.waveformInteraction).delete().eq('id', existing.id)

      // 减少计数
      await supabase
        .from(TABLE_NAMES.waveformData)
        .update({ [countField]: supabase.rpc('decrement_count', { field: countField }) })
        .eq('id', waveformId)

      isActive = false
    } else {
      // 创建互动记录
      await supabase.from(TABLE_NAMES.waveformInteraction).insert({
        user_id: userId,
        waveform_id: waveformId,
        interaction_type: type
      })

      // 增加计数
      await supabase
        .from(TABLE_NAMES.waveformData)
        .update({ [countField]: supabase.rpc('increment_count', { field: countField }) })
        .eq('id', waveformId)

      isActive = true
    }

    // 获取更新后的计数
    const countResult = await supabase
      .from(TABLE_NAMES.waveformData)
      .select(countField)
      .eq('id', waveformId)
      .single()

    const { data: countData } = handleSupabaseSingleResult(countResult)
    const count = countData?.[countField] || 0

    return { success: true, isActive, count }
  } catch (error) {
    console.error('切换波形互动状态失败', error)
    return { success: false, isActive: false, count: 0 }
  }
}

/**
 * 记录波形播放
 */
export async function recordWaveformPlay(
  env: Env,
  userId: string,
  waveformId: string
): Promise<{ success: boolean; playCount: number }> {
  try {
    const supabase = getSupabase(env)

    // 创建播放记录
    await supabase.from(TABLE_NAMES.waveformInteraction).insert({
      user_id: userId,
      waveform_id: waveformId,
      interaction_type: 'play'
    })

    // 增加播放计数
    await supabase
      .from(TABLE_NAMES.waveformData)
      .update({ play_count: supabase.rpc('increment_count', { field: 'play_count' }) })
      .eq('id', waveformId)

    // 获取更新后的播放计数
    const countResult = await supabase
      .from(TABLE_NAMES.waveformData)
      .select('play_count')
      .eq('id', waveformId)
      .single()

    const { data: countData } = handleSupabaseSingleResult(countResult)
    const playCount = countData?.play_count || 0

    return { success: true, playCount }
  } catch (error) {
    console.error('记录波形播放失败', error)
    return { success: false, playCount: 0 }
  }
}

/**
 * 删除波形数据
 */
export async function deleteWaveform(
  env: Env,
  userId: string,
  waveformId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = getSupabase(env)

    // 检查波形是否存在且属于用户
    const waveformResult = await supabase
      .from(TABLE_NAMES.waveformData)
      .select('user_id')
      .eq('id', waveformId)
      .single()

    const { data: waveform, error } = handleSupabaseSingleResult(waveformResult)
    if (error || !waveform) {
      return { success: false, error: '波形不存在' }
    }

    if (waveform.user_id !== userId) {
      return { success: false, error: '无权限删除此波形' }
    }

    // 删除波形数据（相关的互动记录会被级联删除）
    const deleteResult = await supabase.from(TABLE_NAMES.waveformData).delete().eq('id', waveformId)

    const { error: deleteError } = deleteResult
    if (deleteError) throw deleteError

    return { success: true }
  } catch (error) {
    console.error('删除波形失败', error)
    return { success: false, error: '删除失败' }
  }
}
