// Supabase 数据库表类型定义和转换工具
// 这个文件定义了数据库表的 snake_case 类型和与 Drizzle camelCase 类型的转换

import type { SupabaseClient } from '@supabase/supabase-js'

// ==================== 数据库表 snake_case 类型 ====================

// User 表的数据库类型（snake_case）
export interface UserRow {
  id: string
  email: string
  supabase_user_id: string | null
  created_at: string
  updated_at: string
}

// UserProfile 表的数据库类型（snake_case）
export interface UserProfileRow {
  id: string
  user_id: string
  nickname: string | null
  gender: 'male' | 'female' | 'other' | null
  avatar_url: string | null
  bio: string | null
  created_at: string
  updated_at: string
}

// Chat 表的数据库类型（snake_case）
export interface ChatRow {
  id: string
  user_id: string
  title: string
  visibility: 'public' | 'private'
  character_id: string | null
  background_image_url: string | null
  created_at: string
  updated_at: string
}

// Message 表的数据库类型（snake_case）
export interface MessageRow {
  id: string
  chat_id: string
  role: 'user' | 'assistant' | 'system'
  parts: any[] // JSON 类型
  attachments: any[] // JSON 类型
  created_at: string
}

// Character 表的数据库类型（snake_case）
export interface CharacterRow {
  id: string
  user_id: string
  name: string
  description: string | null
  relationship: string | null
  ethnicity: string | null
  gender: 'male' | 'female' | 'other' | null
  age: string | null
  eye_color: string | null
  hair_style: string | null
  hair_color: string | null
  face_shape: string | null
  body_type: string | null
  breast_size: string | null
  butt_size: string | null
  personality: string | null
  clothing: string | null
  voice: string | null
  voice_model_id: string | null
  keywords: string
  prompt: string
  image_url: string | null
  category: string | null
  is_public: boolean
  is_active: boolean
  created_at: string
  updated_at: string
}

// MembershipPlan 表的数据库类型（snake_case）
export interface MembershipPlanRow {
  id: string
  name: string
  description: string | null
  price: string // Decimal as string
  duration_days: number
  points_included: number
  features: any // JSON 类型
  is_active: boolean
  sort_order: number | null
  created_at: string
  updated_at: string
}

// UserSubscription 表的数据库类型（snake_case）
export interface UserSubscriptionRow {
  id: string
  user_id: string
  plan_id: string
  start_date: string
  end_date: string
  status: 'active' | 'expired' | 'cancelled' | 'pending'
  auto_renew: boolean
  payment_id: string | null
  created_at: string
  updated_at: string
}

// UserPoints 表的数据库类型（snake_case）
export interface UserPointsRow {
  id: string
  user_id: string
  total_points: number
  used_points: number
  available_points: number
  cycle_start_date: string | null
  cycle_end_date: string | null
  membership_level: string | null
  monthly_allocation: number | null
  cycle_consumed: number | null
  cycle_gifted: number | null
  cycle_received: number | null
  last_cycle_check: string | null
  last_updated: string
}

// PointsTransaction 表的数据库类型（snake_case）
export interface PointsTransactionRow {
  id: string
  user_id: string
  transaction_type:
    | 'earn'
    | 'spend'
    | 'refund'
    | 'bonus'
    | 'cycle_grant'
    | 'cycle_reset'
    | 'gift_sent'
    | 'gift_received'
    | 'upgrade_bonus'
  amount: number
  source:
    | 'subscription'
    | 'purchase'
    | 'generation'
    | 'refund'
    | 'bonus'
    | 'admin'
    | 'cycle_grant'
    | 'cycle_reset'
    | 'gift'
    | 'upgrade'
  source_id: string | null
  description: string | null
  balance_after: number
  metadata: any // JSON 类型
  created_at: string
}

// PointsPackage 表的数据库类型（snake_case）
export interface PointsPackageRow {
  id: string
  name: string
  description: string | null
  points: number
  price: string // Decimal as string
  bonus_points: number | null
  is_active: boolean
  sort_order: number | null
  created_at: string
  updated_at: string
}

// PaymentOrder 表的数据库类型（snake_case）
export interface PaymentOrderRow {
  id: string
  user_id: string
  plan_id: string | null
  points_package_id: string | null
  order_no: string
  external_order_id: string | null
  amount: string // Decimal as string
  currency: string
  payment_method: string | null
  status: 'pending' | 'paid' | 'failed' | 'cancelled' | 'expired'
  description: string
  is_upgrade: boolean
  original_amount: string | null
  current_subscription_id: string | null
  callback_url: string | null
  return_url: string | null
  notify_url: string | null
  created_at: string
  paid_at: string | null
  expires_at: string | null
  metadata: any // JSON 类型
}

// AudioEffect 表的数据库类型（snake_case）
export interface AudioEffectRow {
  id: string
  name: string | null
  description: string | null
  category_id: string | null
  tags: any[] // JSON 类型
  url: string
  duration: string // Decimal as string
  avg_pitch: string | null
  avg_loudness: string | null
  energy_variation: string | null
  is_public: boolean
  is_active: boolean
  usage_count: number
  created_by: string | null
  created_at: string
  updated_at: string
}

// VoiceModel 表的数据库类型（snake_case）
export interface VoiceModelRow {
  id: string
  model_id: string
  name: string
  display_name: string
  description: string | null
  gender: 'male' | 'female' | 'neutral'
  language: string
  supported_languages: any // JSON 类型
  category: string | null
  tags: any // JSON 类型
  is_active: boolean
  is_premium: boolean
  sort_order: number | null
  created_at: string
  updated_at: string
}

// Template 表的数据库类型（snake_case）
export interface TemplateRow {
  id: string
  name: string
  description: string | null
  category: string | null
  preview_image: string | null
  prompt: string
  negative_prompt: string | null
  points_cost: number
  is_premium: boolean
  is_public: boolean
  is_active: boolean
  tags: any // JSON 类型
  settings: any // JSON 类型
  created_by: string | null
  usage_count: number
  created_at: string
  updated_at: string
}

// Script 表的数据库类型（snake_case）
export interface ScriptRow {
  id: string
  title: string
  description: string
  cover_image: string
  duration: string
  tags: any[] // JSON 类型
  category: string | null
  content: any // JSON 类型
  audio_url: string | null
  total_duration: number | null
  stage_count: number | null
  is_public: boolean
  is_active: boolean
  is_premium: boolean
  usage_count: number
  rating: string | null // Decimal as string
  rating_count: number
  created_by: string | null
  created_at: string
  updated_at: string
}

// ScriptPurchase 表的数据库类型（snake_case）
export interface ScriptPurchaseRow {
  id: string
  user_id: string
  script_id: string
  points_cost: number
  transaction_id: string | null
  status: 'completed' | 'refunded'
  expires_at: string
  is_downloaded: boolean
  downloaded_at: string | null
  created_at: string
  updated_at: string
}

// AudioCategory 表的数据库类型（snake_case）
export interface AudioCategoryRow {
  id: string
  name: string
  display_name: string | null
  description: string | null
  parent_id: string | null
  is_active: boolean
  sort_order: number | null
  created_at: string
  updated_at: string
}

// VoiceSample 表的数据库类型（snake_case）
export interface VoiceSampleRow {
  id: string
  voice_model_id: string
  language: string
  sample_text: string
  audio_url: string | null
  duration: string | null // Decimal as string
  file_size: number | null
  is_default: boolean
  created_at: string
  updated_at: string
}

// MediaGeneration 表的数据库类型（snake_case）
export interface MediaGenerationRow {
  id: string
  user_id: string
  character_id: string | null
  chat_id: string | null
  message_id: string | null
  media_type: 'image' | 'video' | 'audio'
  generation_type: 'multimodal_chat' | 'standalone' | 'template_based'
  prompt: string | null
  negative_prompt: string | null
  input_image_url: string | null
  output_urls: any[] // JSON 类型
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
  error_message: string | null
  points_used: number
  generation_time: number | null
  completed_at: string | null
  metadata: any // JSON 类型
  created_at: string
  updated_at: string
}

// ScriptUsage 表的数据库类型（snake_case）
export interface ScriptUsageRow {
  id: string
  user_id: string
  script_id: string
  chat_id: string | null
  duration: string | null // Decimal as string
  rating: number | null
  feedback: string | null
  created_at: string
  updated_at: string
}

// Device 表的数据库类型（snake_case）
export interface DeviceRow {
  id: string
  user_id: string | null
  device_code: string
  name: string
  pic: string | null
  brand: string | null
  model: string | null
  category: string | null
  description: string | null
  is_active: boolean
  last_connected_at: string | null
  created_at: string
  updated_at: string
}

// DeviceFunction 表的数据库类型（snake_case）
export interface DeviceFunctionRow {
  id: string
  device_id: string
  name: string
  key: string
  description: string | null
  max_intensity: number | null
  is_active: boolean
  created_at: string
  updated_at: string
}

// DeviceCommand 表的数据库类型（snake_case）
export interface DeviceCommandRow {
  id: string
  function_id: string
  intensity: number
  command: string
  description: string | null
  created_at: string
  updated_at: string
}

// DeviceConnection 表的数据库类型（snake_case）
export interface DeviceConnectionRow {
  id: string
  user_id: string
  device_id: string
  session_id: string | null
  status: 'connected' | 'disconnected' | 'error'
  connected_at: string
  disconnected_at: string | null
  error_message: string | null
  metadata: any // JSON 类型
  created_at: string
  updated_at: string
}

// DeviceUsage 表的数据库类型（snake_case）
export interface DeviceUsageRow {
  id: string
  user_id: string
  device_id: string
  script_id: string | null
  session_id: string | null
  function_key: string
  intensity: number
  duration: string | null // Decimal as string
  started_at: string
  ended_at: string | null
  metadata: any // JSON 类型
  created_at: string
  updated_at: string
}

// ==================== 类型转换工具函数 ====================

// 通用转换函数：snake_case -> camelCase
export function toCamelCase(obj: Record<string, any>): any {
  if (!obj || typeof obj !== 'object') return obj

  const result: any = {}
  for (const [key, value] of Object.entries(obj)) {
    const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase())
    result[camelKey] = value
  }
  return result
}

// 通用转换函数：camelCase -> snake_case
export function toSnakeCase(obj: Record<string, any>): any {
  if (!obj || typeof obj !== 'object') return obj

  const result: any = {}
  for (const [key, value] of Object.entries(obj)) {
    const snakeKey = key.replace(/([A-Z])/g, '_$1').toLowerCase()
    result[snakeKey] = value
  }
  return result
}

// 批量转换函数
export function convertRowsToCamelCase<T extends Record<string, any>>(rows: T[]): any[] {
  return rows.map(toCamelCase)
}

// 时间戳处理函数（将 Supabase timestamp 转换为 Date 对象）
export function convertTimestamps(obj: Record<string, any>): Record<string, any> {
  const result = { ...obj }
  const timestampFields = [
    'createdAt',
    'updatedAt',
    'startDate',
    'endDate',
    'lastUpdated',
    'paidAt',
    'expiresAt'
  ]

  for (const field of timestampFields) {
    if (result[field] && typeof result[field] === 'string') {
      result[field] = new Date(result[field] as string)
    }
  }

  return result
}

// 数值处理函数（将 Decimal 字符串转换为 number）
export function convertDecimals(obj: Record<string, any>): Record<string, any> {
  const result = { ...obj }
  const decimalFields = [
    'price',
    'amount',
    'originalAmount',
    'totalDuration', // 只转换 totalDuration（秒数），不转换 duration（时间格式字符串）
    'avgPitch',
    'avgLoudness',
    'energyVariation',
    'rating'
  ]

  for (const field of decimalFields) {
    if (result[field] && typeof result[field] === 'string') {
      result[field] = Number.parseFloat(result[field] as string)
    }
  }

  return result
}

// 组合转换函数：完整的 snake_case -> camelCase 转换
export function convertDbRowToTyped(row: Record<string, any>): any {
  if (!row) return null

  const camelCased = toCamelCase(row)
  const withTimestamps = convertTimestamps(camelCased)
  const withDecimals = convertDecimals(withTimestamps)

  return withDecimals
}

// 批量完整转换
export function convertDbRowsToTyped(rows: Record<string, any>[]): any[] {
  return rows.map(convertDbRowToTyped)
}

// ==================== Supabase 查询结果处理工具 ====================

// 处理 Supabase 查询结果的通用函数
export function handleSupabaseResult(result: {
  data: Record<string, any>[] | Record<string, any> | null
  error: any
}): { data: any; error: any } {
  if (result.error) {
    return { data: null, error: result.error }
  }

  if (Array.isArray(result.data)) {
    return { data: convertDbRowsToTyped(result.data), error: null }
  } else if (result.data) {
    return { data: convertDbRowToTyped(result.data), error: null }
  } else {
    return { data: null, error: null }
  }
}

// 处理单条记录查询结果
export function handleSupabaseSingleResult(result: {
  data: Record<string, any> | null
  error: any
}): { data: any; error: any } {
  if (result.error) {
    return { data: null, error: result.error }
  }

  return { data: result.data ? convertDbRowToTyped(result.data) : null, error: null }
}

// AppVersion 表的数据库类型（snake_case）
export interface AppVersionRow {
  id: string
  version_name: string
  version_code: number
  version_type: 'apk' | 'hotfix'
  file_url: string
  file_size: number | null
  file_hash: string | null
  min_compatible_version: string | null
  release_notes: string | null
  is_active: boolean
  created_at: string
  updated_at: string
}

// AppUpdatePolicy 表的数据库类型（snake_case）
export interface AppUpdatePolicyRow {
  id: string
  version_id: string
  channel: string
  update_strategy: 'force' | 'optional' | 'silent'
  target_version_min: string | null
  target_version_max: string | null
  rollout_percentage: number
  is_active: boolean
  created_at: string
  updated_at: string
}

// AppUpdateLog 表的数据库类型（snake_case）
export interface AppUpdateLogRow {
  id: string
  user_id: string | null
  device_id: string | null
  current_version: string | null
  target_version: string | null
  update_type: 'apk' | 'hotfix'
  update_status:
    | 'started'
    | 'downloading'
    | 'downloaded'
    | 'installing'
    | 'installed'
    | 'failed'
    | 'cancelled'
  error_message: string | null
  metadata: any // JSON 类型
  created_at: string
}

// ==================== 数据库表名映射 ====================

export const TABLE_NAMES = {
  user: 'User',
  userProfile: 'UserProfile',
  chat: 'Chat',
  message: 'Message',
  character: 'Character',
  membershipPlan: 'MembershipPlan',
  userSubscription: 'UserSubscription',
  userPoints: 'UserPoints',
  pointsTransaction: 'PointsTransaction',
  pointsPackage: 'PointsPackage',
  paymentOrder: 'PaymentOrder',
  audioEffect: 'AudioEffect',
  audioCategory: 'AudioCategory',
  voiceModel: 'VoiceModel',
  voiceSample: 'VoiceSample',
  template: 'Template',
  mediaGeneration: 'MediaGeneration',
  script: 'Script',
  scriptUsage: 'ScriptUsage',
  scriptPurchase: 'ScriptPurchase',
  device: 'Device',
  deviceFunction: 'DeviceFunction',
  deviceCommand: 'DeviceCommand',
  deviceConnection: 'DeviceConnection',
  deviceUsage: 'DeviceUsage',
  systemConfig: 'SystemConfig',
  ttsTask: 'TTSTask',
  inviteCode: 'InviteCode',
  referralRelation: 'ReferralRelation',
  commissionAccount: 'CommissionAccount',
  commissionRecord: 'CommissionRecord',
  withdrawRequest: 'WithdrawRequest',
  activationCode: 'ActivationCode',
  activationCodeUsage: 'ActivationCodeUsage',
  appVersion: 'AppVersion',
  appUpdatePolicy: 'AppUpdatePolicy',
  appUpdateLog: 'AppUpdateLog',
  waveformData: 'WaveformData',
  waveformInteraction: 'WaveformInteraction'
} as const

// ==================== 类型导出（保持原有 API 兼容性）====================

// 重新导出 Drizzle 类型以保持兼容性
export type {
  User,
  UserProfile,
  Chat,
  Message,
  Character,
  MediaGeneration,
  MembershipPlan,
  UserSubscription,
  UserPoints,
  PointsTransaction,
  PointsPackage,
  InviteCode,
  ReferralRelation,
  CommissionAccount,
  CommissionRecord,
  WithdrawRequest,
  ActivationCode,
  ActivationCodeUsage,
  Script,
  ScriptPurchase,
  ScriptUsage,
  WaveformData,
  WaveformInteraction
} from './schema'
