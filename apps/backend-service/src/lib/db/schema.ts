import type { InferSelectModel } from 'drizzle-orm'
import {
  pgTable,
  varchar,
  timestamp,
  json,
  uuid,
  text,
  boolean,
  integer,
  bigint,
  decimal,
  index,
  uniqueIndex
} from 'drizzle-orm/pg-core'
import { sql } from 'drizzle-orm'

// ==================== 用户认证系统 ====================

export const user = pgTable(
  'User',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    email: varchar('email', { length: 64 }).unique(), // 移除 notNull()，改为可选
    phone: varchar('phone', { length: 20 }).unique(), // 新增手机号字段
    supabaseUserId: uuid('supabase_user_id').unique(), // 关联 Supabase Auth 用户ID
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow()
  },
  table => ({
    // 性能优化索引
    supabaseUserIdIdx: index('idx_user_supabase_id').on(table.supabaseUserId),
    emailIdx: index('idx_user_email').on(table.email),
    phoneIdx: index('idx_user_phone').on(table.phone) // 新增手机号索引
  })
)

export type User = InferSelectModel<typeof user>

// 用户扩展信息表
export const userProfile = pgTable(
  'UserProfile',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    userId: uuid('user_id')
      .notNull()
      .references(() => user.id),
    nickname: varchar('nickname', { length: 50 }),
    gender: varchar('gender', { enum: ['male', 'female', 'other'] }),
    avatarUrl: text('avatar_url'), // 使用 Supabase Storage
    bio: text('bio'), // 个人简介
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow()
  },
  table => ({
    userIdIdx: index('idx_userprofile_user_id').on(table.userId)
  })
)

export type UserProfile = InferSelectModel<typeof userProfile>

// ==================== 聊天系统 ====================

export const chat = pgTable(
  'Chat',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    userId: uuid('user_id')
      .notNull()
      .references(() => user.id),
    title: text('title').notNull(),
    visibility: varchar('visibility', { enum: ['public', 'private'] })
      .notNull()
      .default('private'),
    characterId: uuid('character_id'), // 关联角色ID
    backgroundImageUrl: text('background_image_url'), // 聊天背景图URL
    backgroundSceneDescription: text('background_scene_description'), // 生成背景图时使用的场景描述
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow()
  },
  table => ({
    // 性能优化索引
    userIdIdx: index('idx_chat_user_id').on(table.userId),
    createdAtIdx: index('idx_chat_created_at').on(table.createdAt.desc()),
    updatedAtIdx: index('idx_chat_updated_at').on(table.updatedAt.desc()),
    userCreatedIdx: index('idx_chat_user_created').on(table.userId, table.createdAt.desc())
  })
)

export type Chat = InferSelectModel<typeof chat>

// 消息表 - 支持多媒体消息和附件
export const message = pgTable(
  'Message',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    chatId: uuid('chat_id')
      .notNull()
      .references(() => chat.id),
    role: varchar('role', { enum: ['user', 'assistant', 'system'] }).notNull(),
    parts: json('parts').notNull(), // 消息部分：文本、图片、音频等
    attachments: json('attachments').notNull().default('[]'), // 附件列表
    createdAt: timestamp('created_at').notNull().defaultNow()
  },
  table => ({
    // 性能优化索引
    chatIdIdx: index('idx_message_chat_id').on(table.chatId),
    createdAtIdx: index('idx_message_created_at').on(table.createdAt.desc()),
    chatCreatedIdx: index('idx_message_chat_created').on(table.chatId, table.createdAt.desc())
  })
)

export type Message = InferSelectModel<typeof message>

// TTS任务表
export const ttsTask = pgTable(
  'TTSTask',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    userId: uuid('user_id')
      .notNull()
      .references(() => user.id),
    messageId: uuid('message_id').references(() => message.id),
    chatId: uuid('chat_id').references(() => chat.id),
    text: text('text').notNull(),
    voice: varchar('voice', { length: 50 }),
    status: varchar('status', {
      enum: ['pending', 'processing', 'completed', 'failed']
    })
      .notNull()
      .default('pending'),
    audioUrl: text('audio_url'),
    errorMessage: text('error_message'),
    progress: integer('progress').default(0),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow()
  },
  table => ({
    userIdIdx: index('idx_tts_user_id').on(table.userId),
    statusIdx: index('idx_tts_status').on(table.status),
    messageIdIdx: index('idx_tts_message_id').on(table.messageId),
    chatIdIdx: index('idx_tts_chat_id').on(table.chatId),
    createdAtIdx: index('idx_tts_created_at').on(table.createdAt.desc())
  })
)

export type TTSTask = InferSelectModel<typeof ttsTask>

// ==================== 角色系统 ====================

export const character = pgTable(
  'Character',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    userId: uuid('user_id')
      .notNull()
      .references(() => user.id),
    name: varchar('name', { length: 100 }).notNull(),
    description: text('description'), // 角色描述
    relationship: varchar('relationship', { length: 50 }), // 关系：女友、妻子、朋友等
    ethnicity: varchar('ethnicity', { length: 50 }), // 种族
    gender: varchar('gender', { enum: ['male', 'female', 'other'] }),
    age: varchar('age', { length: 20 }), // 年龄范围
    eyeColor: varchar('eye_color', { length: 30 }), // 眼睛颜色
    hairStyle: varchar('hair_style', { length: 50 }), // 发型
    hairColor: varchar('hair_color', { length: 30 }), // 头发颜色
    faceShape: varchar('face_shape', { length: 30 }), // 脸型
    bodyType: varchar('body_type', { length: 30 }), // 体型
    breastSize: varchar('breast_size', { length: 20 }), // 胸部大小
    buttSize: varchar('butt_size', { length: 20 }), // 臀部大小
    personality: text('personality'), // 性格特征
    clothing: text('clothing'), // 服装描述
    voice: varchar('voice', { length: 50 }), // 声音特征（兼容性保留）
    voiceModelId: uuid('voice_model_id').references(() => voiceModel.id), // 关联声音模型
    keywords: text('keywords').notNull(), // 关键词
    prompt: text('prompt').notNull(), // AI 提示词
    imageUrl: text('image_url'), // 角色头像 URL (Supabase Storage)
    category: varchar('category', { length: 50 }), // 角色分类
    isPublic: boolean('is_public').notNull().default(false), // 是否公开
    isActive: boolean('is_active').notNull().default(true), // 是否启用
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow()
  },
  table => ({
    // 性能优化索引
    userIdIdx: index('idx_character_user_id').on(table.userId),
    publicIdx: index('idx_character_public')
      .on(table.isPublic)
      .where(sql`${table.isPublic} = true`),
    categoryIdx: index('idx_character_category').on(table.category),
    createdAtIdx: index('idx_character_created_at').on(table.createdAt.desc()),
    // 组合索引：优化按用户ID查询并按创建时间排序的性能
    userCreatedAtIdx: index('idx_character_user_created_at').on(
      table.userId,
      table.createdAt.desc()
    ),
    publicCategoryIdx: index('idx_character_public_category')
      .on(table.isPublic, table.category)
      .where(sql`${table.isPublic} = true`)
  })
)

export type Character = InferSelectModel<typeof character>

// ==================== 声音模块 ====================

// 声音模型表
export const voiceModel = pgTable(
  'VoiceModel',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    modelId: varchar('model_id', { length: 100 }).notNull().unique(), // TTS服务的模型ID
    name: varchar('name', { length: 50 }).notNull(), // 显示名称
    displayName: varchar('display_name', { length: 100 }).notNull(), // 中文显示名称
    description: text('description'), // 声音描述
    gender: varchar('gender', { enum: ['male', 'female', 'neutral'] }).notNull(),
    language: varchar('language', { length: 10 }).notNull().default('zh-CN'), // 主要语言
    supportedLanguages: json('supported_languages'), // 支持的语言列表
    category: varchar('category', { length: 50 }), // 声音分类
    tags: json('tags'), // 标签数组，如 ['温柔', '成熟', '磁性']
    isActive: boolean('is_active').notNull().default(true),
    isPremium: boolean('is_premium').notNull().default(false), // 是否为付费声音
    sortOrder: integer('sort_order').default(0),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow()
  },
  table => ({
    modelIdIdx: index('idx_voice_model_id').on(table.modelId),
    activeIdx: index('idx_voice_active')
      .on(table.isActive)
      .where(sql`${table.isActive} = true`),
    genderIdx: index('idx_voice_gender').on(table.gender)
  })
)

export type VoiceModel = InferSelectModel<typeof voiceModel>

// 声音示例表
export const voiceSample = pgTable(
  'VoiceSample',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    voiceModelId: uuid('voice_model_id')
      .notNull()
      .references(() => voiceModel.id, { onDelete: 'cascade' }),
    language: varchar('language', { length: 10 }).notNull(), // 语言代码，如 zh-CN, en-US
    sampleText: text('sample_text').notNull(), // 示例文本
    audioUrl: text('audio_url'), // 音频文件URL
    duration: integer('duration'), // 音频时长（秒）
    fileSize: integer('file_size'), // 文件大小（字节）
    isDefault: boolean('is_default').notNull().default(false), // 是否为默认示例
    createdAt: timestamp('created_at').notNull().defaultNow()
  },
  table => ({
    voiceModelIdx: index('idx_voice_sample_model').on(table.voiceModelId),
    languageIdx: index('idx_voice_sample_language').on(table.language),
    defaultIdx: index('idx_voice_sample_default')
      .on(table.isDefault)
      .where(sql`${table.isDefault} = true`)
  })
)

export type VoiceSample = InferSelectModel<typeof voiceSample>

// ==================== 会员系统 ====================

// 会员计划表
export const membershipPlan = pgTable(
  'MembershipPlan',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    name: varchar('name', { length: 50 }).notNull(),
    description: text('description'),
    price: decimal('price', { precision: 10, scale: 2 }).notNull(),
    durationDays: integer('duration_days').notNull(), // 有效期天数
    pointsIncluded: integer('points_included').notNull(), // 包含点数
    features: json('features'), // 功能权限 JSON
    // features 结构示例：
    // {
    //   "maxCharacters": 5,           // 最大角色数目
    //   "canCreatePublicCharacters": true,
    //   "canUseCustomVoices": true,
    //   "canAccessPremiumTemplates": true,
    //   "maxImageGenerations": 100
    // }
    isActive: boolean('is_active').notNull().default(true),
    sortOrder: integer('sort_order').default(0), // 排序
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow()
  },
  table => ({
    activeIdx: index('idx_membership_active')
      .on(table.isActive)
      .where(sql`${table.isActive} = true`)
  })
)

export type MembershipPlan = InferSelectModel<typeof membershipPlan>

// 用户订阅表
export const userSubscription = pgTable(
  'UserSubscription',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    userId: uuid('user_id')
      .notNull()
      .references(() => user.id),
    planId: uuid('plan_id')
      .notNull()
      .references(() => membershipPlan.id),
    startDate: timestamp('start_date').notNull(),
    endDate: timestamp('end_date').notNull(),
    status: varchar('status', {
      enum: ['active', 'expired', 'cancelled', 'pending']
    })
      .notNull()
      .default('pending'),
    autoRenew: boolean('auto_renew').notNull().default(false),
    paymentId: text('payment_id'), // 支付系统交易ID
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow()
  },
  table => ({
    userIdIdx: index('idx_subscription_user_id').on(table.userId),
    statusIdx: index('idx_subscription_status').on(table.status),
    expiresIdx: index('idx_subscription_expires').on(table.endDate)
  })
)

export type UserSubscription = InferSelectModel<typeof userSubscription>

// 订阅历史记录表
export const subscriptionHistory = pgTable(
  'SubscriptionHistory',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    userId: uuid('user_id')
      .notNull()
      .references(() => user.id),
    planId: uuid('plan_id')
      .notNull()
      .references(() => membershipPlan.id),
    subscriptionId: uuid('subscription_id').references(() => userSubscription.id),
    action: varchar('action', {
      enum: ['subscribe', 'renew', 'cancel', 'expire', 'upgrade', 'downgrade']
    }).notNull(),
    amount: decimal('amount', { precision: 10, scale: 2 }),
    pointsGranted: integer('points_granted'),
    paymentId: text('payment_id'),
    metadata: json('metadata'), // 额外信息
    createdAt: timestamp('created_at').notNull().defaultNow()
  },
  table => ({
    userIdx: index('idx_subscription_history_user').on(table.userId),
    createdIdx: index('idx_subscription_history_created').on(table.createdAt.desc())
  })
)

export type SubscriptionHistory = InferSelectModel<typeof subscriptionHistory>

// ==================== 积分系统 ====================

// 积分套餐表
export const pointsPackage = pgTable(
  'PointsPackage',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    name: varchar('name', { length: 50 }).notNull(),
    description: text('description'),
    points: integer('points').notNull(),
    price: decimal('price', { precision: 10, scale: 2 }).notNull(),
    bonusPoints: integer('bonus_points').default(0),
    isActive: boolean('is_active').notNull().default(true),
    sortOrder: integer('sort_order').default(0),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow()
  },
  table => ({
    activeIdx: index('idx_points_package_active')
      .on(table.isActive)
      .where(sql`${table.isActive} = true`)
  })
)

export type PointsPackage = InferSelectModel<typeof pointsPackage>

// 用户积分表（扩展支持周期管理）
export const userPoints = pgTable(
  'UserPoints',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    userId: uuid('user_id')
      .notNull()
      .references(() => user.id)
      .unique(), // 每个用户只有一条记录
    totalPoints: integer('total_points').notNull().default(0), // 总获得点数
    usedPoints: integer('used_points').notNull().default(0), // 已使用点数
    availablePoints: integer('available_points').notNull().default(0), // 可用点数

    // 积分周期管理字段 🆕
    cycleStartDate: timestamp('cycle_start_date'), // 当前周期开始时间
    cycleEndDate: timestamp('cycle_end_date'), // 当前周期结束时间
    membershipLevel: varchar('membership_level', { length: 20 }), // 当前会员等级
    monthlyAllocation: integer('monthly_allocation').default(0), // 月度分配积分
    cycleConsumed: integer('cycle_consumed').default(0), // 本周期已消费
    cycleGifted: integer('cycle_gifted').default(0), // 本周期已赠送（预留）
    cycleReceived: integer('cycle_received').default(0), // 本周期已接收（预留）
    lastCycleCheck: timestamp('last_cycle_check'), // 上次周期检查时间

    lastUpdated: timestamp('last_updated').notNull().defaultNow()
  },
  table => ({
    userIdIdx: index('idx_user_points_user_id').on(table.userId),
    cycleEndIdx: index('idx_user_points_cycle_end').on(table.cycleEndDate)
  })
)

export type UserPoints = InferSelectModel<typeof userPoints>

// 积分交易记录表
export const pointsTransaction = pgTable(
  'PointsTransaction',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    userId: uuid('user_id')
      .notNull()
      .references(() => user.id),
    transactionType: varchar('transaction_type', {
      enum: [
        'earn',
        'spend',
        'refund',
        'bonus',
        'cycle_grant',
        'cycle_reset',
        'gift_sent',
        'gift_received',
        'upgrade_bonus'
      ]
    }).notNull(),
    amount: integer('amount').notNull(), // 点数数量（正数为获得，负数为消费）
    source: varchar('source', {
      enum: [
        'subscription',
        'purchase',
        'generation',
        'refund',
        'bonus',
        'admin',
        'cycle_grant',
        'cycle_reset',
        'gift',
        'upgrade'
      ]
    }).notNull(),
    sourceId: uuid('source_id'), // 来源ID
    description: text('description'),
    balanceAfter: integer('balance_after').notNull(), // 交易后余额
    metadata: json('metadata'), // 额外信息
    createdAt: timestamp('created_at').notNull().defaultNow()
  },
  table => ({
    userIdx: index('idx_points_transaction_user').on(table.userId),
    createdIdx: index('idx_points_transaction_created').on(table.createdAt.desc()),
    typeIdx: index('idx_points_transaction_type').on(table.transactionType),
    userCreatedIdx: index('idx_points_transaction_user_created').on(
      table.userId,
      table.createdAt.desc()
    )
  })
)

export type PointsTransaction = InferSelectModel<typeof pointsTransaction>

// ==================== 模板系统 ====================

// 模板表
export const template = pgTable(
  'Template',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    name: varchar('name', { length: 100 }).notNull(),
    description: text('description'),
    category: varchar('category', { length: 50 }),
    previewImage: text('preview_image'), // 预览图片 URL (Supabase Storage)
    prompt: text('prompt').notNull(),
    negativePrompt: text('negative_prompt'),
    pointsCost: integer('points_cost').notNull().default(1),
    isPremium: boolean('is_premium').notNull().default(false), // 会员专属
    isPublic: boolean('is_public').notNull().default(true), // 是否公开
    isActive: boolean('is_active').notNull().default(true),
    tags: json('tags'), // 标签数组
    settings: json('settings'), // 生成参数
    createdBy: uuid('created_by').references(() => user.id),
    usageCount: integer('usage_count').notNull().default(0), // 使用次数
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow()
  },
  table => ({
    publicIdx: index('idx_template_public')
      .on(table.isPublic)
      .where(sql`${table.isPublic} = true`),
    categoryIdx: index('idx_template_category').on(table.category),
    premiumIdx: index('idx_template_premium').on(table.isPremium),
    publicCategoryIdx: index('idx_template_public_category')
      .on(table.isPublic, table.category)
      .where(sql`${table.isPublic} = true`)
  })
)

export type Template = InferSelectModel<typeof template>

// 生成历史记录表
// 媒体生成记录表 - 替代原来的 GenerationHistory
export const mediaGeneration = pgTable(
  'MediaGeneration',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    userId: uuid('user_id')
      .notNull()
      .references(() => user.id),
    characterId: uuid('character_id').references(() => character.id), // 关联角色
    chatId: uuid('chat_id').references(() => chat.id), // 关联聊天会话
    messageId: uuid('message_id'), // 关联消息（如果是多模态对话）

    // 媒体类型和生成类型
    mediaType: varchar('media_type', { enum: ['image', 'video', 'audio'] }).notNull(), // 媒体类型
    generationType: varchar('generation_type', {
      enum: ['multimodal_chat', 'standalone', 'template_based']
    }).notNull(), // 生成类型

    // 生成内容
    prompt: text('prompt'), // 生成提示词
    negativePrompt: text('negative_prompt'), // 负面提示词
    inputImageUrl: text('input_image_url'), // 输入图片URL（如角色头像）
    outputUrls: json('output_urls'), // 生成的媒体URL数组

    // 生成状态和结果
    status: varchar('status', {
      enum: ['pending', 'processing', 'completed', 'failed', 'cancelled']
    })
      .notNull()
      .default('pending'),
    errorMessage: text('error_message'),

    // 积分和时间
    pointsUsed: integer('points_used').notNull().default(0),
    generationTime: integer('generation_time'), // 生成耗时（秒）
    completedAt: timestamp('completed_at'),

    // 元数据
    metadata: json('metadata'), // 生成参数、模板信息等

    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow()
  },
  table => ({
    // 性能优化索引
    userIdIdx: index('idx_media_gen_user_id').on(table.userId),
    characterIdIdx: index('idx_media_gen_character_id').on(table.characterId),
    chatIdIdx: index('idx_media_gen_chat_id').on(table.chatId),
    mediaTypeIdx: index('idx_media_gen_media_type').on(table.mediaType),
    generationTypeIdx: index('idx_media_gen_generation_type').on(table.generationType),
    statusIdx: index('idx_media_gen_status').on(table.status),
    createdAtIdx: index('idx_media_gen_created_at').on(table.createdAt.desc()),
    userCharacterIdx: index('idx_media_gen_user_character').on(
      table.userId,
      table.characterId,
      table.createdAt.desc()
    ),
    userMediaTypeIdx: index('idx_media_gen_user_media_type').on(
      table.userId,
      table.mediaType,
      table.createdAt.desc()
    )
  })
)

export type MediaGeneration = InferSelectModel<typeof mediaGeneration>

// ==================== 音效系统 ====================

// 音效分类表
export const audioCategory = pgTable(
  'AudioCategory',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    name: varchar('name', { length: 50 }).notNull().unique(), // 分类名称
    displayName: varchar('display_name', { length: 100 }), // 显示名称
    description: text('description'),
    parentId: uuid('parent_id'), // 父分类ID，支持层级
    sortOrder: integer('sort_order').default(0),
    isActive: boolean('is_active').notNull().default(true),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow()
  },
  table => ({
    activeIdx: index('idx_audio_category_active')
      .on(table.isActive)
      .where(sql`${table.isActive} = true`),
    parentIdx: index('idx_audio_category_parent').on(table.parentId)
  })
)

export type AudioCategory = InferSelectModel<typeof audioCategory>

// 音效表
export const audioEffect = pgTable(
  'AudioEffect',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(), // 改为 UUID
    name: varchar('name', { length: 100 }), // 音效名称
    description: text('description'), // 音效描述
    categoryId: uuid('category_id').references(() => audioCategory.id), // 关联分类
    tags: json('tags').notNull().default('[]'), // 标签数组
    url: text('url').notNull(), // 音效文件 URL (Supabase Storage)
    duration: decimal('duration', { precision: 10, scale: 2 }).notNull(), // 时长（秒）
    avgPitch: decimal('avg_pitch', { precision: 10, scale: 2 }), // 平均音调
    avgLoudness: decimal('avg_loudness', { precision: 10, scale: 2 }), // 平均响度
    energyVariation: decimal('energy_variation', { precision: 10, scale: 4 }), // 能量变化
    isPublic: boolean('is_public').notNull().default(true), // 是否公开
    isActive: boolean('is_active').notNull().default(true),
    usageCount: integer('usage_count').notNull().default(0), // 使用次数
    createdBy: uuid('created_by').references(() => user.id), // 创建者
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow()
  },
  table => ({
    categoryIdx: index('idx_audio_category').on(table.categoryId),
    publicIdx: index('idx_audio_public')
      .on(table.isPublic)
      .where(sql`${table.isPublic} = true`),
    activeIdx: index('idx_audio_active')
      .on(table.isActive)
      .where(sql`${table.isActive} = true`),
    createdByIdx: index('idx_audio_created_by').on(table.createdBy)
  })
)

export type AudioEffect = InferSelectModel<typeof audioEffect>

// ==================== 系统配置表 ====================

// 系统配置表 - 用于存储系统级别的配置
export const systemConfig = pgTable('SystemConfig', {
  key: varchar('key', { length: 50 }).primaryKey(),
  value: json('value').notNull(),
  description: text('description'),
  isPublic: boolean('is_public').notNull().default(false), // 是否可公开访问
  updatedAt: timestamp('updated_at').notNull().defaultNow()
})

export type SystemConfig = InferSelectModel<typeof systemConfig>

// ==================== 用户会话管理表 ====================

// 用户会话表 - 用于跟踪用户活动
export const userSession = pgTable(
  'UserSession',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    userId: uuid('user_id')
      .notNull()
      .references(() => user.id),
    deviceInfo: json('device_info'), // 设备信息
    ipAddress: varchar('ip_address', { length: 45 }), // IP地址（支持IPv6）
    userAgent: text('user_agent'), // 用户代理
    lastActive: timestamp('last_active').notNull().defaultNow(),
    isActive: boolean('is_active').notNull().default(true),
    createdAt: timestamp('created_at').notNull().defaultNow()
  },
  table => ({
    userIdIdx: index('idx_user_session_user_id').on(table.userId),
    lastActiveIdx: index('idx_user_session_last_active').on(table.lastActive.desc()),
    activeIdx: index('idx_user_session_active')
      .on(table.isActive)
      .where(sql`${table.isActive} = true`)
  })
)

export type UserSession = InferSelectModel<typeof userSession>

// ==================== API 使用统计表 ====================

// API 使用统计表 - 用于监控和分析
export const apiUsage = pgTable(
  'ApiUsage',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    userId: uuid('user_id').references(() => user.id), // 可为空，支持匿名请求
    endpoint: varchar('endpoint', { length: 100 }).notNull(),
    method: varchar('method', { length: 10 }).notNull(),
    statusCode: integer('status_code').notNull(),
    responseTime: integer('response_time'), // 响应时间（毫秒）
    ipAddress: varchar('ip_address', { length: 45 }),
    userAgent: text('user_agent'),
    errorMessage: text('error_message'), // 错误信息
    metadata: json('metadata'), // 额外信息
    createdAt: timestamp('created_at').notNull().defaultNow()
  },
  table => ({
    userIdIdx: index('idx_api_usage_user_id').on(table.userId),
    endpointIdx: index('idx_api_usage_endpoint').on(table.endpoint),
    createdAtIdx: index('idx_api_usage_created_at').on(table.createdAt.desc()),
    statusIdx: index('idx_api_usage_status').on(table.statusCode)
  })
)

export type ApiUsage = InferSelectModel<typeof apiUsage>

// ==================== 剧本系统 ====================

// 剧本表
export const script = pgTable(
  'Script',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    title: varchar('title', { length: 100 }).notNull(), // 剧本标题
    description: text('description').notNull(), // 剧本描述
    coverImage: text('cover_image').notNull(), // 封面图片 URL
    duration: varchar('duration', { length: 20 }).notNull(), // 时长（如：7分钟）
    tags: json('tags').notNull().default('[]'), // 标签数组
    category: varchar('category', { length: 50 }), // 剧本分类
    content: json('content'), // 剧本详细内容（阶段、对话、图片等）
    audioUrl: text('audio_url'), // 音频文件URL
    totalDuration: integer('total_duration'), // 总时长（秒）
    stageCount: integer('stage_count'), // 阶段数量
    isPublic: boolean('is_public').notNull().default(true), // 是否公开
    isActive: boolean('is_active').notNull().default(true), // 是否启用
    isPremium: boolean('is_premium').notNull().default(false), // 是否会员专属
    pointsCost: integer('points_cost').notNull().default(0), // 购买所需积分
    usageCount: integer('usage_count').notNull().default(0), // 使用次数
    rating: decimal('rating', { precision: 3, scale: 2 }).default('0'), // 评分
    ratingCount: integer('rating_count').notNull().default(0), // 评分人数
    createdBy: uuid('created_by').references(() => user.id), // 创建者
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow()
  },
  table => ({
    // 性能优化索引
    publicIdx: index('idx_script_public')
      .on(table.isPublic)
      .where(sql`${table.isPublic} = true`),
    activeIdx: index('idx_script_active')
      .on(table.isActive)
      .where(sql`${table.isActive} = true`),
    categoryIdx: index('idx_script_category').on(table.category),
    premiumIdx: index('idx_script_premium').on(table.isPremium),
    ratingIdx: index('idx_script_rating').on(table.rating.desc()),
    usageIdx: index('idx_script_usage').on(table.usageCount.desc()),
    createdAtIdx: index('idx_script_created_at').on(table.createdAt.desc()),
    publicCategoryIdx: index('idx_script_public_category')
      .on(table.isPublic, table.category)
      .where(sql`${table.isPublic} = true AND ${table.isActive} = true`)
  })
)

export type Script = InferSelectModel<typeof script>

// 剧本使用记录表
export const scriptUsage = pgTable(
  'ScriptUsage',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    userId: uuid('user_id')
      .notNull()
      .references(() => user.id),
    scriptId: uuid('script_id')
      .notNull()
      .references(() => script.id),
    chatId: uuid('chat_id').references(() => chat.id), // 关联的聊天会话
    duration: integer('duration'), // 使用时长（秒）
    rating: integer('rating'), // 用户评分 1-5
    feedback: text('feedback'), // 用户反馈
    createdAt: timestamp('created_at').notNull().defaultNow()
  },
  table => ({
    userIdIdx: index('idx_script_usage_user_id').on(table.userId),
    scriptIdIdx: index('idx_script_usage_script_id').on(table.scriptId),
    chatIdIdx: index('idx_script_usage_chat_id').on(table.chatId),
    createdAtIdx: index('idx_script_usage_created_at').on(table.createdAt.desc()),
    userScriptIdx: index('idx_script_usage_user_script').on(table.userId, table.scriptId)
  })
)

export type ScriptUsage = InferSelectModel<typeof scriptUsage>

// 剧本购买记录表
export const scriptPurchase = pgTable(
  'ScriptPurchase',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    userId: uuid('user_id')
      .notNull()
      .references(() => user.id),
    scriptId: uuid('script_id')
      .notNull()
      .references(() => script.id),
    pointsCost: integer('points_cost').notNull(), // 购买时的积分成本
    transactionId: uuid('transaction_id').references(() => pointsTransaction.id), // 关联的积分交易记录
    status: varchar('status', {
      enum: ['completed', 'refunded']
    })
      .notNull()
      .default('completed'), // 购买状态
    expiresAt: timestamp('expires_at').notNull(), // 过期时间（一年有效期）
    isDownloaded: boolean('is_downloaded').notNull().default(false), // 是否已下载内容
    downloadedAt: timestamp('downloaded_at'), // 下载时间
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow()
  },
  table => ({
    // 性能优化索引
    userIdIdx: index('idx_script_purchase_user_id').on(table.userId),
    scriptIdIdx: index('idx_script_purchase_script_id').on(table.scriptId),
    statusIdx: index('idx_script_purchase_status').on(table.status),
    expiresAtIdx: index('idx_script_purchase_expires_at').on(table.expiresAt),
    createdAtIdx: index('idx_script_purchase_created_at').on(table.createdAt.desc()),
    userScriptIdx: index('idx_script_purchase_user_script').on(table.userId, table.scriptId),
    // 防止重复购买的唯一索引
    uniqueUserScriptIdx: uniqueIndex('idx_script_purchase_unique_user_script').on(
      table.userId,
      table.scriptId
    )
  })
)

export type ScriptPurchase = InferSelectModel<typeof scriptPurchase>

// ==================== 设备系统 (4层架构) ====================

// 1. 指令集表 (Command Sets) - 最基础的蓝牙控制指令
export const deviceCommandSet = pgTable(
  'DeviceCommandSet',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    name: varchar('name', { length: 100 }).notNull(), // 指令集名称
    description: text('description'), // 指令集描述
    command: varchar('command', { length: 200 }).notNull(), // 蓝牙控制指令
    broadcast: varchar('broadcast', { length: 200 }), // 广播指令（如果需要）
    isActive: boolean('is_active').notNull().default(true), // 是否启用
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow()
  },
  table => ({
    nameIdx: index('idx_device_command_set_name').on(table.name),
    activeIdx: index('idx_device_command_set_active').on(table.isActive)
  })
)

export type DeviceCommandSet = InferSelectModel<typeof deviceCommandSet>

// 2. 设备功能表 (Device Functions) - 功能类型和强度等级
export const deviceFunction = pgTable(
  'DeviceFunction',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    name: varchar('name', { length: 50 }).notNull(), // 功能名称，如：震动、抽插、吮吸
    key: varchar('key', { length: 30 }).notNull().unique(), // 功能键，如：vibrate、thrust、suction
    description: text('description'), // 功能描述
    maxIntensity: integer('max_intensity').notNull().default(3), // 最大强度等级
    isActive: boolean('is_active').notNull().default(true), // 是否启用
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow()
  },
  table => ({
    keyIdx: index('idx_device_function_key').on(table.key),
    activeIdx: index('idx_device_function_active').on(table.isActive)
  })
)

export type DeviceFunction = InferSelectModel<typeof deviceFunction>

// 功能强度指令映射表 - 连接功能和指令集
export const deviceFunctionCommand = pgTable(
  'DeviceFunctionCommand',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    functionId: uuid('function_id')
      .notNull()
      .references(() => deviceFunction.id, { onDelete: 'cascade' }),
    commandSetId: uuid('command_set_id')
      .notNull()
      .references(() => deviceCommandSet.id, { onDelete: 'cascade' }),
    intensity: integer('intensity').notNull(), // 强度等级，0表示停止，1-N表示不同强度
    description: text('description'), // 该强度等级的描述
    createdAt: timestamp('created_at').notNull().defaultNow()
  },
  table => ({
    functionIdIdx: index('idx_device_function_command_function_id').on(table.functionId),
    commandSetIdIdx: index('idx_device_function_command_command_set_id').on(table.commandSetId),
    functionIntensityIdx: index('idx_device_function_command_function_intensity').on(
      table.functionId,
      table.intensity
    )
  })
)

export type DeviceFunctionCommand = InferSelectModel<typeof deviceFunctionCommand>

// 3. 设备模式表 (Device Modes) - 绑定到指令集的模式
export const deviceMode = pgTable(
  'DeviceMode',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    name: varchar('name', { length: 100 }).notNull(), // 模式名称，如：连续模式、间歇模式、波浪模式
    description: text('description'), // 模式描述
    commandSetId: uuid('command_set_id')
      .notNull()
      .references(() => deviceCommandSet.id), // 绑定的指令集
    sort: integer('sort').notNull().default(0), // 排序字段，数值越小越靠前
    isActive: boolean('is_active').notNull().default(true), // 是否启用
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow()
  },
  table => ({
    nameIdx: index('idx_device_mode_name').on(table.name),
    activeIdx: index('idx_device_mode_active').on(table.isActive),
    commandSetIdx: index('idx_device_mode_command_set').on(table.commandSetId),
    sortIdx: index('idx_device_mode_sort').on(table.sort)
  })
)

export type DeviceMode = InferSelectModel<typeof deviceMode>

// 4. 设备表 (Devices) - 最终的设备实体
export const device = pgTable(
  'Device',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    userId: uuid('user_id').references(() => user.id), // 允许为空，用于系统预设设备
    deviceCode: varchar('device_code', { length: 20 }).notNull().unique(), // 设备码
    name: varchar('name', { length: 100 }).notNull(), // 设备名称
    pic: text('pic'), // 设备图片URL
    brand: varchar('brand', { length: 50 }), // 品牌
    model: varchar('model', { length: 50 }), // 型号
    category: varchar('category', { length: 30 }), // 设备分类
    description: text('description'), // 设备描述
    isActive: boolean('is_active').notNull().default(true), // 是否启用
    lastConnectedAt: timestamp('last_connected_at'), // 最后连接时间
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow()
  },
  table => ({
    userIdIdx: index('idx_device_user_id').on(table.userId),
    deviceCodeIdx: index('idx_device_code').on(table.deviceCode),
    categoryIdx: index('idx_device_category').on(table.category),
    activeIdx: index('idx_device_active').on(table.isActive),
    userActiveIdx: index('idx_device_user_active').on(table.userId, table.isActive)
  })
)

export type Device = InferSelectModel<typeof device>

// 设备支持的功能关联表
export const deviceSupportedFunction = pgTable(
  'DeviceSupportedFunction',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    deviceId: uuid('device_id')
      .notNull()
      .references(() => device.id, { onDelete: 'cascade' }),
    functionId: uuid('function_id')
      .notNull()
      .references(() => deviceFunction.id, { onDelete: 'cascade' }),
    isActive: boolean('is_active').notNull().default(true), // 是否启用该功能
    createdAt: timestamp('created_at').notNull().defaultNow()
  },
  table => ({
    deviceIdIdx: index('idx_device_supported_function_device_id').on(table.deviceId),
    functionIdIdx: index('idx_device_supported_function_function_id').on(table.functionId),
    deviceFunctionIdx: index('idx_device_supported_function_device_function').on(
      table.deviceId,
      table.functionId
    )
  })
)

export type DeviceSupportedFunction = InferSelectModel<typeof deviceSupportedFunction>

// 设备支持的模式关联表
export const deviceSupportedMode = pgTable(
  'DeviceSupportedMode',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    deviceId: uuid('device_id')
      .notNull()
      .references(() => device.id, { onDelete: 'cascade' }),
    modeId: uuid('mode_id')
      .notNull()
      .references(() => deviceMode.id, { onDelete: 'cascade' }),
    isActive: boolean('is_active').notNull().default(true), // 是否启用该模式
    createdAt: timestamp('created_at').notNull().defaultNow()
  },
  table => ({
    deviceIdIdx: index('idx_device_supported_mode_device_id').on(table.deviceId),
    modeIdIdx: index('idx_device_supported_mode_mode_id').on(table.modeId),
    deviceModeIdx: index('idx_device_supported_mode_device_mode').on(table.deviceId, table.modeId)
  })
)

export type DeviceSupportedMode = InferSelectModel<typeof deviceSupportedMode>

// 设备连接记录表
export const deviceConnection = pgTable(
  'DeviceConnection',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    userId: uuid('user_id')
      .notNull()
      .references(() => user.id),
    deviceId: uuid('device_id')
      .notNull()
      .references(() => device.id),
    sessionId: varchar('session_id', { length: 100 }), // 会话ID
    status: varchar('status', {
      enum: ['connected', 'disconnected', 'error']
    })
      .notNull()
      .default('connected'),
    connectedAt: timestamp('connected_at').notNull().defaultNow(),
    disconnectedAt: timestamp('disconnected_at'),
    errorMessage: text('error_message'), // 错误信息
    metadata: json('metadata').default('{}') // 连接元数据
  },
  table => ({
    // 性能优化索引
    userIdIdx: index('idx_device_connection_user_id').on(table.userId),
    deviceIdIdx: index('idx_device_connection_device_id').on(table.deviceId),
    statusIdx: index('idx_device_connection_status').on(table.status),
    connectedAtIdx: index('idx_device_connection_connected_at').on(table.connectedAt.desc()),
    userDeviceIdx: index('idx_device_connection_user_device').on(table.userId, table.deviceId)
  })
)

export type DeviceConnection = InferSelectModel<typeof deviceConnection>

// 设备使用记录表
export const deviceUsage = pgTable(
  'DeviceUsage',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    userId: uuid('user_id')
      .notNull()
      .references(() => user.id),
    deviceId: uuid('device_id')
      .notNull()
      .references(() => device.id),
    scriptId: uuid('script_id').references(() => script.id), // 关联剧本
    sessionId: varchar('session_id', { length: 100 }), // 会话ID
    functionKey: varchar('function_key', { length: 30 }).notNull(), // 使用的功能
    intensity: integer('intensity').notNull(), // 使用的强度
    duration: integer('duration'), // 使用时长(秒)
    startedAt: timestamp('started_at').notNull().defaultNow(),
    endedAt: timestamp('ended_at'),
    metadata: json('metadata').default('{}') // 使用元数据
  },
  table => ({
    // 性能优化索引
    userIdIdx: index('idx_device_usage_user_id').on(table.userId),
    deviceIdIdx: index('idx_device_usage_device_id').on(table.deviceId),
    scriptIdIdx: index('idx_device_usage_script_id').on(table.scriptId),
    startedAtIdx: index('idx_device_usage_started_at').on(table.startedAt.desc()),
    userDeviceIdx: index('idx_device_usage_user_device').on(table.userId, table.deviceId)
  })
)

export type DeviceUsage = InferSelectModel<typeof deviceUsage>

// ==================== 支付订单系统 ====================

// 支付订单表
export const paymentOrder = pgTable(
  'PaymentOrder',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    userId: uuid('user_id')
      .notNull()
      .references(() => user.id),
    planId: uuid('plan_id').references(() => membershipPlan.id),
    pointsPackageId: uuid('points_package_id').references(() => pointsPackage.id),

    // 订单信息
    orderNo: varchar('order_no', { length: 64 }).notNull().unique(), // 我们的订单号
    externalOrderId: varchar('external_order_id', { length: 128 }), // 支付平台订单号 (支付宝trade_no/微信transaction_id)

    // 支付信息
    amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
    currency: varchar('currency', { length: 3 }).notNull().default('CNY'),
    paymentMethod: varchar('payment_method', { length: 20 }), // 'alipay', 'wechat'

    // 订单状态
    status: varchar('status', {
      enum: ['pending', 'paid', 'failed', 'cancelled', 'expired']
    })
      .notNull()
      .default('pending'),

    // 业务信息
    description: text('description').notNull(),
    isUpgrade: boolean('is_upgrade').notNull().default(false), // 是否升级订单
    originalAmount: decimal('original_amount', { precision: 10, scale: 2 }), // 原价(升级时显示节省金额)
    currentSubscriptionId: uuid('current_subscription_id'), // 当前订阅ID(升级时用于取消)

    // 回调信息
    callbackUrl: text('callback_url'),
    returnUrl: text('return_url'),
    notifyUrl: text('notify_url'),

    // 时间戳
    createdAt: timestamp('created_at').notNull().defaultNow(),
    paidAt: timestamp('paid_at'),
    expiresAt: timestamp('expires_at'), // 订单过期时间(30分钟)

    // 元数据
    metadata: json('metadata').default('{}')
  },
  table => ({
    // 性能优化索引
    userIdIdx: index('idx_payment_order_user_id').on(table.userId),
    orderNoIdx: index('idx_payment_order_order_no').on(table.orderNo),
    statusIdx: index('idx_payment_order_status').on(table.status),
    externalOrderIdIdx: index('idx_payment_order_external_id').on(table.externalOrderId),
    expiresAtIdx: index('idx_payment_order_expires_at').on(table.expiresAt),
    paymentMethodIdx: index('idx_payment_order_payment_method').on(table.paymentMethod),
    createdAtIdx: index('idx_payment_order_created_at').on(table.createdAt.desc()),
    userCreatedIdx: index('idx_payment_order_user_created').on(table.userId, table.createdAt.desc())
  })
)

export type PaymentOrder = InferSelectModel<typeof paymentOrder>

// ==================== 邀请码营销系统 ====================

// 邀请码表
export const inviteCode = pgTable(
  'InviteCode',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    userId: uuid('user_id')
      .notNull()
      .references(() => user.id),
    code: varchar('code', { length: 20 }).notNull().unique(), // 邀请码
    maxUses: integer('max_uses'), // 最大使用次数，NULL表示无限制
    usedCount: integer('used_count').notNull().default(0), // 已使用次数
    expiresAt: timestamp('expires_at'), // 过期时间，NULL表示永不过期
    isActive: boolean('is_active').notNull().default(true), // 是否启用
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow()
  },
  table => ({
    userIdIdx: index('idx_invite_code_user_id').on(table.userId),
    codeIdx: index('idx_invite_code_code').on(table.code),
    activeIdx: index('idx_invite_code_active').on(table.isActive),
    expiresAtIdx: index('idx_invite_code_expires_at').on(table.expiresAt)
  })
)

export type InviteCode = InferSelectModel<typeof inviteCode>

// 推荐关系表
export const referralRelation = pgTable(
  'ReferralRelation',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    inviterId: uuid('inviter_id')
      .notNull()
      .references(() => user.id), // 邀请人
    inviteeId: uuid('invitee_id')
      .notNull()
      .references(() => user.id), // 被邀请人
    inviteCodeId: uuid('invite_code_id')
      .notNull()
      .references(() => inviteCode.id), // 使用的邀请码
    createdAt: timestamp('created_at').notNull().defaultNow()
  },
  table => ({
    inviterIdIdx: index('idx_referral_relation_inviter_id').on(table.inviterId),
    inviteeIdIdx: index('idx_referral_relation_invitee_id').on(table.inviteeId),
    inviteCodeIdIdx: index('idx_referral_relation_invite_code_id').on(table.inviteCodeId),
    // 确保一个用户只能被邀请一次
    inviteeUniqueIdx: uniqueIndex('idx_referral_relation_invitee_unique').on(table.inviteeId)
  })
)

export type ReferralRelation = InferSelectModel<typeof referralRelation>

// 用户佣金账户表
export const commissionAccount = pgTable(
  'CommissionAccount',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    userId: uuid('user_id')
      .notNull()
      .references(() => user.id)
      .unique(), // 一个用户只能有一个佣金账户
    totalEarned: decimal('total_earned', { precision: 10, scale: 2 }).notNull().default('0'), // 累计赚取
    availableBalance: decimal('available_balance', { precision: 10, scale: 2 })
      .notNull()
      .default('0'), // 可提现余额
    frozenBalance: decimal('frozen_balance', { precision: 10, scale: 2 }).notNull().default('0'), // 冻结余额
    totalWithdrawn: decimal('total_withdrawn', { precision: 10, scale: 2 }).notNull().default('0'), // 累计提现
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow()
  },
  table => ({
    userIdIdx: index('idx_commission_account_user_id').on(table.userId)
  })
)

export type CommissionAccount = InferSelectModel<typeof commissionAccount>

// 佣金记录表
export const commissionRecord = pgTable(
  'CommissionRecord',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    inviterId: uuid('inviter_id')
      .notNull()
      .references(() => user.id), // 邀请人（获得佣金的人）
    inviteeId: uuid('invitee_id')
      .notNull()
      .references(() => user.id), // 被邀请人（产生消费的人）
    orderId: uuid('order_id')
      .notNull()
      .references(() => paymentOrder.id), // 关联的支付订单
    commissionAmount: decimal('commission_amount', { precision: 10, scale: 2 }).notNull(), // 佣金金额
    sourceType: varchar('source_type', {
      enum: ['membership', 'points_package']
    }).notNull(), // 佣金来源类型
    sourceAmount: decimal('source_amount', { precision: 10, scale: 2 }).notNull(), // 原始消费金额
    commissionRate: decimal('commission_rate', { precision: 5, scale: 4 }).notNull(), // 佣金比例
    status: varchar('status', {
      enum: ['pending', 'settled', 'cancelled']
    })
      .notNull()
      .default('pending'), // 佣金状态
    settledAt: timestamp('settled_at'), // 结算时间
    createdAt: timestamp('created_at').notNull().defaultNow()
  },
  table => ({
    inviterIdIdx: index('idx_commission_record_inviter_id').on(table.inviterId),
    inviteeIdIdx: index('idx_commission_record_invitee_id').on(table.inviteeId),
    orderIdIdx: index('idx_commission_record_order_id').on(table.orderId),
    statusIdx: index('idx_commission_record_status').on(table.status),
    createdAtIdx: index('idx_commission_record_created_at').on(table.createdAt.desc())
  })
)

export type CommissionRecord = InferSelectModel<typeof commissionRecord>

// 提现申请表
export const withdrawRequest = pgTable(
  'WithdrawRequest',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    userId: uuid('user_id')
      .notNull()
      .references(() => user.id), // 申请人
    amount: decimal('amount', { precision: 10, scale: 2 }).notNull(), // 申请提现金额
    feeAmount: decimal('fee_amount', { precision: 10, scale: 2 }).notNull(), // 手续费
    actualAmount: decimal('actual_amount', { precision: 10, scale: 2 }).notNull(), // 实际到账金额
    status: varchar('status', {
      enum: ['pending', 'approved', 'rejected', 'completed']
    })
      .notNull()
      .default('pending'), // 申请状态
    bankInfo: json('bank_info'), // 银行卡信息
    adminNote: text('admin_note'), // 管理员备注
    processedBy: uuid('processed_by').references(() => user.id), // 处理人
    processedAt: timestamp('processed_at'), // 处理时间
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow()
  },
  table => ({
    userIdIdx: index('idx_withdraw_request_user_id').on(table.userId),
    statusIdx: index('idx_withdraw_request_status').on(table.status),
    processedByIdx: index('idx_withdraw_request_processed_by').on(table.processedBy),
    createdAtIdx: index('idx_withdraw_request_created_at').on(table.createdAt.desc())
  })
)

export type WithdrawRequest = InferSelectModel<typeof withdrawRequest>

// ==================== 激活码系统 ====================

// 激活码表
export const activationCode = pgTable(
  'ActivationCode',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    code: varchar('code', { length: 32 }).notNull().unique(), // 激活码字符串
    type: varchar('type', {
      enum: ['membership', 'points']
    }).notNull(), // 激活码类型

    // 关联的产品
    membershipPlanId: uuid('membership_plan_id').references(() => membershipPlan.id), // 会员套餐ID
    pointsPackageId: uuid('points_package_id').references(() => pointsPackage.id), // 积分包ID

    // 使用限制（简化为一次性使用）
    isUsed: boolean('is_used').notNull().default(false), // 是否已使用
    usedAt: timestamp('used_at'), // 使用时间
    usedBy: uuid('used_by').references(() => user.id), // 使用者
    expiresAt: timestamp('expires_at'), // 过期时间，NULL表示永不过期

    // 状态和管理
    isActive: boolean('is_active').notNull().default(true), // 是否启用
    description: text('description'), // 激活码描述
    batchId: varchar('batch_id', { length: 32 }), // 批次ID，用于批量生成的激活码

    // 创建信息
    createdBy: uuid('created_by')
      .notNull()
      .references(() => user.id), // 创建者（管理员）
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow()
  },
  table => ({
    // 性能优化索引
    codeIdx: index('idx_activation_code_code').on(table.code),
    typeIdx: index('idx_activation_code_type').on(table.type),
    activeIdx: index('idx_activation_code_active')
      .on(table.isActive)
      .where(sql`${table.isActive} = true`),
    usedIdx: index('idx_activation_code_used').on(table.isUsed),
    expiresAtIdx: index('idx_activation_code_expires_at').on(table.expiresAt),
    batchIdIdx: index('idx_activation_code_batch_id').on(table.batchId),
    createdByIdx: index('idx_activation_code_created_by').on(table.createdBy),
    createdAtIdx: index('idx_activation_code_created_at').on(table.createdAt.desc()),
    usedByIdx: index('idx_activation_code_used_by').on(table.usedBy),
    membershipPlanIdx: index('idx_activation_code_membership_plan').on(table.membershipPlanId),
    pointsPackageIdx: index('idx_activation_code_points_package').on(table.pointsPackageId)
  })
)

export type ActivationCode = InferSelectModel<typeof activationCode>

// 激活码使用记录表
export const activationCodeUsage = pgTable(
  'ActivationCodeUsage',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    activationCodeId: uuid('activation_code_id')
      .notNull()
      .references(() => activationCode.id), // 激活码ID
    userId: uuid('user_id')
      .notNull()
      .references(() => user.id), // 使用者ID

    // 激活结果
    resultType: varchar('result_type', {
      enum: ['membership_created', 'membership_extended', 'membership_upgraded', 'points_added']
    }).notNull(), // 激活结果类型
    resultId: uuid('result_id'), // 结果ID（订阅ID或积分交易ID）

    // 激活详情
    originalMembershipId: uuid('original_membership_id'), // 原有会员订阅ID（如果有）
    conflictResolution: varchar('conflict_resolution', {
      enum: ['no_conflict', 'extended', 'upgraded', 'replaced', 'queued', 'rejected']
    }), // 实际的冲突解决方式

    // 元数据
    metadata: json('metadata').default('{}'), // 额外信息（如激活时的用户状态等）
    ipAddress: varchar('ip_address', { length: 45 }), // 激活时的IP地址
    userAgent: text('user_agent'), // 用户代理

    usedAt: timestamp('used_at').notNull().defaultNow() // 使用时间
  },
  table => ({
    // 性能优化索引
    activationCodeIdIdx: index('idx_activation_code_usage_code_id').on(table.activationCodeId),
    userIdIdx: index('idx_activation_code_usage_user_id').on(table.userId),
    resultTypeIdx: index('idx_activation_code_usage_result_type').on(table.resultType),
    usedAtIdx: index('idx_activation_code_usage_used_at').on(table.usedAt.desc()),
    codeUserIdx: index('idx_activation_code_usage_code_user').on(
      table.activationCodeId,
      table.userId
    ),
    userUsedAtIdx: index('idx_activation_code_usage_user_used_at').on(
      table.userId,
      table.usedAt.desc()
    ),
    // 防重复激活的唯一索引
    uniqueUserCodeIdx: uniqueIndex('idx_activation_code_usage_unique_user_code').on(
      table.userId,
      table.activationCodeId
    )
  })
)

export type ActivationCodeUsage = InferSelectModel<typeof activationCodeUsage>

// ==================== 应用更新系统 ====================

// 应用版本表
export const appVersion = pgTable(
  'AppVersion',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    versionName: varchar('version_name', { length: 50 }).notNull(), // 版本名称如 1.0.0
    versionCode: integer('version_code').notNull(), // 版本号如 1
    versionType: varchar('version_type', {
      enum: ['apk', 'hotfix']
    }).notNull(), // 版本类型：APK整包 或 热更新
    fileUrl: text('file_url').notNull(), // 文件下载链接
    fileSize: bigint('file_size', { mode: 'number' }), // 文件大小(字节)
    fileHash: varchar('file_hash', { length: 64 }), // 文件哈希值
    minCompatibleVersion: varchar('min_compatible_version', { length: 50 }), // 最小兼容版本
    releaseNotes: text('release_notes'), // 更新说明
    isActive: boolean('is_active').notNull().default(true), // 是否启用
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow()
  },
  table => ({
    versionCodeIdx: index('idx_app_version_code').on(table.versionCode),
    versionTypeIdx: index('idx_app_version_type').on(table.versionType),
    activeIdx: index('idx_app_version_active').on(table.isActive),
    createdAtIdx: index('idx_app_version_created_at').on(table.createdAt.desc())
  })
)

export type AppVersion = InferSelectModel<typeof appVersion>

// 应用更新策略表
export const appUpdatePolicy = pgTable(
  'AppUpdatePolicy',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    versionId: uuid('version_id')
      .notNull()
      .references(() => appVersion.id, { onDelete: 'cascade' }),
    channel: varchar('channel', { length: 50 }).notNull().default('production'), // 渠道
    updateStrategy: varchar('update_strategy', {
      enum: ['force', 'optional', 'silent']
    }).notNull(), // 更新策略：强制、可选、静默
    targetVersionMin: varchar('target_version_min', { length: 50 }), // 目标最小版本
    targetVersionMax: varchar('target_version_max', { length: 50 }), // 目标最大版本
    rolloutPercentage: integer('rollout_percentage').notNull().default(100), // 灰度发布百分比
    isActive: boolean('is_active').notNull().default(true),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow()
  },
  table => ({
    versionIdIdx: index('idx_app_update_policy_version_id').on(table.versionId),
    channelIdx: index('idx_app_update_policy_channel').on(table.channel),
    strategyIdx: index('idx_app_update_policy_strategy').on(table.updateStrategy),
    activeIdx: index('idx_app_update_policy_active').on(table.isActive)
  })
)

export type AppUpdatePolicy = InferSelectModel<typeof appUpdatePolicy>

// 应用更新日志表
export const appUpdateLog = pgTable(
  'AppUpdateLog',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    userId: uuid('user_id').references(() => user.id), // 可为空，支持匿名统计
    deviceId: varchar('device_id', { length: 100 }), // 设备ID
    currentVersion: varchar('current_version', { length: 50 }), // 当前版本
    targetVersion: varchar('target_version', { length: 50 }), // 目标版本
    updateType: varchar('update_type', {
      enum: ['apk', 'hotfix']
    }).notNull(), // 更新类型
    updateStatus: varchar('update_status', {
      enum: [
        'started',
        'downloading',
        'downloaded',
        'installing',
        'installed',
        'failed',
        'cancelled'
      ]
    }).notNull(), // 更新状态
    errorMessage: text('error_message'), // 错误信息
    metadata: json('metadata').default('{}'), // 额外信息
    createdAt: timestamp('created_at').notNull().defaultNow()
  },
  table => ({
    userIdIdx: index('idx_app_update_log_user_id').on(table.userId),
    deviceIdIdx: index('idx_app_update_log_device_id').on(table.deviceId),
    updateTypeIdx: index('idx_app_update_log_update_type').on(table.updateType),
    statusIdx: index('idx_app_update_log_status').on(table.updateStatus),
    createdAtIdx: index('idx_app_update_log_created_at').on(table.createdAt.desc())
  })
)

export type AppUpdateLog = InferSelectModel<typeof appUpdateLog>

// ==================== 波形数据管理系统 ====================

// 波形数据表
export const waveformData = pgTable(
  'WaveformData',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    userId: uuid('user_id')
      .notNull()
      .references(() => user.id),
    name: varchar('name', { length: 100 }).notNull(),
    description: text('description'),

    // 波形数据
    recordingData: json('recording_data').notNull(), // 完整的录制数据
    thumbnail: text('thumbnail'), // 缩略图URL或Base64

    // 统计信息
    duration: integer('duration').notNull(), // 时长(ms)
    maxIntensity: decimal('max_intensity', { precision: 5, scale: 2 }), // 最大强度
    averageIntensity: decimal('average_intensity', { precision: 5, scale: 2 }), // 平均强度
    totalDistance: decimal('total_distance', { precision: 10, scale: 2 }), // 总距离
    complexity: decimal('complexity', { precision: 5, scale: 2 }), // 复杂度
    smoothness: decimal('smoothness', { precision: 5, scale: 2 }), // 平滑度

    // 社交功能
    isPublic: boolean('is_public').notNull().default(false),
    likeCount: integer('like_count').notNull().default(0),
    favoriteCount: integer('favorite_count').notNull().default(0),
    playCount: integer('play_count').notNull().default(0),
    tags: json('tags').$type<string[]>(),

    // 作者信息（从user表获取）
    authorName: varchar('author_name', { length: 100 }).notNull(),
    authorAvatar: text('author_avatar'),

    publishedAt: timestamp('published_at'),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow()
  },
  table => ({
    // 性能优化索引
    userIdIdx: index('idx_waveform_data_user_id').on(table.userId),
    publicIdx: index('idx_waveform_data_public')
      .on(table.isPublic)
      .where(sql`${table.isPublic} = true`),
    createdAtIdx: index('idx_waveform_data_created_at').on(table.createdAt.desc()),
    userCreatedIdx: index('idx_waveform_data_user_created').on(
      table.userId,
      table.createdAt.desc()
    ),
    publicCreatedIdx: index('idx_waveform_data_public_created')
      .on(table.isPublic, table.createdAt.desc())
      .where(sql`${table.isPublic} = true`),
    likeCountIdx: index('idx_waveform_data_like_count').on(table.likeCount.desc()),
    playCountIdx: index('idx_waveform_data_play_count').on(table.playCount.desc())
  })
)

export type WaveformData = InferSelectModel<typeof waveformData>

// 波形互动表
export const waveformInteraction = pgTable(
  'WaveformInteraction',
  {
    id: uuid('id').primaryKey().notNull().defaultRandom(),
    userId: uuid('user_id')
      .notNull()
      .references(() => user.id),
    waveformId: uuid('waveform_id')
      .notNull()
      .references(() => waveformData.id, { onDelete: 'cascade' }),
    interactionType: varchar('interaction_type', {
      enum: ['like', 'favorite', 'play']
    }).notNull(),
    createdAt: timestamp('created_at').notNull().defaultNow()
  },
  table => ({
    // 性能优化索引
    userIdIdx: index('idx_waveform_interaction_user_id').on(table.userId),
    waveformIdIdx: index('idx_waveform_interaction_waveform_id').on(table.waveformId),
    typeIdx: index('idx_waveform_interaction_type').on(table.interactionType),
    userWaveformIdx: index('idx_waveform_interaction_user_waveform').on(
      table.userId,
      table.waveformId
    ),
    createdAtIdx: index('idx_waveform_interaction_created_at').on(table.createdAt.desc()),
    // 防止重复互动的唯一索引
    uniqueUserWaveformTypeIdx: uniqueIndex('idx_waveform_interaction_unique').on(
      table.userId,
      table.waveformId,
      table.interactionType
    )
  })
)

export type WaveformInteraction = InferSelectModel<typeof waveformInteraction>
