import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import type { Env } from '@/types/env'
import { authMiddleware, optionalAuthMiddleware } from '@/middleware/auth'
import { languageMiddleware } from '@/middleware/language'
import { getCachedDbUserId } from '@/lib/cache/cache-utils'
import {
  createWaveform,
  getWaveformById,
  getUserWaveforms,
  getPublicWaveforms,
  getFavoriteWaveforms,
  toggleWaveformInteraction,
  recordWaveformPlay,
  deleteWaveform,
  type WaveformRecording,
  type WaveformFilter
} from '@/lib/db/queries/waveform'
import { createSuccessResponse, createErrorResponse } from '@/types/responses'
import { ErrorCode } from '@/types/errors'

const app = new Hono<{ Bindings: Env }>()

// 波形录制数据验证Schema
const waveformRecordingSchema = z.object({
  id: z.string(),
  name: z.string(),
  startTime: z.number(),
  endTime: z.number(),
  duration: z.number(),
  points: z.array(z.array(z.number())),
  canvasSize: z.tuple([z.number(), z.number()]),
  metadata: z.tuple([z.number(), z.number(), z.number(), z.number()])
})

// 创建波形数据验证Schema
const createWaveformSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  recordingData: waveformRecordingSchema,
  isPublic: z.boolean().optional(),
  tags: z.array(z.string()).optional(),
  thumbnail: z.string().optional(),
  maxIntensity: z.number().optional(),
  averageIntensity: z.number().optional(),
  totalDistance: z.number().optional(),
  complexity: z.number().optional(),
  smoothness: z.number().optional()
})

// 波形列表查询参数验证Schema
const waveformListQuerySchema = z.object({
  sortBy: z.enum(['latest', 'popular', 'most_played']).optional(),
  search: z.string().optional(),
  tags: z.string().optional(), // 逗号分隔的标签字符串
  page: z.string().optional(),
  limit: z.string().optional()
})

// 辅助函数：从Supabase用户上下文解析本地用户ID（带缓存）
async function resolveLocalUserId(c: any): Promise<string> {
  const supabaseUser = c.get('user')
  if (!supabaseUser) {
    const t = c.get('t')
    throw new Error(t('auth.user_not_authenticated'))
  }

  const localUserId = await getCachedDbUserId(c.env, supabaseUser.id)
  if (!localUserId) {
    const t = c.get('t')
    throw new Error(t('auth.user_not_exist'))
  }

  return localUserId
}

/**
 * POST /api/waveforms
 * 创建波形数据
 */
app.post(
  '/',
  authMiddleware,
  languageMiddleware,
  zValidator('json', createWaveformSchema),
  async c => {
    try {
      const localUserId = await resolveLocalUserId(c)
      const data = c.req.valid('json')
      const t = c.get('t')

      console.log('🎵 创建波形数据:', {
        userId: localUserId,
        name: data.name,
        isPublic: data.isPublic,
        duration: data.recordingData.duration
      })

      const waveforms = await createWaveform(c.env, localUserId, data)

      return c.json(createSuccessResponse(waveforms[0], t('waveform.created_successfully')))
    } catch (error) {
      console.error('创建波形数据失败:', error)
      const t = c.get('t')
      return c.json(
        createErrorResponse(
          error instanceof Error ? error.message : t('waveform.create_failed'),
          ErrorCode.SYSTEM_ERROR
        ),
        500
      )
    }
  }
)

/**
 * GET /api/waveforms/my
 * 获取我的波形列表
 */
app.get(
  '/my',
  authMiddleware,
  languageMiddleware,
  zValidator('query', waveformListQuerySchema),
  async c => {
    try {
      const localUserId = await resolveLocalUserId(c)
      const query = c.req.valid('query')

      // 构建过滤条件
      const filter: WaveformFilter = {
        sortBy: query.sortBy || 'latest',
        search: query.search,
        tags: query.tags ? query.tags.split(',') : undefined,
        page: query.page ? parseInt(query.page) : 1,
        limit: query.limit ? parseInt(query.limit) : 20
      }

      const result = await getUserWaveforms(c.env, localUserId, filter)

      return c.json(createSuccessResponse(result))
    } catch (error) {
      console.error('获取我的波形列表失败:', error)
      const t = c.get('t')
      return c.json(
        createErrorResponse(
          error instanceof Error ? error.message : t('waveform.get_my_list_failed'),
          ErrorCode.SYSTEM_ERROR
        ),
        500
      )
    }
  }
)

/**
 * GET /api/waveforms/public
 * 获取公共波形库
 */
app.get(
  '/public',
  optionalAuthMiddleware,
  languageMiddleware,
  zValidator('query', waveformListQuerySchema),
  async c => {
    try {
      const query = c.req.valid('query')
      const user = c.get('user')

      let userId: string | undefined
      if (user?.id) {
        const dbUserId = await getCachedDbUserId(c.env, user.id)
        userId = dbUserId || undefined
      }

      // 构建过滤条件
      const filter: WaveformFilter = {
        sortBy: query.sortBy || 'latest',
        search: query.search,
        tags: query.tags ? query.tags.split(',') : undefined,
        page: query.page ? parseInt(query.page) : 1,
        limit: query.limit ? parseInt(query.limit) : 20
      }

      const result = await getPublicWaveforms(c.env, userId, filter)

      return c.json(createSuccessResponse(result))
    } catch (error) {
      console.error('获取公共波形库失败:', error)
      const t = c.get('t')
      return c.json(
        createErrorResponse(t('waveform.get_public_list_failed'), ErrorCode.SYSTEM_ERROR),
        500
      )
    }
  }
)

/**
 * GET /api/waveforms/favorites
 * 获取收藏的波形
 */
app.get(
  '/favorites',
  authMiddleware,
  languageMiddleware,
  zValidator('query', waveformListQuerySchema),
  async c => {
    try {
      const localUserId = await resolveLocalUserId(c)
      const query = c.req.valid('query')

      // 构建过滤条件
      const filter: WaveformFilter = {
        page: query.page ? parseInt(query.page) : 1,
        limit: query.limit ? parseInt(query.limit) : 20
      }

      const result = await getFavoriteWaveforms(c.env, localUserId, filter)

      return c.json(createSuccessResponse(result))
    } catch (error) {
      console.error('获取收藏波形失败:', error)
      const t = c.get('t')
      return c.json(
        createErrorResponse(
          error instanceof Error ? error.message : t('waveform.get_favorites_failed'),
          ErrorCode.SYSTEM_ERROR
        ),
        500
      )
    }
  }
)

/**
 * POST /api/waveforms/:id/like
 * 点赞/取消点赞波形
 */
app.post('/:id/like', authMiddleware, languageMiddleware, async c => {
  try {
    const waveformId = c.req.param('id')
    const localUserId = await resolveLocalUserId(c)
    const t = c.get('t')

    const result = await toggleWaveformInteraction(c.env, localUserId, waveformId, 'like')

    if (!result.success) {
      return c.json(createErrorResponse(t('waveform.like_failed'), ErrorCode.SYSTEM_ERROR), 500)
    }

    return c.json(
      createSuccessResponse(
        {
          isLiked: result.isActive,
          likeCount: result.count
        },
        result.isActive ? t('waveform.liked_successfully') : t('waveform.unliked_successfully')
      )
    )
  } catch (error) {
    console.error('点赞操作失败:', error)
    const t = c.get('t')
    return c.json(
      createErrorResponse(
        error instanceof Error ? error.message : t('waveform.like_failed'),
        ErrorCode.SYSTEM_ERROR
      ),
      500
    )
  }
})

/**
 * POST /api/waveforms/:id/favorite
 * 收藏/取消收藏波形
 */
app.post('/:id/favorite', authMiddleware, languageMiddleware, async c => {
  try {
    const waveformId = c.req.param('id')
    const localUserId = await resolveLocalUserId(c)
    const t = c.get('t')

    const result = await toggleWaveformInteraction(c.env, localUserId, waveformId, 'favorite')

    if (!result.success) {
      return c.json(createErrorResponse(t('waveform.favorite_failed'), ErrorCode.SYSTEM_ERROR), 500)
    }

    return c.json(
      createSuccessResponse(
        {
          isFavorited: result.isActive,
          favoriteCount: result.count
        },
        result.isActive
          ? t('waveform.favorited_successfully')
          : t('waveform.unfavorited_successfully')
      )
    )
  } catch (error) {
    console.error('收藏操作失败:', error)
    const t = c.get('t')
    return c.json(
      createErrorResponse(
        error instanceof Error ? error.message : t('waveform.favorite_failed'),
        ErrorCode.SYSTEM_ERROR
      ),
      500
    )
  }
})

/**
 * POST /api/waveforms/:id/play
 * 记录波形播放
 */
app.post('/:id/play', authMiddleware, languageMiddleware, async c => {
  try {
    const waveformId = c.req.param('id')
    const localUserId = await resolveLocalUserId(c)
    const t = c.get('t')

    const result = await recordWaveformPlay(c.env, localUserId, waveformId)

    if (!result.success) {
      return c.json(
        createErrorResponse(t('waveform.play_record_failed'), ErrorCode.SYSTEM_ERROR),
        500
      )
    }

    return c.json(
      createSuccessResponse(
        {
          playCount: result.playCount
        },
        t('waveform.play_recorded_successfully')
      )
    )
  } catch (error) {
    console.error('记录播放失败:', error)
    const t = c.get('t')
    return c.json(
      createErrorResponse(
        error instanceof Error ? error.message : t('waveform.play_record_failed'),
        ErrorCode.SYSTEM_ERROR
      ),
      500
    )
  }
})

/**
 * GET /api/waveforms/:id
 * 根据ID获取波形数据
 */
app.get('/:id', optionalAuthMiddleware, languageMiddleware, async c => {
  try {
    const waveformId = c.req.param('id')
    const user = c.get('user')
    const t = c.get('t')

    let userId: string | undefined
    if (user?.id) {
      const dbUserId = await getCachedDbUserId(c.env, user.id)
      userId = dbUserId || undefined
    }

    const waveform = await getWaveformById(c.env, waveformId, userId)

    if (!waveform) {
      return c.json(createErrorResponse(t('waveform.not_found'), ErrorCode.INVALID_PARAMETER), 404)
    }

    return c.json(createSuccessResponse(waveform))
  } catch (error) {
    console.error('获取波形数据失败:', error)
    const t = c.get('t')
    return c.json(createErrorResponse(t('waveform.get_failed'), ErrorCode.SYSTEM_ERROR), 500)
  }
})

/**
 * DELETE /api/waveforms/:id
 * 删除波形数据
 */
app.delete('/:id', authMiddleware, languageMiddleware, async c => {
  try {
    const waveformId = c.req.param('id')
    const localUserId = await resolveLocalUserId(c)
    const t = c.get('t')

    const result = await deleteWaveform(c.env, localUserId, waveformId)

    if (!result.success) {
      return c.json(
        createErrorResponse(
          result.error || t('waveform.delete_failed'),
          ErrorCode.INVALID_PARAMETER
        ),
        400
      )
    }

    return c.json(createSuccessResponse(null, t('waveform.deleted_successfully')))
  } catch (error) {
    console.error('删除波形失败:', error)
    const t = c.get('t')
    return c.json(
      createErrorResponse(
        error instanceof Error ? error.message : t('waveform.delete_failed'),
        ErrorCode.SYSTEM_ERROR
      ),
      500
    )
  }
})

export default app
