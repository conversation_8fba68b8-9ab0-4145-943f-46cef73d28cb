import { Hono } from 'hono'
import { z } from 'zod'
import {
  createSupabaseServiceClient,
  signUpWithEmail,
  signInWithEmail,
  signOut
} from '@/lib/supabase'
import {
  createUser,
  getUserBySupabaseId,
  createUserProfile,
  getUser,
  initializeNewUserPointsAsync
} from '@/lib/db/queries'
import {
  generateVerificationCode,
  sendRegistrationCodeEmail,
  sendLoginCodeEmail,
  storeVerificationCode,
  verifyStoredCode
} from '@/lib/email'
import { authMiddleware, optionalAuthMiddleware } from '@/middleware/auth'
import {
  validateInviteCode,
  createReferralRelation,
  incrementInviteCodeUsage
} from '@/lib/db/queries/referral'
import type { Env } from '@/types/env'
import type { SupportedLanguage } from '@/i18n/config'
import { throwBusinessError } from '@/middleware/global-error-handler'
import { createSuccessResponse } from '@/types/responses'
import { ErrorCode } from '@/types/errors'

const auth = new Hono<{
  Bindings: Env
  Variables: {
    language: SupportedLanguage
    t: (key: string, params?: Record<string, string | number>) => string
  }
}>()

// ==================== 验证模式 ====================

// 创建国际化验证中间件
const createI18nValidator = (schema: z.ZodSchema) => {
  return async (c: any, next: any) => {
    try {
      const data = await c.req.json()
      schema.parse(data)
      await next()
    } catch (error) {
      if (error instanceof z.ZodError) {
        const t = c.get('t')
        const firstError = error.errors[0]
        let message = t('invalid_input')

        // 根据错误类型和字段提供具体的国际化消息
        if (firstError.path.includes('email')) {
          if (firstError.code === 'invalid_string' && firstError.validation === 'email') {
            message = t('email_invalid')
          }
        } else if (firstError.path.includes('password')) {
          if (firstError.code === 'too_small') {
            message = t('password_min_length', { length: firstError.minimum })
          }
        } else if (firstError.path.includes('code')) {
          if (firstError.code === 'too_small') {
            message = t('code_min_length', { length: firstError.minimum })
          }
        } else if (firstError.path.includes('refresh_token')) {
          message = t('refresh_token_required')
        }

        throwBusinessError(ErrorCode.VALIDATION_ERROR, { message })
      }
      throw error
    }
  }
}

// 基础验证模式（不包含错误消息）
const registerSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
  name: z.string().optional(),
  inviteCode: z.string().optional()
})

const loginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6)
})

const sendCodeSchema = z.object({
  email: z.string().email()
})

const verifyCodeSchema = z.object({
  email: z.string().email(),
  code: z.string().min(6),
  name: z.string().optional(),
  password: z.string().optional(),
  inviteCode: z.string().optional()
})

const loginCodeSchema = z.object({
  email: z.string().email(),
  code: z.string().min(6)
})

const refreshTokenSchema = z.object({
  refresh_token: z.string().min(1)
})

// ==================== 发送验证码 ====================

auth.post('/send-code', createI18nValidator(sendCodeSchema), async c => {
  const t = c.get('t')
  const data = await c.req.json()
  const { email } = data
  const env = c.env

  // 检查数据库中是否已存在用户记录
  const existingUsers = await getUser(env, email)
  if (existingUsers && existingUsers.length > 0) {
    throwBusinessError(ErrorCode.EMAIL_ALREADY_REGISTERED, {
      message: t('auth.email_already_registered')
    })
  }

  // 生成6位数字验证码
  const verificationCode = generateVerificationCode()

  // 发送注册验证码邮件
  const emailResult = await sendRegistrationCodeEmail(env, email, verificationCode)

  if (!emailResult.success) {
    console.error('发送注册验证码邮件失败:', emailResult.error)
    throwBusinessError(ErrorCode.EMAIL_SEND_FAILED, { message: t('auth.send_code_failed') })
  }

  // 将验证码存储到 KV（10分钟有效期）
  await storeVerificationCode(env, email, verificationCode, 10)

  return c.json(
    createSuccessResponse({
      message: t('auth.code_sent')
    })
  )
})

// ==================== 验证验证码并注册 ====================

auth.post('/verify-code', createI18nValidator(verifyCodeSchema), async c => {
  const t = c.get('t')
  const data = await c.req.json()
  const { email, code, name, password, inviteCode } = data
  const env = c.env

  // 验证存储的验证码
  const codeVerification = await verifyStoredCode(env, email, code)

  if (!codeVerification.valid) {
    throwBusinessError(ErrorCode.EMAIL_VERIFICATION_FAILED, {
      message: codeVerification.error || t('auth.code_verification_failed')
    })
  }

  // 验证码正确，尝试创建用户
  const supabase = createSupabaseServiceClient(env)

  // 如果用户提供了密码，使用用户密码；否则生成临时密码
  const userPassword = password || 'temp_' + Math.random().toString(36).substring(2, 15)

  let authData: any
  let isNewUser = true

  // 尝试创建新用户
  const { data: createData, error: createError } = await supabase.auth.admin.createUser({
    email,
    password: userPassword,
    email_confirm: true, // 自动确认邮箱
    user_metadata: {
      name: name || email.split('@')[0],
      hasPassword: !!password // 标记用户是否设置了密码
    }
  })

  if (createError) {
    if (
      createError.message.includes('already been registered') ||
      createError.message.includes('already exists') ||
      createError.message.includes('User already registered')
    ) {
      // 用户已存在，尝试获取现有用户
      isNewUser = false

      const { data: users, error: listError } = await supabase.auth.admin.listUsers()
      if (listError) {
        console.error('获取用户列表失败:', listError)
        throwBusinessError(ErrorCode.EMAIL_ALREADY_REGISTERED, {
          message: t('auth.email_already_registered_login_directly')
        })
      }

      const existingUser = users?.users?.find(u => u.email === email)
      if (!existingUser) {
        throwBusinessError(ErrorCode.USER_NOT_FOUND, {
          message: t('auth.user_status_abnormal')
        })
      }

      authData = { user: existingUser }
    } else if (createError.message.includes('Database error')) {
      // 数据库错误，通常是邮件配置或权限问题
      throwBusinessError(ErrorCode.SYSTEM_ERROR, {
        message: t('auth.database_error_creating_user')
      })
    } else {
      // 其他未知错误
      throwBusinessError(ErrorCode.SYSTEM_ERROR, {
        message: t('auth.registration_failed_unknown_error')
      })
    }
  } else {
    authData = createData
  }

  if (!authData.user) {
    throwBusinessError(ErrorCode.SYSTEM_ERROR, {
      message: t('auth.registration_failed')
    })
  }

  try {
    // 检查数据库中是否已有用户记录
    let dbUser = await getUserBySupabaseId(env, authData.user.id)

    if (!dbUser && isNewUser) {
      // 在数据库中创建用户记录
      const [newDbUser] = await createUser(env, email, authData.user.id)
      dbUser = newDbUser

      // 创建用户配置文件
      if (dbUser) {
        await createUserProfile(env, {
          userId: dbUser.id,
          nickname: name || email.split('@')[0],
          gender: 'other'
        })

        // 🆕 异步初始化新用户积分（不阻塞注册流程）
        initializeNewUserPointsAsync(env, dbUser.id)

        // 处理邀请码（如果提供了）
        if (inviteCode && inviteCode.trim()) {
          try {
            const validationResult = await validateInviteCode(env, inviteCode.trim())

            if (validationResult.valid && validationResult.inviteCode) {
              // 创建推荐关系
              await createReferralRelation(
                env,
                validationResult.inviteCode.userId,
                dbUser.id,
                validationResult.inviteCode.id
              )

              // 更新邀请码使用次数
              await incrementInviteCodeUsage(env, validationResult.inviteCode.id)

              console.log(
                `用户 ${dbUser.id} 通过邀请码 ${inviteCode} 注册成功，邀请人: ${validationResult.inviteCode.userId}`
              )
            } else {
              console.warn(`邀请码处理失败: ${validationResult.error}`)
              // 邀请码处理失败不影响注册流程，只记录日志
            }
          } catch (error) {
            console.error('处理邀请码时出错:', error)
            // 邀请码处理失败不影响注册流程
          }
        }
      }
    }

    // 为用户创建会话
    let signInData: any = null

    if (password) {
      // 用户设置了密码，使用密码登录
      const { data: passwordSignIn, error: passwordError } = await supabase.auth.signInWithPassword(
        {
          email,
          password: userPassword
        }
      )

      if (!passwordError && passwordSignIn.session) {
        signInData = passwordSignIn
      }
    }

    // 如果密码登录失败或用户没有设置密码，尝试其他方式
    if (!signInData?.session) {
      console.warn('密码登录失败或用户未设置密码，返回注册成功状态')
      return c.json(
        createSuccessResponse({
          message: isNewUser ? t('auth.registration_success') : t('auth.verification_success'),
          requireLogin: !password, // 如果没有密码，提示需要登录
          hasPassword: !!password
        }),
        201
      )
    }

    return c.json(
      createSuccessResponse({
        message: isNewUser ? t('auth.registration_success') : t('auth.verification_success'),
        session: {
          access_token: signInData.session.access_token,
          refresh_token: signInData.session.refresh_token,
          expires_at: signInData.session.expires_at,
          user: {
            id: authData.user.id,
            email: authData.user.email,
            emailConfirmed: true, // 通过验证码验证的用户邮箱已确认
            dbUserId: dbUser?.id
          }
        }
      }),
      201
    )
  } catch (dbError) {
    console.error('数据库操作失败:', dbError)
    throwBusinessError(ErrorCode.SYSTEM_ERROR, {
      message: t('auth.registration_success_but_init_failed')
    })
  }
})

// ==================== 用户注册 (保留原有功能) ====================

auth.post('/register', createI18nValidator(registerSchema), async c => {
  const t = c.get('t')
  const data = await c.req.json()
  const { email, password, name, inviteCode } = data
  const env = c.env

  // 使用 Supabase Auth 注册用户
  const { data: authData, error } = await signUpWithEmail(env, email, password)

  if (error) {
    console.error('Supabase 注册失败:', error)

    // 处理常见错误
    if (error.message.includes('already registered')) {
      throwBusinessError(ErrorCode.EMAIL_ALREADY_REGISTERED, {
        message: t('auth.email_already_registered')
      })
    }

    throwBusinessError(ErrorCode.SYSTEM_ERROR, {
      message: error.message || t('auth.registration_failed')
    })
  }

  if (!authData.user) {
    throwBusinessError(ErrorCode.SYSTEM_ERROR, {
      message: t('auth.registration_failed_user_not_created')
    })
  }

  try {
    // 在数据库中创建用户记录
    const [dbUser] = await createUser(env, email, authData.user.id)

    // 创建用户配置文件
    if (dbUser) {
      await createUserProfile(env, {
        userId: dbUser.id,
        nickname: name || email.split('@')[0],
        gender: 'other'
      })

      // 🆕 异步初始化新用户积分（不阻塞注册流程）
      initializeNewUserPointsAsync(env, dbUser.id)

      // 处理邀请码（如果提供了）
      if (inviteCode && inviteCode.trim()) {
        try {
          const validationResult = await validateInviteCode(env, inviteCode.trim())

          if (validationResult.valid && validationResult.inviteCode) {
            // 创建推荐关系
            await createReferralRelation(
              env,
              validationResult.inviteCode.userId,
              dbUser.id,
              validationResult.inviteCode.id
            )

            // 更新邀请码使用次数
            await incrementInviteCodeUsage(env, validationResult.inviteCode.id)

            console.log(
              `用户 ${dbUser.id} 通过邀请码 ${inviteCode} 注册成功，邀请人: ${validationResult.inviteCode.userId}`
            )
          } else {
            console.warn(`邀请码处理失败: ${validationResult.error}`)
            // 邀请码处理失败不影响注册流程，只记录日志
          }
        } catch (error) {
          console.error('处理邀请码时出错:', error)
          // 邀请码处理失败不影响注册流程
        }
      }
    }

    return c.json(
      createSuccessResponse({
        message: t('auth.registration_success_check_email'),
        user: {
          id: authData.user.id,
          email: authData.user.email,
          emailConfirmed: authData.user.email_confirmed_at !== null
        }
      }),
      201
    )
  } catch (dbError) {
    console.error('数据库操作失败:', dbError)
    throwBusinessError(ErrorCode.SYSTEM_ERROR, {
      message: t('auth.registration_success_but_init_failed')
    })
  }
})

// ==================== 用户登录 ====================

auth.post('/login', createI18nValidator(loginSchema), async c => {
  const t = c.get('t')
  const data = await c.req.json()
  const { email, password } = data
  const env = c.env

  // 使用 Supabase Auth 登录
  const { data: authData, error } = await signInWithEmail(env, email, password)

  if (error) {
    console.error('Supabase 登录失败:', error)
    throwBusinessError(ErrorCode.AUTH_UNAUTHORIZED, {
      message: t('user.login_failed')
    })
  }

  if (!authData.user || !authData.session) {
    throwBusinessError(ErrorCode.AUTH_UNAUTHORIZED, {
      message: t('user.login_failed')
    })
  }

  // 获取或创建数据库用户记录
  let dbUser = await getUserBySupabaseId(env, authData.user.id)

  if (!dbUser) {
    // 如果数据库中没有用户记录，创建一个
    const [newDbUser] = await createUser(env, email, authData.user.id)
    dbUser = newDbUser

    // 创建默认用户配置文件
    if (dbUser) {
      await createUserProfile(env, {
        userId: dbUser.id,
        nickname: email.split('@')[0],
        gender: 'other'
      })
    }
  }

  return c.json(
    createSuccessResponse({
      message: t('user.login_success'),
      session: {
        access_token: authData.session.access_token,
        refresh_token: authData.session.refresh_token,
        expires_at: authData.session.expires_at,
        user: {
          id: authData.user.id,
          email: authData.user.email,
          emailConfirmed: authData.user.email_confirmed_at !== null,
          dbUserId: dbUser?.id
        }
      }
    })
  )
})

// ==================== 登录验证码验证 ====================

// 发送登录验证码
auth.post('/send-login-code', createI18nValidator(sendCodeSchema), async c => {
  const t = c.get('t')
  const data = await c.req.json()
  const { email } = data
  const env = c.env

  // 检查数据库中是否存在用户记录
  const existingUsers = await getUser(env, email)
  if (!existingUsers || existingUsers.length === 0) {
    throwBusinessError(ErrorCode.USER_NOT_FOUND, {
      message: t('auth.user_not_exist_register_first')
    })
  }

  // 生成6位数字验证码
  const verificationCode = generateVerificationCode()

  // 发送登录验证码邮件
  const emailResult = await sendLoginCodeEmail(env, email, verificationCode)

  if (!emailResult.success) {
    console.error('发送登录验证码邮件失败:', emailResult.error)
    throwBusinessError(ErrorCode.EMAIL_SEND_FAILED, {
      message: t('auth.send_code_failed')
    })
  }

  // 将验证码存储到 KV（10分钟有效期）
  await storeVerificationCode(env, email, verificationCode, 10)

  return c.json(
    createSuccessResponse({
      message: t('auth.code_sent')
    })
  )
})

auth.post('/login-code', createI18nValidator(loginCodeSchema), async c => {
  const t = c.get('t')
  const data = await c.req.json()
  const { email, code } = data
  const env = c.env

  // 验证存储的验证码
  const codeVerification = await verifyStoredCode(env, email, code)

  if (!codeVerification.valid) {
    throwBusinessError(ErrorCode.EMAIL_VERIFICATION_FAILED, {
      message: codeVerification.error || t('auth.code_verification_failed')
    })
  }

  // 验证码正确，检查用户是否存在
  const existingUsers = await getUser(env, email)
  if (!existingUsers || existingUsers.length === 0) {
    throwBusinessError(ErrorCode.USER_NOT_FOUND, {
      message: t('auth.user_not_exist_register_first')
    })
  }

  const dbUser = existingUsers[0]

  // 获取 Supabase 用户信息
  const supabase = createSupabaseServiceClient(env)
  const { data: users, error: listError } = await supabase.auth.admin.listUsers()

  if (listError) {
    console.error('获取用户列表失败:', listError)
    throwBusinessError(ErrorCode.SYSTEM_ERROR, {
      message: t('auth.login_code_failed')
    })
  }

  const authUser = users?.users?.find(u => u.id === dbUser.supabaseUserId)
  if (!authUser) {
    throwBusinessError(ErrorCode.USER_NOT_FOUND, {
      message: t('auth.user_status_abnormal')
    })
  }

  try {
    // 生成一个临时的强密码
    const tempPassword = 'TempPass_' + Math.random().toString(36).substring(2, 15) + Date.now()

    // 更新用户密码（临时）
    const { error: updateError } = await supabase.auth.admin.updateUserById(authUser.id, {
      password: tempPassword
    })

    if (updateError) {
      console.error('更新临时密码失败:', updateError)
      throw updateError
    }

    // 使用临时密码登录
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email,
      password: tempPassword
    })

    if (loginError || !loginData.session) {
      console.error('临时密码登录失败:', loginError)
      throw loginError
    }

    return c.json(
      createSuccessResponse({
        message: t('auth.login_success'),
        session: {
          access_token: loginData.session.access_token,
          refresh_token: loginData.session.refresh_token,
          expires_at: loginData.session.expires_at,
          user: {
            id: authUser.id,
            email: authUser.email,
            emailConfirmed: authUser.email_confirmed_at !== null,
            dbUserId: dbUser.id
          }
        }
      })
    )
  } catch (fallbackError) {
    console.error('验证码登录失败:', fallbackError)
    throwBusinessError(ErrorCode.AUTH_UNAUTHORIZED, {
      message: t('auth.login_code_failed')
    })
  }
})

// ==================== 用户登出 ====================

auth.post('/logout', optionalAuthMiddleware, async c => {
  const t = c.get('t')
  const env = c.env

  // 从请求头获取 token
  const authHeader = c.req.header('Authorization')
  if (authHeader && authHeader.startsWith('Bearer ')) {
    const token = authHeader.substring(7)

    try {
      // 使用 Supabase Auth 登出
      await signOut(env, token)
    } catch (error) {
      console.error('登出过程中出现错误:', error)
      // 即使发生错误也返回成功，因为前端可能已清除本地状态
    }
  }

  return c.json(
    createSuccessResponse({
      message: t('auth.logout_success')
    })
  )
})

// ==================== 获取用户信息 ====================

auth.get('/profile', authMiddleware, async c => {
  const t = c.get('t')
  const user = c.get('user')
  const env = c.env

  if (!user) {
    throwBusinessError(ErrorCode.AUTH_UNAUTHORIZED, {
      message: t('auth.user_not_found')
    })
  }

  // 获取数据库用户信息
  const dbUser = await getUserBySupabaseId(env, user.id)

  return c.json(
    createSuccessResponse({
      user: {
        id: user.id,
        email: user.email,
        emailConfirmed: user.email_confirmed_at !== null,
        dbUserId: dbUser?.id,
        createdAt: user.created_at,
        lastSignIn: user.last_sign_in_at
      }
    })
  )
})

// ==================== 获取会话信息 ====================

auth.get('/session', optionalAuthMiddleware, async c => {
  const t = c.get('t')
  const user = c.get('user')

  if (!user) {
    return c.json(
      createSuccessResponse({
        session: null
      })
    )
  }

  const env = c.env

  try {
    const dbUser = await getUserBySupabaseId(env, user.id)

    // 计算实际的 token 过期时间（Supabase JWT 默认 1 小时）
    // 建议在 token 过期前 10 分钟提醒刷新
    const tokenExpiresAt = new Date(Date.now() + 50 * 60 * 1000) // 50 分钟后提醒刷新
    const sessionExpiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 天后完全过期

    return c.json(
      createSuccessResponse({
        user: {
          id: user.id,
          email: user.email,
          emailConfirmed: user.email_confirmed_at !== null,
          dbUserId: dbUser?.id
        },
        expires: sessionExpiresAt.toISOString(), // 会话过期时间
        tokenRefreshAt: tokenExpiresAt.toISOString(), // 建议刷新时间
        message: t('auth.token_refresh_suggestion', { minutes: 50 })
      })
    )
  } catch (error) {
    console.error('获取会话信息失败:', error)
    return c.json(null, 200)
  }
})

// ==================== 刷新令牌 ====================

auth.post('/refresh', createI18nValidator(refreshTokenSchema), async c => {
  const t = c.get('t')
  const data = await c.req.json()
  const { refresh_token } = data
  const env = c.env

  // 使用 Supabase 刷新令牌
  const supabase = createSupabaseServiceClient(env)

  const { data: refreshData, error } = await supabase.auth.refreshSession({
    refresh_token
  })

  if (error || !refreshData.session || !refreshData.user) {
    console.error('刷新令牌失败:', error)
    throwBusinessError(ErrorCode.AUTH_TOKEN_EXPIRED, {
      message: t('auth.refresh_token_failed_or_expired')
    })
  }

  // 获取或创建数据库用户记录
  let dbUser = await getUserBySupabaseId(env, refreshData.user.id)

  if (!dbUser) {
    // 如果数据库中没有用户记录，创建一个
    const [newDbUser] = await createUser(env, refreshData.user.email || '', refreshData.user.id)
    dbUser = newDbUser

    // 创建默认用户配置文件
    if (dbUser) {
      await createUserProfile(env, {
        userId: dbUser.id,
        nickname: refreshData.user.email?.split('@')[0] || 'User',
        gender: 'other'
      })
    }
  }

  // 计算下次建议刷新时间（token 过期前 10 分钟）
  const nextRefreshAt = new Date(Date.now() + 50 * 60 * 1000) // 50 分钟后

  return c.json(
    createSuccessResponse({
      message: t('auth.token_refresh_success'),
      session: {
        access_token: refreshData.session.access_token,
        refresh_token: refreshData.session.refresh_token,
        expires_at: refreshData.session.expires_at,
        user: {
          id: refreshData.user.id,
          email: refreshData.user.email,
          emailConfirmed: refreshData.user.email_confirmed_at !== null,
          dbUserId: dbUser?.id
        }
      },
      // 添加刷新建议信息
      nextRefreshAt: nextRefreshAt.toISOString(),
      refreshInterval: 3000000, // 50 分钟（毫秒）
      message_detail: t('auth.token_refresh_suggestion', { minutes: 50 })
    })
  )
})

// ==================== 管理员登录 ====================

auth.post('/admin/login', createI18nValidator(loginSchema), async c => {
  const t = c.get('t')
  try {
    const data = await c.req.json()
    const { email, password } = data
    const env = c.env

    // 使用 Supabase Auth 登录
    const { data: authData, error } = await signInWithEmail(env, email, password)

    if (error) {
      console.error('管理员登录失败:', error)
      return c.json(
        {
          success: false,
          message: t('auth.admin_login_invalid_credentials')
        },
        401
      )
    }

    if (!authData.user || !authData.session) {
      return c.json(
        {
          success: false,
          message: t('auth.admin_login_failed')
        },
        401
      )
    }

    // 检查管理员权限
    // Supabase 可能使用 user_metadata 或 raw_user_meta_data
    const userMetadata =
      authData.user.user_metadata || (authData.user as any).raw_user_meta_data || {}
    const isAdmin = userMetadata.role === 'admin' || userMetadata.isAdmin === true

    // 备用检查：检查特定的管理员邮箱
    const adminEmails = [
      '<EMAIL>'
      // 在这里添加其他管理员邮箱
    ]

    const isAdminByEmail = adminEmails.includes(authData.user.email || '')

    if (!isAdmin && !isAdminByEmail) {
      return c.json(
        {
          success: false,
          message: t('auth.admin_no_permission')
        },
        403
      )
    }

    // 获取或创建数据库用户记录
    let dbUser = await getUserBySupabaseId(env, authData.user.id)

    if (!dbUser) {
      // 创建数据库用户记录
      const newUsers = await createUser(env, authData.user.email!, authData.user.id)
      dbUser = newUsers[0]
    }

    return c.json(
      {
        success: true,
        message: t('auth.admin_login_success'),
        data: {
          user: {
            id: authData.user.id,
            email: authData.user.email,
            isAdmin: true
          },
          token: authData.session.access_token
        }
      },
      200
    )
  } catch (error) {
    console.error('管理员登录过程中出现错误:', error)
    return c.json(
      {
        success: false,
        message: t('auth.admin_login_error')
      },
      500
    )
  }
})

// 获取管理员信息
auth.get('/admin/profile', authMiddleware, async c => {
  const t = c.get('t')
  try {
    const user = c.get('user')
    if (!user) {
      return c.json(
        {
          success: false,
          message: t('auth.not_logged_in')
        },
        401
      )
    }

    // 检查管理员权限
    // Supabase 可能使用 user_metadata 或 raw_user_meta_data
    const userMetadata = user.user_metadata || user.raw_user_meta_data || {}
    const isAdmin = userMetadata.role === 'admin' || userMetadata.isAdmin === true

    const adminEmails = [
      '<EMAIL>'
      // 在这里添加其他管理员邮箱
    ]

    const isAdminByEmail = adminEmails.includes(user.email || '')

    if (!isAdmin && !isAdminByEmail) {
      return c.json(
        {
          success: false,
          message: t('auth.admin_no_permission')
        },
        403
      )
    }

    return c.json(
      {
        success: true,
        data: {
          id: user.id,
          email: user.email,
          isAdmin: true,
          createdAt: user.created_at
        }
      },
      200
    )
  } catch (error) {
    console.error('获取管理员信息失败:', error)
    return c.json(
      {
        success: false,
        message: t('auth.admin_profile_error')
      },
      500
    )
  }
})

// 管理员登出
auth.post('/admin/logout', authMiddleware, async c => {
  const t = c.get('t')
  try {
    const env = c.env
    const authHeader = c.req.header('Authorization')

    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7)
      await signOut(env, token)
    }

    return c.json(
      {
        success: true,
        message: t('auth.admin_logout_success')
      },
      200
    )
  } catch (error) {
    console.error('管理员登出失败:', error)
    return c.json(
      {
        success: false,
        message: t('auth.admin_logout_error')
      },
      500
    )
  }
})

export default auth
