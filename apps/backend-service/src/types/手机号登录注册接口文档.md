# 手机号登录注册接口文档（完整版）

## 📋 目录
- [1. 项目概述](#1-项目概述)
- [2. 技术架构](#2-技术架构)
- [3. 业务逻辑说明](#3-业务逻辑说明)
- [4. 数据库设计](#4-数据库设计)
- [5. API接口规范（5个接口）](#5-api接口规范5个接口)
- [6. 前端集成指南](#6-前端集成指南)
- [7. 错误处理](#7-错误处理)
- [8. 安全考虑](#8-安全考虑)

---

## 1. 项目概述

### 功能简介
基于火山引擎SMS服务的手机号认证系统，完整对标邮箱认证功能：
- ✅ **注册流程**：发送验证码 → 验证码注册（必须设密码）
- ✅ **登录方式**：手机号密码登录 + 手机号验证码登录
- ✅ **完整功能**：邀请码系统、新用户积分奖励、JWT会话管理

### 架构设计
- **后端框架**: Hono + Cloudflare Workers
- **数据库**: Supabase (PostgreSQL) 
- **认证系统**: Supabase Auth + JWT
- **短信服务**: 字节跳动火山引擎SMS
- **存储**: Cloudflare KV (验证码缓存)

---

## 2. 技术架构

### 2.1 代码结构
```
apps/backend-service/src/modules/app/auth/
├── types/
│   ├── index.ts                    # 类型导出
│   ├── sms.ts                      # SMS类型定义
│   └── phone-auth.ts               # 手机号认证类型
├── services/
│   ├── phone-auth.ts               # 5个接口业务逻辑
│   ├── user-db.ts                  # 数据库操作
│   └── referral.ts                 # 邀请码服务
├── controller/
│   └── phone-auth.ts               # 5个接口控制器
├── routes/
│   └── phone-auth.ts               # 5个接口路由
└── email/
    └── sms.ts                      # 火山引擎SMS实现
```

### 2.2 接口映射表
| 接口功能 | 路由地址 | 控制器函数 | 服务函数 |
|---------|---------|----------|---------|
| 发送注册验证码 | `POST /send-phone-code` | `handleSendPhoneCode` | `sendPhoneRegistrationCode` |
| 验证码注册 | `POST /verify-phone-code` | `handleVerifyPhoneCode` | `verifyPhoneRegistration` |
| 手机号密码登录 | `POST /login-phone-password` | `handleLoginPhonePassword` | `loginWithPhonePassword` |
| 发送登录验证码 | `POST /send-phone-login-code` | `handleSendPhoneLoginCode` | `sendPhoneLoginCode` |
| 验证码登录 | `POST /login-phone-code` | `handleLoginPhoneCode` | `verifyPhoneLogin` |

---

## 3. 业务逻辑说明

### 3.1 注册流程（强制设置密码）
```mermaid
sequenceDiagram
    participant F as 前端
    participant B as 后端
    participant S as SMS服务
    participant DB as 数据库
    participant Auth as Supabase Auth
    
    F->>B: 1. 发送注册验证码
    B->>DB: 检查手机号是否已注册
    B->>S: 发送短信验证码
    B->>F: 返回发送结果
    
    F->>B: 2. 验证码注册（必传密码）
    B->>B: 验证验证码
    B->>Auth: 创建Supabase用户（临时邮箱+密码）
    B->>DB: 创建数据库用户记录
    B->>DB: 创建用户配置文件
    B->>DB: 初始化积分系统
    B->>Auth: 密码登录获取会话
    B->>F: 返回登录会话
```

### 3.2 登录流程（两种方式）

#### 方式一：手机号密码登录
```mermaid
sequenceDiagram
    participant F as 前端
    participant B as 后端
    participant DB as 数据库
    participant Auth as Supabase Auth
    
    F->>B: 手机号+密码登录
    B->>DB: 检查用户是否存在
    B->>Auth: 使用临时邮箱+密码登录
    B->>F: 返回登录会话
```

#### 方式二：手机号验证码登录
```mermaid
sequenceDiagram
    participant F as 前端
    participant B as 后端
    participant S as SMS服务
    participant DB as 数据库
    participant Auth as Supabase Auth
    
    F->>B: 1. 发送登录验证码
    B->>DB: 检查用户是否存在
    B->>S: 发送短信验证码
    B->>F: 返回发送结果
    
    F->>B: 2. 验证码登录
    B->>B: 验证验证码
    B->>Auth: 生成临时密码并更新
    B->>Auth: 临时密码登录获取会话
    B->>F: 返回登录会话
```

---

## 4. 数据库设计

### 4.1 用户表（User）
```sql
CREATE TABLE User (
    id TEXT PRIMARY KEY,              -- 数据库用户ID
    email TEXT UNIQUE,                -- 临时邮箱（手机号@phone.placeholder.com）
    phone TEXT UNIQUE,                -- 手机号
    supabaseUserId TEXT UNIQUE,       -- Supabase用户ID
    createdAt INTEGER,                -- 创建时间戳
    updatedAt INTEGER                 -- 更新时间戳
);
```

### 4.2 用户配置文件（UserProfile）
```sql
CREATE TABLE UserProfile (
    id TEXT PRIMARY KEY,
    userId TEXT,                      -- 关联User.id
    nickname TEXT,                    -- 用户昵称
    gender TEXT,                      -- 性别：male/female/other
    createdAt INTEGER,
    updatedAt INTEGER
);
```

### 4.3 Supabase Auth用户存储
```typescript
// Supabase Auth用户metadata
{
    email: "<EMAIL>",  // 临时邮箱
    password: "用户设置的密码",                    // 用户密码
    user_metadata: {
        name: "用户昵称",
        hasPassword: true,                        // 标记是否设置密码
        phone: "13812345678"                      // 手机号
    }
}
```

### 4.4 验证码存储（Cloudflare KV）
```typescript
// KV存储格式
key: `phone_code:${phone}`           // 例如: "phone_code:13812345678"
value: {
    code: "123456",                  // 6位验证码
    expiresAt: 1640995200,          // 过期时间戳
    attempts: 0                      // 验证尝试次数
}
ttl: 600                            // 10分钟过期
```

---

## 5. API接口规范（5个接口）

### 5.1 基础信息
- **Base URL**: `https://your-domain.com`
- **API前缀**: `/auth/phone`
- **Content-Type**: `application/json`

### 5.2 通用响应格式
```typescript
interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
}
```

---

### 📱 **接口1: 发送注册验证码**

**接口地址**: `POST /auth/phone/send-phone-code`

**业务逻辑**:
1. 校验手机号格式
2. 检查手机号是否已注册
3. 生成6位数字验证码
4. 调用火山引擎SMS发送短信
5. 验证码存储到KV（10分钟有效期）

**前端传入**:
```typescript
{
  phone: string  // 手机号，格式：13812345678
}
```

**后端接收处理**:
```typescript
// 1. 参数校验
const { phone } = await c.req.json()

// 2. 检查是否已注册
const existingUsers = await getUserByPhone(env, phone)
if (existingUsers?.length > 0) {
  return { success: false, message: '手机号已注册，请直接登录' }
}

// 3. 发送短信
const verificationCode = generateSMSVerificationCode() // 生成6位验证码
const smsResult = await sendRegistrationCodeSMS(env, phone, verificationCode)

// 4. 存储验证码
await storeVerificationCode(env, phone, verificationCode, 10)
```

**数据库存储**: 无（仅KV存储验证码）

**成功响应**:
```json
{
  "success": true,
  "message": "验证码已发送",
  "data": {
    "messageId": "sms_message_id_12345"
  }
}
```

---

### 🔐 **接口2: 验证码注册**

**接口地址**: `POST /auth/phone/verify-phone-code`

**业务逻辑**:
1. 验证存储的验证码
2. 创建Supabase Auth用户（临时邮箱+密码）
3. 创建数据库用户记录
4. 创建用户配置文件
5. 处理邀请码逻辑
6. 初始化积分系统
7. 如果设置密码，直接登录返回会话

**前端传入**:
```typescript
{
  phone: string           // 手机号（必填）
  code: string           // 6位验证码（必填）
  name?: string          // 用户昵称（可选）
  password: string       // 用户密码（必填！前端强制传入）
  inviteCode?: string    // 邀请码（可选）
}
```

**后端接收处理**:
```typescript
// 1. 验证验证码
const codeVerification = await verifyStoredCode(env, phone, code)

// 2. 创建Supabase用户
const tempEmail = `${phone.replace(/\D/g, '')}@phone.placeholder.com`
const { data: authData } = await supabase.auth.admin.createUser({
  email: tempEmail,
  password: password,  // 用户设置的密码
  email_confirm: true,
  user_metadata: {
    name: name || `用户${phone.slice(-4)}`,
    hasPassword: true,  // 标记有密码
    phone: phone
  }
})

// 3. 创建数据库记录
const [dbUser] = await createUserByPhone(env, phone, authData.user.id)
await createUserProfile(env, {
  userId: dbUser.id,
  nickname: name || `用户${phone.slice(-4)}`,
  gender: 'other'
})

// 4. 处理邀请码和积分
// 5. 密码登录获取会话
const { data: signInData } = await supabase.auth.signInWithPassword({
  email: tempEmail,
  password: password
})
```

**数据库存储**:
- **User表**: id, email(临时), phone, supabaseUserId
- **UserProfile表**: userId, nickname, gender
- **UserPoints表**: 初始积分记录
- **Supabase Auth**: 用户认证信息

**成功响应**:
```json
{
  "success": true,
  "message": "注册成功",
  "data": {
    "session": {
      "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refresh_token": "v1.MR5tOCd7ZKKhOurkVjQSWnbCCd8-yZhZ...",
      "expires_at": 1640995200,
      "user": {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "phone": "13812345678",
        "phoneConfirmed": true,
        "dbUserId": "db_user_id_12345"
      }
    }
  }
}
```

---

### 🔑 **接口3: 手机号密码登录**

**接口地址**: `POST /auth/phone/login-phone-password`

**业务逻辑**:
1. 检查用户是否存在
2. 获取用户的Supabase信息
3. 使用临时邮箱+密码进行Supabase登录
4. 返回登录会话

**前端传入**:
```typescript
{
  phone: string     // 手机号（必填）
  password: string  // 用户密码（必填）
}
```

**后端接收处理**:
```typescript
// 1. 检查用户是否存在
const existingUsers = await getUserByPhone(env, phone)
if (!existingUsers?.length) {
  return { success: false, message: '用户不存在，请先注册' }
}

const dbUser = existingUsers[0]

// 2. 获取Supabase用户
const supabase = createSupabaseServiceClient(env)
const { data: users } = await supabase.auth.admin.listUsers()
const authUser = users?.users?.find(u => u.id === dbUser.supabaseUserId)

// 3. 密码登录
const tempEmail = `${phone.replace(/\D/g, '')}@phone.placeholder.com`
const { data: loginData } = await supabase.auth.signInWithPassword({
  email: tempEmail,
  password: password
})
```

**数据库存储**: 无（仅查询验证）

**成功响应**:
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "session": {
      "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refresh_token": "v1.MR5tOCd7ZKKhOurkVjQSWnbCCd8-yZhZ...",
      "expires_at": 1640995200,
      "user": {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "phone": "13812345678",
        "phoneConfirmed": true,
        "dbUserId": "db_user_id_12345"
      }
    }
  }
}
```

---

### 📨 **接口4: 发送登录验证码**

**接口地址**: `POST /auth/phone/send-phone-login-code`

**业务逻辑**:
1. 检查用户是否存在
2. 生成6位数字验证码
3. 调用火山引擎SMS发送短信
4. 验证码存储到KV（10分钟有效期）

**前端传入**:
```typescript
{
  phone: string  // 手机号
}
```

**后端接收处理**:
```typescript
// 1. 检查用户是否存在
const existingUsers = await getUserByPhone(env, phone)
if (!existingUsers?.length) {
  return { success: false, message: '用户不存在，请先注册' }
}

// 2. 生成和发送验证码
const verificationCode = generateSMSVerificationCode()
const smsResult = await sendLoginCodeSMS(env, phone, verificationCode)

// 3. 存储验证码
await storeVerificationCode(env, phone, verificationCode, 10)
```

**数据库存储**: 无（仅KV存储验证码）

**成功响应**:
```json
{
  "success": true,
  "message": "登录验证码已发送",
  "data": {
    "messageId": "sms_message_id_67890"
  }
}
```

---

### 🔓 **接口5: 验证码登录**

**接口地址**: `POST /auth/phone/login-phone-code`

**业务逻辑**:
1. 验证存储的验证码
2. 检查用户是否存在
3. 获取用户的Supabase信息
4. 生成临时密码并更新到Supabase
5. 使用临时密码登录获取会话

**前端传入**:
```typescript
{
  phone: string  // 手机号（必填）
  code: string   // 6位验证码（必填）
}
```

**后端接收处理**:
```typescript
// 1. 验证验证码
const codeVerification = await verifyStoredCode(env, phone, code)

// 2. 检查用户存在
const existingUsers = await getUserByPhone(env, phone)
const dbUser = existingUsers[0]

// 3. 获取Supabase用户
const authUser = users?.users?.find(u => u.id === dbUser.supabaseUserId)

// 4. 生成临时密码并登录
const tempPassword = 'TempPass_' + Math.random().toString(36).substring(2, 15)
await supabase.auth.admin.updateUserById(authUser.id, { password: tempPassword })

const tempEmail = `${phone.replace(/\D/g, '')}@phone.placeholder.com`
const { data: loginData } = await supabase.auth.signInWithPassword({
  email: tempEmail,
  password: tempPassword
})
```

**数据库存储**: 无（仅更新Supabase密码）

**成功响应**:
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "session": {
      "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refresh_token": "v1.MR5tOCd7ZKKhOurkVjQSWnbCCd8-yZhZ...",
      "expires_at": 1640995200,
      "user": {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "phone": "13812345678",
        "phoneConfirmed": true,
        "dbUserId": "db_user_id_12345"
      }
    }
  }
}
```

---

## 6. 前端集成指南

### 6.1 TypeScript类型定义
```typescript
// types/phone-auth.ts
export interface PhoneAuthAPI {
  sendCode: (phone: string) => Promise<ApiResponse>
  register: (params: RegisterParams) => Promise<ApiResponse<SessionData>>
  loginWithPassword: (phone: string, password: string) => Promise<ApiResponse<SessionData>>
  sendLoginCode: (phone: string) => Promise<ApiResponse>
  loginWithCode: (phone: string, code: string) => Promise<ApiResponse<SessionData>>
}

export interface RegisterParams {
  phone: string
  code: string
  name?: string
  password: string        // 必填！
  inviteCode?: string
}

export interface LoginPasswordParams {
  phone: string
  password: string
}

export interface LoginCodeParams {
  phone: string
  code: string
}
```

### 6.2 API服务封装
```typescript
// services/phoneAuth.ts
class PhoneAuthService {
  private baseURL = '/auth/phone'

  // 1. 发送注册验证码
  async sendCode(phone: string): Promise<ApiResponse> {
    const response = await fetch(`${this.baseURL}/send-phone-code`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ phone })
    })
    return response.json()
  }

  // 2. 验证码注册
  async register(params: RegisterParams): Promise<ApiResponse<SessionData>> {
    const response = await fetch(`${this.baseURL}/verify-phone-code`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(params)
    })
    return response.json()
  }

  // 3. 手机号密码登录
  async loginWithPassword(phone: string, password: string): Promise<ApiResponse<SessionData>> {
    const response = await fetch(`${this.baseURL}/login-phone-password`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ phone, password })
    })
    return response.json()
  }

  // 4. 发送登录验证码
  async sendLoginCode(phone: string): Promise<ApiResponse> {
    const response = await fetch(`${this.baseURL}/send-phone-login-code`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ phone })
    })
    return response.json()
  }

  // 5. 验证码登录
  async loginWithCode(phone: string, code: string): Promise<ApiResponse<SessionData>> {
    const response = await fetch(`${this.baseURL}/login-phone-code`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ phone, code })
    })
    return response.json()
  }
}

export const phoneAuthService = new PhoneAuthService()
```

### 6.3 React使用示例
```tsx
// components/PhoneAuth.tsx
import React, { useState } from 'react'
import { phoneAuthService } from '../services/phoneAuth'

export function PhoneAuth() {
  const [phone, setPhone] = useState('')
  const [code, setCode] = useState('')
  const [password, setPassword] = useState('')
  const [name, setName] = useState('')
  const [mode, setMode] = useState<'register' | 'loginPassword' | 'loginCode'>('register')

  // 注册流程
  const handleRegister = async () => {
    // 1. 发送验证码
    const sendResult = await phoneAuthService.sendCode(phone)
    if (!sendResult.success) return alert(sendResult.message)
    
    // 2. 等用户输入验证码后注册
    const registerResult = await phoneAuthService.register({
      phone,
      code,
      password,  // 必填
      name
    })
    
    if (registerResult.success) {
      // 注册成功，保存会话
      localStorage.setItem('access_token', registerResult.data.session.access_token)
    }
  }

  // 密码登录
  const handlePasswordLogin = async () => {
    const result = await phoneAuthService.loginWithPassword(phone, password)
    if (result.success) {
      localStorage.setItem('access_token', result.data.session.access_token)
    }
  }

  // 验证码登录
  const handleCodeLogin = async () => {
    // 1. 发送登录验证码
    const sendResult = await phoneAuthService.sendLoginCode(phone)
    if (!sendResult.success) return alert(sendResult.message)
    
    // 2. 验证码登录
    const loginResult = await phoneAuthService.loginWithCode(phone, code)
    if (loginResult.success) {
      localStorage.setItem('access_token', loginResult.data.session.access_token)
    }
  }

  return (
    <div>
      <div>
        <button onClick={() => setMode('register')}>注册</button>
        <button onClick={() => setMode('loginPassword')}>密码登录</button>
        <button onClick={() => setMode('loginCode')}>验证码登录</button>
      </div>
      
      <input 
        placeholder="手机号" 
        value={phone} 
        onChange={e => setPhone(e.target.value)} 
      />
      
      {mode === 'register' && (
        <>
          <input placeholder="验证码" value={code} onChange={e => setCode(e.target.value)} />
          <input placeholder="密码" type="password" value={password} onChange={e => setPassword(e.target.value)} />
          <input placeholder="昵称" value={name} onChange={e => setName(e.target.value)} />
          <button onClick={handleRegister}>注册</button>
        </>
      )}
      
      {mode === 'loginPassword' && (
        <>
          <input placeholder="密码" type="password" value={password} onChange={e => setPassword(e.target.value)} />
          <button onClick={handlePasswordLogin}>密码登录</button>
        </>
      )}
      
      {mode === 'loginCode' && (
        <>
          <input placeholder="验证码" value={code} onChange={e => setCode(e.target.value)} />
          <button onClick={handleCodeLogin}>验证码登录</button>
        </>
      )}
    </div>
  )
}
```

---

## 7. 错误处理

### 7.1 常见错误码
```typescript
enum PhoneAuthError {
  PHONE_ALREADY_REGISTERED = '手机号已注册，请直接登录',
  USER_NOT_FOUND = '用户不存在，请先注册',
  INVALID_CODE = '验证码错误',
  CODE_EXPIRED = '验证码已过期',
  SMS_SEND_FAILED = '短信发送失败，请稍后重试',
  WRONG_PASSWORD = '手机号或密码错误',
  SYSTEM_ERROR = '系统错误，请重试'
}
```

### 7.2 前端错误处理
```typescript
export function handlePhoneAuthError(error: ApiResponse) {
  const errorMap = {
    '手机号已注册，请直接登录': '该手机号已被注册，请使用登录功能',
    '用户不存在，请先注册': '该手机号未注册，请先注册',
    '验证码错误': '验证码输入错误，请重新输入',
    '手机号或密码错误': '登录失败，请检查手机号和密码',
  }
  
  return errorMap[error.message] || error.message || '操作失败，请重试'
}
```

---

## 8. 安全考虑

### 8.1 验证码安全
- **有效期**: 10分钟过期
- **一次性**: 使用后立即删除
- **频率限制**: 同一手机号1分钟内只能发送1次
- **IP限制**: 同一IP每小时最多20次

### 8.2 密码安全
- **强制设置**: 注册时必须设置密码
- **复杂度要求**: 建议前端校验密码强度
- **安全存储**: Supabase自动加密存储

### 8.3 会话安全
- **JWT过期**: Access Token 1小时过期
- **自动刷新**: 使用Refresh Token刷新
- **安全存储**: 建议HttpOnly Cookie

### 8.4 前端安全工具
```typescript
// 手机号脱敏
export function maskPhone(phone: string): string {
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

// 验证码格式校验
export function validateCode(code: string): boolean {
  return /^\d{6}$/.test(code)
}

// 手机号格式校验
export function validatePhone(phone: string): boolean {
  return /^1[3-9]\d{9}$/.test(phone)
}

// 密码强度校验
export function validatePassword(password: string): boolean {
  return password.length >= 6
}
```

---

## 📊 总结

### ✅ 完整的5个接口
1. **发送注册验证码** - 注册前验证手机号
2. **验证码注册** - 验证码+密码注册（强制设密码）
3. **手机号密码登录** - 快速登录方式
4. **发送登录验证码** - 忘记密码时使用
5. **验证码登录** - 验证码登录方式

### 🔒 业务逻辑特点
- **注册强制密码**: 对标邮箱注册，必须设置密码
- **双登录方式**: 密码登录 + 验证码登录
- **完整会话管理**: JWT + Refresh Token
- **数据安全**: Supabase Auth + 加密存储

### 🚀 技术亮点
- **分层架构**: Routes → Controller → Services → Database
- **类型安全**: 完整的TypeScript类型定义
- **错误处理**: 统一的错误响应格式
- **安全防护**: 验证码防刷、密码加密、会话管理

**版本**: v2.0.0  
**更新时间**: 2024年1月  
**维护团队**: PleasureHub开发团队 